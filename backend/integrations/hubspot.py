# hubspot.py

import datetime
import json
import secrets
from fastapi import Request, HTTPException
from fastapi.responses import HTMLResponse
import httpx
import asyncio
import base64

import requests
from integrations.integration_item import IntegrationItem

from redis_client import add_key_value_redis, get_value_redis, delete_key_redis

# HubSpot OAuth Configuration
# Note: Replace these with your actual HubSpot app credentials
CLIENT_ID = '************************************'
CLIENT_SECRET = 'c0e45c9b-9736-41c3-8dd9-cf91b6cb05f6'
REDIRECT_URI = 'http://localhost:8000/integrations/hubspot/oauth2callback'

# HubSpot OAuth URLs
AUTHORIZATION_URL = 'https://app.hubspot.com/oauth/authorize'
TOKEN_URL = 'https://api.hubapi.com/oauth/v1/token'

# Required scopes for HubSpot integration
SCOPES = 'crm.objects.contacts.read crm.objects.companies.read crm.objects.deals.read'

async def authorize_hubspot(user_id, org_id):
    """Initialize OAuth authorization flow for HubSpot"""
    state_data = {
        'state': secrets.token_urlsafe(32),
        'user_id': user_id,
        'org_id': org_id
    }
    encoded_state = base64.urlsafe_b64encode(json.dumps(state_data).encode('utf-8')).decode('utf-8')

    # Store state in Redis for verification
    await add_key_value_redis(f'hubspot_state:{org_id}:{user_id}', json.dumps(state_data), expire=600)

    # Build authorization URL
    auth_url = f'{AUTHORIZATION_URL}?client_id={CLIENT_ID}&redirect_uri={REDIRECT_URI}&scope={SCOPES}&state={encoded_state}'

    return auth_url

async def oauth2callback_hubspot(request: Request):
    """Handle OAuth callback from HubSpot"""
    if request.query_params.get('error'):
        raise HTTPException(status_code=400, detail=request.query_params.get('error_description'))

    code = request.query_params.get('code')
    encoded_state = request.query_params.get('state')

    if not code or not encoded_state:
        raise HTTPException(status_code=400, detail='Missing authorization code or state')

    # Decode and verify state
    try:
        state_data = json.loads(base64.urlsafe_b64decode(encoded_state).decode('utf-8'))
    except Exception:
        raise HTTPException(status_code=400, detail='Invalid state parameter')

    original_state = state_data.get('state')
    user_id = state_data.get('user_id')
    org_id = state_data.get('org_id')

    # Verify state matches what we stored
    saved_state = await get_value_redis(f'hubspot_state:{org_id}:{user_id}')
    if not saved_state or original_state != json.loads(saved_state).get('state'):
        raise HTTPException(status_code=400, detail='State does not match.')

    # Exchange authorization code for access token
    async with httpx.AsyncClient() as client:
        response, _ = await asyncio.gather(
            client.post(
                TOKEN_URL,
                data={
                    'grant_type': 'authorization_code',
                    'client_id': CLIENT_ID,
                    'client_secret': CLIENT_SECRET,
                    'redirect_uri': REDIRECT_URI,
                    'code': code,
                },
                headers={
                    'Content-Type': 'application/x-www-form-urlencoded',
                }
            ),
            delete_key_redis(f'hubspot_state:{org_id}:{user_id}'),
        )

    if response.status_code != 200:
        raise HTTPException(status_code=400, detail='Failed to exchange authorization code for token')

    # Store credentials in Redis
    await add_key_value_redis(f'hubspot_credentials:{org_id}:{user_id}', json.dumps(response.json()), expire=600)

    # Return HTML to close the popup window
    close_window_script = """
    <html>
        <script>
            window.close();
        </script>
    </html>
    """
    return HTMLResponse(content=close_window_script)

async def get_hubspot_credentials(user_id, org_id):
    """Retrieve stored HubSpot credentials"""
    credentials = await get_value_redis(f'hubspot_credentials:{org_id}:{user_id}')
    if not credentials:
        raise HTTPException(status_code=400, detail='No credentials found.')

    credentials = json.loads(credentials)
    await delete_key_redis(f'hubspot_credentials:{org_id}:{user_id}')

    return credentials

def create_integration_item_metadata_object(
    response_json: dict, item_type: str, parent_id=None, parent_name=None
) -> IntegrationItem:
    """Create an IntegrationItem object from HubSpot API response"""

    # Extract common properties
    item_id = response_json.get('id', None)
    properties = response_json.get('properties', {})

    # Determine name based on object type
    if item_type == 'Contact':
        first_name = properties.get('firstname', '')
        last_name = properties.get('lastname', '')
        name = f"{first_name} {last_name}".strip() or properties.get('email', f'Contact {item_id}')
    elif item_type == 'Company':
        name = properties.get('name', f'Company {item_id}')
    elif item_type == 'Deal':
        name = properties.get('dealname', f'Deal {item_id}')
    else:
        name = f'{item_type} {item_id}'

    # Extract timestamps
    created_at = properties.get('createdate')
    modified_at = properties.get('hs_lastmodifieddate')

    creation_time = None
    last_modified_time = None

    if created_at:
        try:
            creation_time = datetime.datetime.fromisoformat(created_at.replace('Z', '+00:00'))
        except:
            pass

    if modified_at:
        try:
            last_modified_time = datetime.datetime.fromisoformat(modified_at.replace('Z', '+00:00'))
        except:
            pass

    integration_item_metadata = IntegrationItem(
        id=f'{item_id}_{item_type}',
        name=name,
        type=item_type,
        parent_id=parent_id,
        parent_path_or_name=parent_name,
        creation_time=creation_time,
        last_modified_time=last_modified_time,
    )

    return integration_item_metadata

def fetch_hubspot_objects(access_token: str, object_type: str, limit: int = 100) -> list:
    """Fetch objects from HubSpot CRM API"""
    url = f'https://api.hubapi.com/crm/v3/objects/{object_type}'
    headers = {'Authorization': f'Bearer {access_token}'}
    params = {'limit': limit}

    all_objects = []

    while url:
        response = requests.get(url, headers=headers, params=params)

        if response.status_code != 200:
            print(f'Error fetching {object_type}: {response.status_code} - {response.text}')
            break

        data = response.json()
        results = data.get('results', [])
        all_objects.extend(results)

        # Check for pagination
        paging = data.get('paging', {})
        next_link = paging.get('next', {}).get('link')

        if next_link:
            url = next_link
            params = {}  # Next link already includes parameters
        else:
            url = None

    return all_objects

async def get_items_hubspot(credentials) -> list[IntegrationItem]:
    """Fetch and return HubSpot items as IntegrationItem objects"""
    credentials = json.loads(credentials) if isinstance(credentials, str) else credentials
    access_token = credentials.get('access_token')

    if not access_token:
        raise HTTPException(status_code=400, detail='No access token found in credentials')

    list_of_integration_item_metadata = []

    # Define the object types we want to fetch
    object_types = [
        ('contacts', 'Contact'),
        ('companies', 'Company'),
        ('deals', 'Deal')
    ]

    for endpoint, item_type in object_types:
        try:
            objects = fetch_hubspot_objects(access_token, endpoint)

            for obj in objects:
                integration_item = create_integration_item_metadata_object(obj, item_type)
                list_of_integration_item_metadata.append(integration_item)

        except Exception as e:
            print(f'Error fetching {item_type} objects: {str(e)}')
            continue

    print(f'list_of_integration_item_metadata: {list_of_integration_item_metadata}')
    return list_of_integration_item_metadata