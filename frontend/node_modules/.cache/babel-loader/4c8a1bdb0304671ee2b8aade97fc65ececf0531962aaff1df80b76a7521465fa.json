{"ast": null, "code": "import { unstable_generateUtilityClasses as generateUtilityClasses } from '@mui/utils';\nimport generateUtilityClass from '../generateUtilityClass';\nexport function getToggleButtonGroupUtilityClass(slot) {\n  return generateUtilityClass('MuiToggleButtonGroup', slot);\n}\nconst toggleButtonGroupClasses = generateUtilityClasses('MuiToggleButtonGroup', ['root', 'selected', 'vertical', 'disabled', 'grouped', 'groupedHorizontal', 'groupedVertical', 'fullWidth']);\nexport default toggleButtonGroupClasses;", "map": {"version": 3, "names": ["unstable_generateUtilityClasses", "generateUtilityClasses", "generateUtilityClass", "getToggleButtonGroupUtilityClass", "slot", "toggleButtonGroupClasses"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/material/ToggleButtonGroup/toggleButtonGroupClasses.js"], "sourcesContent": ["import { unstable_generateUtilityClasses as generateUtilityClasses } from '@mui/utils';\nimport generateUtilityClass from '../generateUtilityClass';\nexport function getToggleButtonGroupUtilityClass(slot) {\n  return generateUtilityClass('MuiToggleButtonGroup', slot);\n}\nconst toggleButtonGroupClasses = generateUtilityClasses('MuiToggleButtonGroup', ['root', 'selected', 'vertical', 'disabled', 'grouped', 'groupedHorizontal', 'groupedVertical', 'fullWidth']);\nexport default toggleButtonGroupClasses;"], "mappings": "AAAA,SAASA,+BAA+B,IAAIC,sBAAsB,QAAQ,YAAY;AACtF,OAAOC,oBAAoB,MAAM,yBAAyB;AAC1D,OAAO,SAASC,gCAAgCA,CAACC,IAAI,EAAE;EACrD,OAAOF,oBAAoB,CAAC,sBAAsB,EAAEE,IAAI,CAAC;AAC3D;AACA,MAAMC,wBAAwB,GAAGJ,sBAAsB,CAAC,sBAAsB,EAAE,CAAC,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,mBAAmB,EAAE,iBAAiB,EAAE,WAAW,CAAC,CAAC;AAC7L,eAAeI,wBAAwB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}