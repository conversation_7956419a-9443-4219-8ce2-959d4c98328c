{"ast": null, "code": "import { generateUtilityClasses } from '../generateUtilityClasses';\nimport { generateUtilityClass } from '../generateUtilityClass';\nexport function getBadgeUtilityClass(slot) {\n  return generateUtilityClass('MuiBadge', slot);\n}\nexport const badgeClasses = generateUtilityClasses('MuiBadge', ['root', 'badge', 'invisible']);", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getBadgeUtilityClass", "slot", "badgeClasses"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/base/Badge/badgeClasses.js"], "sourcesContent": ["import { generateUtilityClasses } from '../generateUtilityClasses';\nimport { generateUtilityClass } from '../generateUtilityClass';\nexport function getBadgeUtilityClass(slot) {\n  return generateUtilityClass('MuiBadge', slot);\n}\nexport const badgeClasses = generateUtilityClasses('MuiBadge', ['root', 'badge', 'invisible']);"], "mappings": "AAAA,SAASA,sBAAsB,QAAQ,2BAA2B;AAClE,SAASC,oBAAoB,QAAQ,yBAAyB;AAC9D,OAAO,SAASC,oBAAoBA,CAACC,IAAI,EAAE;EACzC,OAAOF,oBAAoB,CAAC,UAAU,EAAEE,IAAI,CAAC;AAC/C;AACA,OAAO,MAAMC,YAAY,GAAGJ,sBAAsB,CAAC,UAAU,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}