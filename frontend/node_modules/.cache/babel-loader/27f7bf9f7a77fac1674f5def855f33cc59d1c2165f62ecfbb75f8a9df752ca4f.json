{"ast": null, "code": "'use client';\n\nexport { useModal as unstable_useModal } from './useModal';\nexport * from './useModal.types';\nexport * from './ModalManager';", "map": {"version": 3, "names": ["useModal", "unstable_useModal"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/base/unstable_useModal/index.js"], "sourcesContent": ["'use client';\n\nexport { useModal as unstable_useModal } from './useModal';\nexport * from './useModal.types';\nexport * from './ModalManager';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,QAAQ,IAAIC,iBAAiB,QAAQ,YAAY;AAC1D,cAAc,kBAAkB;AAChC,cAAc,gBAAgB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}