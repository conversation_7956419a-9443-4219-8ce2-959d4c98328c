{"ast": null, "code": "import { rectToClientRect, computePosition as computePosition$1 } from '@floating-ui/core';\nexport { arrow, autoPlacement, detectOverflow, flip, hide, inline, limitShift, offset, shift, size } from '@floating-ui/core';\nimport { round, createCoords, max, min, floor } from '@floating-ui/utils';\nimport { getComputedStyle, isHTMLElement, isElement, getWindow, isWebKit, getDocumentElement, getNodeName, isOverflowElement, getNodeScroll, getOverflowAncestors, getParentNode, isLastTraversableNode, isContainingBlock, isTableElement, getContainingBlock } from '@floating-ui/utils/dom';\nexport { getOverflowAncestors } from '@floating-ui/utils/dom';\nfunction getCssDimensions(element) {\n  const css = getComputedStyle(element);\n  // In testing environments, the `width` and `height` properties are empty\n  // strings for SVG elements, returning NaN. Fallback to `0` in this case.\n  let width = parseFloat(css.width) || 0;\n  let height = parseFloat(css.height) || 0;\n  const hasOffset = isHTMLElement(element);\n  const offsetWidth = hasOffset ? element.offsetWidth : width;\n  const offsetHeight = hasOffset ? element.offsetHeight : height;\n  const shouldFallback = round(width) !== offsetWidth || round(height) !== offsetHeight;\n  if (shouldFallback) {\n    width = offsetWidth;\n    height = offsetHeight;\n  }\n  return {\n    width,\n    height,\n    $: shouldFallback\n  };\n}\nfunction unwrapElement(element) {\n  return !isElement(element) ? element.contextElement : element;\n}\nfunction getScale(element) {\n  const domElement = unwrapElement(element);\n  if (!isHTMLElement(domElement)) {\n    return createCoords(1);\n  }\n  const rect = domElement.getBoundingClientRect();\n  const {\n    width,\n    height,\n    $\n  } = getCssDimensions(domElement);\n  let x = ($ ? round(rect.width) : rect.width) / width;\n  let y = ($ ? round(rect.height) : rect.height) / height;\n\n  // 0, NaN, or Infinity should always fallback to 1.\n\n  if (!x || !Number.isFinite(x)) {\n    x = 1;\n  }\n  if (!y || !Number.isFinite(y)) {\n    y = 1;\n  }\n  return {\n    x,\n    y\n  };\n}\nconst noOffsets = /*#__PURE__*/createCoords(0);\nfunction getVisualOffsets(element) {\n  const win = getWindow(element);\n  if (!isWebKit() || !win.visualViewport) {\n    return noOffsets;\n  }\n  return {\n    x: win.visualViewport.offsetLeft,\n    y: win.visualViewport.offsetTop\n  };\n}\nfunction shouldAddVisualOffsets(element, isFixed, floatingOffsetParent) {\n  if (isFixed === void 0) {\n    isFixed = false;\n  }\n  if (!floatingOffsetParent || isFixed && floatingOffsetParent !== getWindow(element)) {\n    return false;\n  }\n  return isFixed;\n}\nfunction getBoundingClientRect(element, includeScale, isFixedStrategy, offsetParent) {\n  if (includeScale === void 0) {\n    includeScale = false;\n  }\n  if (isFixedStrategy === void 0) {\n    isFixedStrategy = false;\n  }\n  const clientRect = element.getBoundingClientRect();\n  const domElement = unwrapElement(element);\n  let scale = createCoords(1);\n  if (includeScale) {\n    if (offsetParent) {\n      if (isElement(offsetParent)) {\n        scale = getScale(offsetParent);\n      }\n    } else {\n      scale = getScale(element);\n    }\n  }\n  const visualOffsets = shouldAddVisualOffsets(domElement, isFixedStrategy, offsetParent) ? getVisualOffsets(domElement) : createCoords(0);\n  let x = (clientRect.left + visualOffsets.x) / scale.x;\n  let y = (clientRect.top + visualOffsets.y) / scale.y;\n  let width = clientRect.width / scale.x;\n  let height = clientRect.height / scale.y;\n  if (domElement) {\n    const win = getWindow(domElement);\n    const offsetWin = offsetParent && isElement(offsetParent) ? getWindow(offsetParent) : offsetParent;\n    let currentIFrame = win.frameElement;\n    while (currentIFrame && offsetParent && offsetWin !== win) {\n      const iframeScale = getScale(currentIFrame);\n      const iframeRect = currentIFrame.getBoundingClientRect();\n      const css = getComputedStyle(currentIFrame);\n      const left = iframeRect.left + (currentIFrame.clientLeft + parseFloat(css.paddingLeft)) * iframeScale.x;\n      const top = iframeRect.top + (currentIFrame.clientTop + parseFloat(css.paddingTop)) * iframeScale.y;\n      x *= iframeScale.x;\n      y *= iframeScale.y;\n      width *= iframeScale.x;\n      height *= iframeScale.y;\n      x += left;\n      y += top;\n      currentIFrame = getWindow(currentIFrame).frameElement;\n    }\n  }\n  return rectToClientRect({\n    width,\n    height,\n    x,\n    y\n  });\n}\nfunction convertOffsetParentRelativeRectToViewportRelativeRect(_ref) {\n  let {\n    rect,\n    offsetParent,\n    strategy\n  } = _ref;\n  const isOffsetParentAnElement = isHTMLElement(offsetParent);\n  const documentElement = getDocumentElement(offsetParent);\n  if (offsetParent === documentElement) {\n    return rect;\n  }\n  let scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  let scale = createCoords(1);\n  const offsets = createCoords(0);\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && strategy !== 'fixed') {\n    if (getNodeName(offsetParent) !== 'body' || isOverflowElement(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n    if (isHTMLElement(offsetParent)) {\n      const offsetRect = getBoundingClientRect(offsetParent);\n      scale = getScale(offsetParent);\n      offsets.x = offsetRect.x + offsetParent.clientLeft;\n      offsets.y = offsetRect.y + offsetParent.clientTop;\n    }\n  }\n  return {\n    width: rect.width * scale.x,\n    height: rect.height * scale.y,\n    x: rect.x * scale.x - scroll.scrollLeft * scale.x + offsets.x,\n    y: rect.y * scale.y - scroll.scrollTop * scale.y + offsets.y\n  };\n}\nfunction getClientRects(element) {\n  return Array.from(element.getClientRects());\n}\nfunction getWindowScrollBarX(element) {\n  // If <html> has a CSS width greater than the viewport, then this will be\n  // incorrect for RTL.\n  return getBoundingClientRect(getDocumentElement(element)).left + getNodeScroll(element).scrollLeft;\n}\n\n// Gets the entire size of the scrollable document area, even extending outside\n// of the `<html>` and `<body>` rect bounds if horizontally scrollable.\nfunction getDocumentRect(element) {\n  const html = getDocumentElement(element);\n  const scroll = getNodeScroll(element);\n  const body = element.ownerDocument.body;\n  const width = max(html.scrollWidth, html.clientWidth, body.scrollWidth, body.clientWidth);\n  const height = max(html.scrollHeight, html.clientHeight, body.scrollHeight, body.clientHeight);\n  let x = -scroll.scrollLeft + getWindowScrollBarX(element);\n  const y = -scroll.scrollTop;\n  if (getComputedStyle(body).direction === 'rtl') {\n    x += max(html.clientWidth, body.clientWidth) - width;\n  }\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\nfunction getViewportRect(element, strategy) {\n  const win = getWindow(element);\n  const html = getDocumentElement(element);\n  const visualViewport = win.visualViewport;\n  let width = html.clientWidth;\n  let height = html.clientHeight;\n  let x = 0;\n  let y = 0;\n  if (visualViewport) {\n    width = visualViewport.width;\n    height = visualViewport.height;\n    const visualViewportBased = isWebKit();\n    if (!visualViewportBased || visualViewportBased && strategy === 'fixed') {\n      x = visualViewport.offsetLeft;\n      y = visualViewport.offsetTop;\n    }\n  }\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\n\n// Returns the inner client rect, subtracting scrollbars if present.\nfunction getInnerBoundingClientRect(element, strategy) {\n  const clientRect = getBoundingClientRect(element, true, strategy === 'fixed');\n  const top = clientRect.top + element.clientTop;\n  const left = clientRect.left + element.clientLeft;\n  const scale = isHTMLElement(element) ? getScale(element) : createCoords(1);\n  const width = element.clientWidth * scale.x;\n  const height = element.clientHeight * scale.y;\n  const x = left * scale.x;\n  const y = top * scale.y;\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\nfunction getClientRectFromClippingAncestor(element, clippingAncestor, strategy) {\n  let rect;\n  if (clippingAncestor === 'viewport') {\n    rect = getViewportRect(element, strategy);\n  } else if (clippingAncestor === 'document') {\n    rect = getDocumentRect(getDocumentElement(element));\n  } else if (isElement(clippingAncestor)) {\n    rect = getInnerBoundingClientRect(clippingAncestor, strategy);\n  } else {\n    const visualOffsets = getVisualOffsets(element);\n    rect = {\n      ...clippingAncestor,\n      x: clippingAncestor.x - visualOffsets.x,\n      y: clippingAncestor.y - visualOffsets.y\n    };\n  }\n  return rectToClientRect(rect);\n}\nfunction hasFixedPositionAncestor(element, stopNode) {\n  const parentNode = getParentNode(element);\n  if (parentNode === stopNode || !isElement(parentNode) || isLastTraversableNode(parentNode)) {\n    return false;\n  }\n  return getComputedStyle(parentNode).position === 'fixed' || hasFixedPositionAncestor(parentNode, stopNode);\n}\n\n// A \"clipping ancestor\" is an `overflow` element with the characteristic of\n// clipping (or hiding) child elements. This returns all clipping ancestors\n// of the given element up the tree.\nfunction getClippingElementAncestors(element, cache) {\n  const cachedResult = cache.get(element);\n  if (cachedResult) {\n    return cachedResult;\n  }\n  let result = getOverflowAncestors(element, [], false).filter(el => isElement(el) && getNodeName(el) !== 'body');\n  let currentContainingBlockComputedStyle = null;\n  const elementIsFixed = getComputedStyle(element).position === 'fixed';\n  let currentNode = elementIsFixed ? getParentNode(element) : element;\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n  while (isElement(currentNode) && !isLastTraversableNode(currentNode)) {\n    const computedStyle = getComputedStyle(currentNode);\n    const currentNodeIsContaining = isContainingBlock(currentNode);\n    if (!currentNodeIsContaining && computedStyle.position === 'fixed') {\n      currentContainingBlockComputedStyle = null;\n    }\n    const shouldDropCurrentNode = elementIsFixed ? !currentNodeIsContaining && !currentContainingBlockComputedStyle : !currentNodeIsContaining && computedStyle.position === 'static' && !!currentContainingBlockComputedStyle && ['absolute', 'fixed'].includes(currentContainingBlockComputedStyle.position) || isOverflowElement(currentNode) && !currentNodeIsContaining && hasFixedPositionAncestor(element, currentNode);\n    if (shouldDropCurrentNode) {\n      // Drop non-containing blocks.\n      result = result.filter(ancestor => ancestor !== currentNode);\n    } else {\n      // Record last containing block for next iteration.\n      currentContainingBlockComputedStyle = computedStyle;\n    }\n    currentNode = getParentNode(currentNode);\n  }\n  cache.set(element, result);\n  return result;\n}\n\n// Gets the maximum area that the element is visible in due to any number of\n// clipping ancestors.\nfunction getClippingRect(_ref) {\n  let {\n    element,\n    boundary,\n    rootBoundary,\n    strategy\n  } = _ref;\n  const elementClippingAncestors = boundary === 'clippingAncestors' ? getClippingElementAncestors(element, this._c) : [].concat(boundary);\n  const clippingAncestors = [...elementClippingAncestors, rootBoundary];\n  const firstClippingAncestor = clippingAncestors[0];\n  const clippingRect = clippingAncestors.reduce((accRect, clippingAncestor) => {\n    const rect = getClientRectFromClippingAncestor(element, clippingAncestor, strategy);\n    accRect.top = max(rect.top, accRect.top);\n    accRect.right = min(rect.right, accRect.right);\n    accRect.bottom = min(rect.bottom, accRect.bottom);\n    accRect.left = max(rect.left, accRect.left);\n    return accRect;\n  }, getClientRectFromClippingAncestor(element, firstClippingAncestor, strategy));\n  return {\n    width: clippingRect.right - clippingRect.left,\n    height: clippingRect.bottom - clippingRect.top,\n    x: clippingRect.left,\n    y: clippingRect.top\n  };\n}\nfunction getDimensions(element) {\n  return getCssDimensions(element);\n}\nfunction getRectRelativeToOffsetParent(element, offsetParent, strategy) {\n  const isOffsetParentAnElement = isHTMLElement(offsetParent);\n  const documentElement = getDocumentElement(offsetParent);\n  const isFixed = strategy === 'fixed';\n  const rect = getBoundingClientRect(element, true, isFixed, offsetParent);\n  let scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  const offsets = createCoords(0);\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== 'body' || isOverflowElement(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n    if (isOffsetParentAnElement) {\n      const offsetRect = getBoundingClientRect(offsetParent, true, isFixed, offsetParent);\n      offsets.x = offsetRect.x + offsetParent.clientLeft;\n      offsets.y = offsetRect.y + offsetParent.clientTop;\n    } else if (documentElement) {\n      offsets.x = getWindowScrollBarX(documentElement);\n    }\n  }\n  return {\n    x: rect.left + scroll.scrollLeft - offsets.x,\n    y: rect.top + scroll.scrollTop - offsets.y,\n    width: rect.width,\n    height: rect.height\n  };\n}\nfunction getTrueOffsetParent(element, polyfill) {\n  if (!isHTMLElement(element) || getComputedStyle(element).position === 'fixed') {\n    return null;\n  }\n  if (polyfill) {\n    return polyfill(element);\n  }\n  return element.offsetParent;\n}\n\n// Gets the closest ancestor positioned element. Handles some edge cases,\n// such as table ancestors and cross browser bugs.\nfunction getOffsetParent(element, polyfill) {\n  const window = getWindow(element);\n  if (!isHTMLElement(element)) {\n    return window;\n  }\n  let offsetParent = getTrueOffsetParent(element, polyfill);\n  while (offsetParent && isTableElement(offsetParent) && getComputedStyle(offsetParent).position === 'static') {\n    offsetParent = getTrueOffsetParent(offsetParent, polyfill);\n  }\n  if (offsetParent && (getNodeName(offsetParent) === 'html' || getNodeName(offsetParent) === 'body' && getComputedStyle(offsetParent).position === 'static' && !isContainingBlock(offsetParent))) {\n    return window;\n  }\n  return offsetParent || getContainingBlock(element) || window;\n}\nconst getElementRects = async function (_ref) {\n  let {\n    reference,\n    floating,\n    strategy\n  } = _ref;\n  const getOffsetParentFn = this.getOffsetParent || getOffsetParent;\n  const getDimensionsFn = this.getDimensions;\n  return {\n    reference: getRectRelativeToOffsetParent(reference, await getOffsetParentFn(floating), strategy),\n    floating: {\n      x: 0,\n      y: 0,\n      ...(await getDimensionsFn(floating))\n    }\n  };\n};\nfunction isRTL(element) {\n  return getComputedStyle(element).direction === 'rtl';\n}\nconst platform = {\n  convertOffsetParentRelativeRectToViewportRelativeRect,\n  getDocumentElement,\n  getClippingRect,\n  getOffsetParent,\n  getElementRects,\n  getClientRects,\n  getDimensions,\n  getScale,\n  isElement,\n  isRTL\n};\n\n// https://samthor.au/2021/observing-dom/\nfunction observeMove(element, onMove) {\n  let io = null;\n  let timeoutId;\n  const root = getDocumentElement(element);\n  function cleanup() {\n    clearTimeout(timeoutId);\n    io && io.disconnect();\n    io = null;\n  }\n  function refresh(skip, threshold) {\n    if (skip === void 0) {\n      skip = false;\n    }\n    if (threshold === void 0) {\n      threshold = 1;\n    }\n    cleanup();\n    const {\n      left,\n      top,\n      width,\n      height\n    } = element.getBoundingClientRect();\n    if (!skip) {\n      onMove();\n    }\n    if (!width || !height) {\n      return;\n    }\n    const insetTop = floor(top);\n    const insetRight = floor(root.clientWidth - (left + width));\n    const insetBottom = floor(root.clientHeight - (top + height));\n    const insetLeft = floor(left);\n    const rootMargin = -insetTop + \"px \" + -insetRight + \"px \" + -insetBottom + \"px \" + -insetLeft + \"px\";\n    const options = {\n      rootMargin,\n      threshold: max(0, min(1, threshold)) || 1\n    };\n    let isFirstUpdate = true;\n    function handleObserve(entries) {\n      const ratio = entries[0].intersectionRatio;\n      if (ratio !== threshold) {\n        if (!isFirstUpdate) {\n          return refresh();\n        }\n        if (!ratio) {\n          timeoutId = setTimeout(() => {\n            refresh(false, 1e-7);\n          }, 100);\n        } else {\n          refresh(false, ratio);\n        }\n      }\n      isFirstUpdate = false;\n    }\n\n    // Older browsers don't support a `document` as the root and will throw an\n    // error.\n    try {\n      io = new IntersectionObserver(handleObserve, {\n        ...options,\n        // Handle <iframe>s\n        root: root.ownerDocument\n      });\n    } catch (e) {\n      io = new IntersectionObserver(handleObserve, options);\n    }\n    io.observe(element);\n  }\n  refresh(true);\n  return cleanup;\n}\n\n/**\n * Automatically updates the position of the floating element when necessary.\n * Should only be called when the floating element is mounted on the DOM or\n * visible on the screen.\n * @returns cleanup function that should be invoked when the floating element is\n * removed from the DOM or hidden from the screen.\n * @see https://floating-ui.com/docs/autoUpdate\n */\nfunction autoUpdate(reference, floating, update, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    ancestorScroll = true,\n    ancestorResize = true,\n    elementResize = typeof ResizeObserver === 'function',\n    layoutShift = typeof IntersectionObserver === 'function',\n    animationFrame = false\n  } = options;\n  const referenceEl = unwrapElement(reference);\n  const ancestors = ancestorScroll || ancestorResize ? [...(referenceEl ? getOverflowAncestors(referenceEl) : []), ...getOverflowAncestors(floating)] : [];\n  ancestors.forEach(ancestor => {\n    ancestorScroll && ancestor.addEventListener('scroll', update, {\n      passive: true\n    });\n    ancestorResize && ancestor.addEventListener('resize', update);\n  });\n  const cleanupIo = referenceEl && layoutShift ? observeMove(referenceEl, update) : null;\n  let reobserveFrame = -1;\n  let resizeObserver = null;\n  if (elementResize) {\n    resizeObserver = new ResizeObserver(_ref => {\n      let [firstEntry] = _ref;\n      if (firstEntry && firstEntry.target === referenceEl && resizeObserver) {\n        // Prevent update loops when using the `size` middleware.\n        // https://github.com/floating-ui/floating-ui/issues/1740\n        resizeObserver.unobserve(floating);\n        cancelAnimationFrame(reobserveFrame);\n        reobserveFrame = requestAnimationFrame(() => {\n          resizeObserver && resizeObserver.observe(floating);\n        });\n      }\n      update();\n    });\n    if (referenceEl && !animationFrame) {\n      resizeObserver.observe(referenceEl);\n    }\n    resizeObserver.observe(floating);\n  }\n  let frameId;\n  let prevRefRect = animationFrame ? getBoundingClientRect(reference) : null;\n  if (animationFrame) {\n    frameLoop();\n  }\n  function frameLoop() {\n    const nextRefRect = getBoundingClientRect(reference);\n    if (prevRefRect && (nextRefRect.x !== prevRefRect.x || nextRefRect.y !== prevRefRect.y || nextRefRect.width !== prevRefRect.width || nextRefRect.height !== prevRefRect.height)) {\n      update();\n    }\n    prevRefRect = nextRefRect;\n    frameId = requestAnimationFrame(frameLoop);\n  }\n  update();\n  return () => {\n    ancestors.forEach(ancestor => {\n      ancestorScroll && ancestor.removeEventListener('scroll', update);\n      ancestorResize && ancestor.removeEventListener('resize', update);\n    });\n    cleanupIo && cleanupIo();\n    resizeObserver && resizeObserver.disconnect();\n    resizeObserver = null;\n    if (animationFrame) {\n      cancelAnimationFrame(frameId);\n    }\n  };\n}\n\n/**\n * Computes the `x` and `y` coordinates that will place the floating element\n * next to a reference element when it is given a certain CSS positioning\n * strategy.\n */\nconst computePosition = (reference, floating, options) => {\n  // This caches the expensive `getClippingElementAncestors` function so that\n  // multiple lifecycle resets re-use the same result. It only lives for a\n  // single call. If other functions become expensive, we can add them as well.\n  const cache = new Map();\n  const mergedOptions = {\n    platform,\n    ...options\n  };\n  const platformWithCache = {\n    ...mergedOptions.platform,\n    _c: cache\n  };\n  return computePosition$1(reference, floating, {\n    ...mergedOptions,\n    platform: platformWithCache\n  });\n};\nexport { autoUpdate, computePosition, platform };", "map": {"version": 3, "names": ["rectToClientRect", "computePosition", "computePosition$1", "arrow", "autoPlacement", "detectOverflow", "flip", "hide", "inline", "limitShift", "offset", "shift", "size", "round", "createCoords", "max", "min", "floor", "getComputedStyle", "isHTMLElement", "isElement", "getWindow", "isWebKit", "getDocumentElement", "getNodeName", "isOverflowElement", "getNodeScroll", "getOverflowAncestors", "getParentNode", "isLastTraversableNode", "isContainingBlock", "isTableElement", "getContainingBlock", "getCssDimensions", "element", "css", "width", "parseFloat", "height", "hasOffset", "offsetWidth", "offsetHeight", "<PERSON><PERSON><PERSON><PERSON>", "$", "unwrapElement", "contextElement", "getScale", "dom<PERSON>lement", "rect", "getBoundingClientRect", "x", "y", "Number", "isFinite", "noOffsets", "getVisualOffsets", "win", "visualViewport", "offsetLeft", "offsetTop", "shouldAddVisualOffsets", "isFixed", "floatingOffsetParent", "includeScale", "isFixedStrategy", "offsetParent", "clientRect", "scale", "visualOffsets", "left", "top", "offsetWin", "currentIFrame", "frameElement", "iframeScale", "iframeRect", "clientLeft", "paddingLeft", "clientTop", "paddingTop", "convertOffsetParentRelativeRectToViewportRelativeRect", "_ref", "strategy", "isOffsetParentAnElement", "documentElement", "scroll", "scrollLeft", "scrollTop", "offsets", "offsetRect", "getClientRects", "Array", "from", "getWindowScrollBarX", "getDocumentRect", "html", "body", "ownerDocument", "scrollWidth", "clientWidth", "scrollHeight", "clientHeight", "direction", "getViewportRect", "visualViewportBased", "getInnerBoundingClientRect", "getClientRectFromClippingAncestor", "clippingAncestor", "hasFixedPositionAncestor", "stopNode", "parentNode", "position", "getClippingElementAncestors", "cache", "cachedResult", "get", "result", "filter", "el", "currentContainingBlockComputedStyle", "elementIsFixed", "currentNode", "computedStyle", "currentNodeIsContaining", "shouldDropCurrentNode", "includes", "ancestor", "set", "getClippingRect", "boundary", "rootBoundary", "elementClippingAncestors", "_c", "concat", "clippingAncestors", "firstClippingAncestor", "clippingRect", "reduce", "accRect", "right", "bottom", "getDimensions", "getRectRelativeToOffsetParent", "getTrueOffsetParent", "polyfill", "getOffsetParent", "window", "getElementRects", "reference", "floating", "getOffsetParentFn", "getDimensionsFn", "isRTL", "platform", "<PERSON><PERSON><PERSON>", "onMove", "io", "timeoutId", "root", "cleanup", "clearTimeout", "disconnect", "refresh", "skip", "threshold", "insetTop", "insetRight", "insetBottom", "insetLeft", "rootMargin", "options", "isFirstUpdate", "handleObserve", "entries", "ratio", "intersectionRatio", "setTimeout", "IntersectionObserver", "e", "observe", "autoUpdate", "update", "ancestorScroll", "ancestorResize", "elementResize", "ResizeObserver", "layoutShift", "animationFrame", "referenceEl", "ancestors", "for<PERSON>ach", "addEventListener", "passive", "cleanupIo", "reobserveFrame", "resizeObserver", "firstEntry", "target", "unobserve", "cancelAnimationFrame", "requestAnimationFrame", "frameId", "prevRefRect", "frameLoop", "nextRefRect", "removeEventListener", "Map", "mergedOptions", "platformWithCache"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@floating-ui/dom/dist/floating-ui.dom.mjs"], "sourcesContent": ["import { rectToClientRect, computePosition as computePosition$1 } from '@floating-ui/core';\nexport { arrow, autoPlacement, detectOverflow, flip, hide, inline, limitShift, offset, shift, size } from '@floating-ui/core';\nimport { round, createCoords, max, min, floor } from '@floating-ui/utils';\nimport { getComputedStyle, isHTMLElement, isElement, getWindow, isWebKit, getDocumentElement, getNodeName, isOverflowElement, getNodeScroll, getOverflowAncestors, getParentNode, isLastTraversableNode, isContainingBlock, isTableElement, getContainingBlock } from '@floating-ui/utils/dom';\nexport { getOverflowAncestors } from '@floating-ui/utils/dom';\n\nfunction getCssDimensions(element) {\n  const css = getComputedStyle(element);\n  // In testing environments, the `width` and `height` properties are empty\n  // strings for SVG elements, returning NaN. Fallback to `0` in this case.\n  let width = parseFloat(css.width) || 0;\n  let height = parseFloat(css.height) || 0;\n  const hasOffset = isHTMLElement(element);\n  const offsetWidth = hasOffset ? element.offsetWidth : width;\n  const offsetHeight = hasOffset ? element.offsetHeight : height;\n  const shouldFallback = round(width) !== offsetWidth || round(height) !== offsetHeight;\n  if (shouldFallback) {\n    width = offsetWidth;\n    height = offsetHeight;\n  }\n  return {\n    width,\n    height,\n    $: shouldFallback\n  };\n}\n\nfunction unwrapElement(element) {\n  return !isElement(element) ? element.contextElement : element;\n}\n\nfunction getScale(element) {\n  const domElement = unwrapElement(element);\n  if (!isHTMLElement(domElement)) {\n    return createCoords(1);\n  }\n  const rect = domElement.getBoundingClientRect();\n  const {\n    width,\n    height,\n    $\n  } = getCssDimensions(domElement);\n  let x = ($ ? round(rect.width) : rect.width) / width;\n  let y = ($ ? round(rect.height) : rect.height) / height;\n\n  // 0, NaN, or Infinity should always fallback to 1.\n\n  if (!x || !Number.isFinite(x)) {\n    x = 1;\n  }\n  if (!y || !Number.isFinite(y)) {\n    y = 1;\n  }\n  return {\n    x,\n    y\n  };\n}\n\nconst noOffsets = /*#__PURE__*/createCoords(0);\nfunction getVisualOffsets(element) {\n  const win = getWindow(element);\n  if (!isWebKit() || !win.visualViewport) {\n    return noOffsets;\n  }\n  return {\n    x: win.visualViewport.offsetLeft,\n    y: win.visualViewport.offsetTop\n  };\n}\nfunction shouldAddVisualOffsets(element, isFixed, floatingOffsetParent) {\n  if (isFixed === void 0) {\n    isFixed = false;\n  }\n  if (!floatingOffsetParent || isFixed && floatingOffsetParent !== getWindow(element)) {\n    return false;\n  }\n  return isFixed;\n}\n\nfunction getBoundingClientRect(element, includeScale, isFixedStrategy, offsetParent) {\n  if (includeScale === void 0) {\n    includeScale = false;\n  }\n  if (isFixedStrategy === void 0) {\n    isFixedStrategy = false;\n  }\n  const clientRect = element.getBoundingClientRect();\n  const domElement = unwrapElement(element);\n  let scale = createCoords(1);\n  if (includeScale) {\n    if (offsetParent) {\n      if (isElement(offsetParent)) {\n        scale = getScale(offsetParent);\n      }\n    } else {\n      scale = getScale(element);\n    }\n  }\n  const visualOffsets = shouldAddVisualOffsets(domElement, isFixedStrategy, offsetParent) ? getVisualOffsets(domElement) : createCoords(0);\n  let x = (clientRect.left + visualOffsets.x) / scale.x;\n  let y = (clientRect.top + visualOffsets.y) / scale.y;\n  let width = clientRect.width / scale.x;\n  let height = clientRect.height / scale.y;\n  if (domElement) {\n    const win = getWindow(domElement);\n    const offsetWin = offsetParent && isElement(offsetParent) ? getWindow(offsetParent) : offsetParent;\n    let currentIFrame = win.frameElement;\n    while (currentIFrame && offsetParent && offsetWin !== win) {\n      const iframeScale = getScale(currentIFrame);\n      const iframeRect = currentIFrame.getBoundingClientRect();\n      const css = getComputedStyle(currentIFrame);\n      const left = iframeRect.left + (currentIFrame.clientLeft + parseFloat(css.paddingLeft)) * iframeScale.x;\n      const top = iframeRect.top + (currentIFrame.clientTop + parseFloat(css.paddingTop)) * iframeScale.y;\n      x *= iframeScale.x;\n      y *= iframeScale.y;\n      width *= iframeScale.x;\n      height *= iframeScale.y;\n      x += left;\n      y += top;\n      currentIFrame = getWindow(currentIFrame).frameElement;\n    }\n  }\n  return rectToClientRect({\n    width,\n    height,\n    x,\n    y\n  });\n}\n\nfunction convertOffsetParentRelativeRectToViewportRelativeRect(_ref) {\n  let {\n    rect,\n    offsetParent,\n    strategy\n  } = _ref;\n  const isOffsetParentAnElement = isHTMLElement(offsetParent);\n  const documentElement = getDocumentElement(offsetParent);\n  if (offsetParent === documentElement) {\n    return rect;\n  }\n  let scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  let scale = createCoords(1);\n  const offsets = createCoords(0);\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && strategy !== 'fixed') {\n    if (getNodeName(offsetParent) !== 'body' || isOverflowElement(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n    if (isHTMLElement(offsetParent)) {\n      const offsetRect = getBoundingClientRect(offsetParent);\n      scale = getScale(offsetParent);\n      offsets.x = offsetRect.x + offsetParent.clientLeft;\n      offsets.y = offsetRect.y + offsetParent.clientTop;\n    }\n  }\n  return {\n    width: rect.width * scale.x,\n    height: rect.height * scale.y,\n    x: rect.x * scale.x - scroll.scrollLeft * scale.x + offsets.x,\n    y: rect.y * scale.y - scroll.scrollTop * scale.y + offsets.y\n  };\n}\n\nfunction getClientRects(element) {\n  return Array.from(element.getClientRects());\n}\n\nfunction getWindowScrollBarX(element) {\n  // If <html> has a CSS width greater than the viewport, then this will be\n  // incorrect for RTL.\n  return getBoundingClientRect(getDocumentElement(element)).left + getNodeScroll(element).scrollLeft;\n}\n\n// Gets the entire size of the scrollable document area, even extending outside\n// of the `<html>` and `<body>` rect bounds if horizontally scrollable.\nfunction getDocumentRect(element) {\n  const html = getDocumentElement(element);\n  const scroll = getNodeScroll(element);\n  const body = element.ownerDocument.body;\n  const width = max(html.scrollWidth, html.clientWidth, body.scrollWidth, body.clientWidth);\n  const height = max(html.scrollHeight, html.clientHeight, body.scrollHeight, body.clientHeight);\n  let x = -scroll.scrollLeft + getWindowScrollBarX(element);\n  const y = -scroll.scrollTop;\n  if (getComputedStyle(body).direction === 'rtl') {\n    x += max(html.clientWidth, body.clientWidth) - width;\n  }\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\n\nfunction getViewportRect(element, strategy) {\n  const win = getWindow(element);\n  const html = getDocumentElement(element);\n  const visualViewport = win.visualViewport;\n  let width = html.clientWidth;\n  let height = html.clientHeight;\n  let x = 0;\n  let y = 0;\n  if (visualViewport) {\n    width = visualViewport.width;\n    height = visualViewport.height;\n    const visualViewportBased = isWebKit();\n    if (!visualViewportBased || visualViewportBased && strategy === 'fixed') {\n      x = visualViewport.offsetLeft;\n      y = visualViewport.offsetTop;\n    }\n  }\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\n\n// Returns the inner client rect, subtracting scrollbars if present.\nfunction getInnerBoundingClientRect(element, strategy) {\n  const clientRect = getBoundingClientRect(element, true, strategy === 'fixed');\n  const top = clientRect.top + element.clientTop;\n  const left = clientRect.left + element.clientLeft;\n  const scale = isHTMLElement(element) ? getScale(element) : createCoords(1);\n  const width = element.clientWidth * scale.x;\n  const height = element.clientHeight * scale.y;\n  const x = left * scale.x;\n  const y = top * scale.y;\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\nfunction getClientRectFromClippingAncestor(element, clippingAncestor, strategy) {\n  let rect;\n  if (clippingAncestor === 'viewport') {\n    rect = getViewportRect(element, strategy);\n  } else if (clippingAncestor === 'document') {\n    rect = getDocumentRect(getDocumentElement(element));\n  } else if (isElement(clippingAncestor)) {\n    rect = getInnerBoundingClientRect(clippingAncestor, strategy);\n  } else {\n    const visualOffsets = getVisualOffsets(element);\n    rect = {\n      ...clippingAncestor,\n      x: clippingAncestor.x - visualOffsets.x,\n      y: clippingAncestor.y - visualOffsets.y\n    };\n  }\n  return rectToClientRect(rect);\n}\nfunction hasFixedPositionAncestor(element, stopNode) {\n  const parentNode = getParentNode(element);\n  if (parentNode === stopNode || !isElement(parentNode) || isLastTraversableNode(parentNode)) {\n    return false;\n  }\n  return getComputedStyle(parentNode).position === 'fixed' || hasFixedPositionAncestor(parentNode, stopNode);\n}\n\n// A \"clipping ancestor\" is an `overflow` element with the characteristic of\n// clipping (or hiding) child elements. This returns all clipping ancestors\n// of the given element up the tree.\nfunction getClippingElementAncestors(element, cache) {\n  const cachedResult = cache.get(element);\n  if (cachedResult) {\n    return cachedResult;\n  }\n  let result = getOverflowAncestors(element, [], false).filter(el => isElement(el) && getNodeName(el) !== 'body');\n  let currentContainingBlockComputedStyle = null;\n  const elementIsFixed = getComputedStyle(element).position === 'fixed';\n  let currentNode = elementIsFixed ? getParentNode(element) : element;\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n  while (isElement(currentNode) && !isLastTraversableNode(currentNode)) {\n    const computedStyle = getComputedStyle(currentNode);\n    const currentNodeIsContaining = isContainingBlock(currentNode);\n    if (!currentNodeIsContaining && computedStyle.position === 'fixed') {\n      currentContainingBlockComputedStyle = null;\n    }\n    const shouldDropCurrentNode = elementIsFixed ? !currentNodeIsContaining && !currentContainingBlockComputedStyle : !currentNodeIsContaining && computedStyle.position === 'static' && !!currentContainingBlockComputedStyle && ['absolute', 'fixed'].includes(currentContainingBlockComputedStyle.position) || isOverflowElement(currentNode) && !currentNodeIsContaining && hasFixedPositionAncestor(element, currentNode);\n    if (shouldDropCurrentNode) {\n      // Drop non-containing blocks.\n      result = result.filter(ancestor => ancestor !== currentNode);\n    } else {\n      // Record last containing block for next iteration.\n      currentContainingBlockComputedStyle = computedStyle;\n    }\n    currentNode = getParentNode(currentNode);\n  }\n  cache.set(element, result);\n  return result;\n}\n\n// Gets the maximum area that the element is visible in due to any number of\n// clipping ancestors.\nfunction getClippingRect(_ref) {\n  let {\n    element,\n    boundary,\n    rootBoundary,\n    strategy\n  } = _ref;\n  const elementClippingAncestors = boundary === 'clippingAncestors' ? getClippingElementAncestors(element, this._c) : [].concat(boundary);\n  const clippingAncestors = [...elementClippingAncestors, rootBoundary];\n  const firstClippingAncestor = clippingAncestors[0];\n  const clippingRect = clippingAncestors.reduce((accRect, clippingAncestor) => {\n    const rect = getClientRectFromClippingAncestor(element, clippingAncestor, strategy);\n    accRect.top = max(rect.top, accRect.top);\n    accRect.right = min(rect.right, accRect.right);\n    accRect.bottom = min(rect.bottom, accRect.bottom);\n    accRect.left = max(rect.left, accRect.left);\n    return accRect;\n  }, getClientRectFromClippingAncestor(element, firstClippingAncestor, strategy));\n  return {\n    width: clippingRect.right - clippingRect.left,\n    height: clippingRect.bottom - clippingRect.top,\n    x: clippingRect.left,\n    y: clippingRect.top\n  };\n}\n\nfunction getDimensions(element) {\n  return getCssDimensions(element);\n}\n\nfunction getRectRelativeToOffsetParent(element, offsetParent, strategy) {\n  const isOffsetParentAnElement = isHTMLElement(offsetParent);\n  const documentElement = getDocumentElement(offsetParent);\n  const isFixed = strategy === 'fixed';\n  const rect = getBoundingClientRect(element, true, isFixed, offsetParent);\n  let scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  const offsets = createCoords(0);\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== 'body' || isOverflowElement(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n    if (isOffsetParentAnElement) {\n      const offsetRect = getBoundingClientRect(offsetParent, true, isFixed, offsetParent);\n      offsets.x = offsetRect.x + offsetParent.clientLeft;\n      offsets.y = offsetRect.y + offsetParent.clientTop;\n    } else if (documentElement) {\n      offsets.x = getWindowScrollBarX(documentElement);\n    }\n  }\n  return {\n    x: rect.left + scroll.scrollLeft - offsets.x,\n    y: rect.top + scroll.scrollTop - offsets.y,\n    width: rect.width,\n    height: rect.height\n  };\n}\n\nfunction getTrueOffsetParent(element, polyfill) {\n  if (!isHTMLElement(element) || getComputedStyle(element).position === 'fixed') {\n    return null;\n  }\n  if (polyfill) {\n    return polyfill(element);\n  }\n  return element.offsetParent;\n}\n\n// Gets the closest ancestor positioned element. Handles some edge cases,\n// such as table ancestors and cross browser bugs.\nfunction getOffsetParent(element, polyfill) {\n  const window = getWindow(element);\n  if (!isHTMLElement(element)) {\n    return window;\n  }\n  let offsetParent = getTrueOffsetParent(element, polyfill);\n  while (offsetParent && isTableElement(offsetParent) && getComputedStyle(offsetParent).position === 'static') {\n    offsetParent = getTrueOffsetParent(offsetParent, polyfill);\n  }\n  if (offsetParent && (getNodeName(offsetParent) === 'html' || getNodeName(offsetParent) === 'body' && getComputedStyle(offsetParent).position === 'static' && !isContainingBlock(offsetParent))) {\n    return window;\n  }\n  return offsetParent || getContainingBlock(element) || window;\n}\n\nconst getElementRects = async function (_ref) {\n  let {\n    reference,\n    floating,\n    strategy\n  } = _ref;\n  const getOffsetParentFn = this.getOffsetParent || getOffsetParent;\n  const getDimensionsFn = this.getDimensions;\n  return {\n    reference: getRectRelativeToOffsetParent(reference, await getOffsetParentFn(floating), strategy),\n    floating: {\n      x: 0,\n      y: 0,\n      ...(await getDimensionsFn(floating))\n    }\n  };\n};\n\nfunction isRTL(element) {\n  return getComputedStyle(element).direction === 'rtl';\n}\n\nconst platform = {\n  convertOffsetParentRelativeRectToViewportRelativeRect,\n  getDocumentElement,\n  getClippingRect,\n  getOffsetParent,\n  getElementRects,\n  getClientRects,\n  getDimensions,\n  getScale,\n  isElement,\n  isRTL\n};\n\n// https://samthor.au/2021/observing-dom/\nfunction observeMove(element, onMove) {\n  let io = null;\n  let timeoutId;\n  const root = getDocumentElement(element);\n  function cleanup() {\n    clearTimeout(timeoutId);\n    io && io.disconnect();\n    io = null;\n  }\n  function refresh(skip, threshold) {\n    if (skip === void 0) {\n      skip = false;\n    }\n    if (threshold === void 0) {\n      threshold = 1;\n    }\n    cleanup();\n    const {\n      left,\n      top,\n      width,\n      height\n    } = element.getBoundingClientRect();\n    if (!skip) {\n      onMove();\n    }\n    if (!width || !height) {\n      return;\n    }\n    const insetTop = floor(top);\n    const insetRight = floor(root.clientWidth - (left + width));\n    const insetBottom = floor(root.clientHeight - (top + height));\n    const insetLeft = floor(left);\n    const rootMargin = -insetTop + \"px \" + -insetRight + \"px \" + -insetBottom + \"px \" + -insetLeft + \"px\";\n    const options = {\n      rootMargin,\n      threshold: max(0, min(1, threshold)) || 1\n    };\n    let isFirstUpdate = true;\n    function handleObserve(entries) {\n      const ratio = entries[0].intersectionRatio;\n      if (ratio !== threshold) {\n        if (!isFirstUpdate) {\n          return refresh();\n        }\n        if (!ratio) {\n          timeoutId = setTimeout(() => {\n            refresh(false, 1e-7);\n          }, 100);\n        } else {\n          refresh(false, ratio);\n        }\n      }\n      isFirstUpdate = false;\n    }\n\n    // Older browsers don't support a `document` as the root and will throw an\n    // error.\n    try {\n      io = new IntersectionObserver(handleObserve, {\n        ...options,\n        // Handle <iframe>s\n        root: root.ownerDocument\n      });\n    } catch (e) {\n      io = new IntersectionObserver(handleObserve, options);\n    }\n    io.observe(element);\n  }\n  refresh(true);\n  return cleanup;\n}\n\n/**\n * Automatically updates the position of the floating element when necessary.\n * Should only be called when the floating element is mounted on the DOM or\n * visible on the screen.\n * @returns cleanup function that should be invoked when the floating element is\n * removed from the DOM or hidden from the screen.\n * @see https://floating-ui.com/docs/autoUpdate\n */\nfunction autoUpdate(reference, floating, update, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    ancestorScroll = true,\n    ancestorResize = true,\n    elementResize = typeof ResizeObserver === 'function',\n    layoutShift = typeof IntersectionObserver === 'function',\n    animationFrame = false\n  } = options;\n  const referenceEl = unwrapElement(reference);\n  const ancestors = ancestorScroll || ancestorResize ? [...(referenceEl ? getOverflowAncestors(referenceEl) : []), ...getOverflowAncestors(floating)] : [];\n  ancestors.forEach(ancestor => {\n    ancestorScroll && ancestor.addEventListener('scroll', update, {\n      passive: true\n    });\n    ancestorResize && ancestor.addEventListener('resize', update);\n  });\n  const cleanupIo = referenceEl && layoutShift ? observeMove(referenceEl, update) : null;\n  let reobserveFrame = -1;\n  let resizeObserver = null;\n  if (elementResize) {\n    resizeObserver = new ResizeObserver(_ref => {\n      let [firstEntry] = _ref;\n      if (firstEntry && firstEntry.target === referenceEl && resizeObserver) {\n        // Prevent update loops when using the `size` middleware.\n        // https://github.com/floating-ui/floating-ui/issues/1740\n        resizeObserver.unobserve(floating);\n        cancelAnimationFrame(reobserveFrame);\n        reobserveFrame = requestAnimationFrame(() => {\n          resizeObserver && resizeObserver.observe(floating);\n        });\n      }\n      update();\n    });\n    if (referenceEl && !animationFrame) {\n      resizeObserver.observe(referenceEl);\n    }\n    resizeObserver.observe(floating);\n  }\n  let frameId;\n  let prevRefRect = animationFrame ? getBoundingClientRect(reference) : null;\n  if (animationFrame) {\n    frameLoop();\n  }\n  function frameLoop() {\n    const nextRefRect = getBoundingClientRect(reference);\n    if (prevRefRect && (nextRefRect.x !== prevRefRect.x || nextRefRect.y !== prevRefRect.y || nextRefRect.width !== prevRefRect.width || nextRefRect.height !== prevRefRect.height)) {\n      update();\n    }\n    prevRefRect = nextRefRect;\n    frameId = requestAnimationFrame(frameLoop);\n  }\n  update();\n  return () => {\n    ancestors.forEach(ancestor => {\n      ancestorScroll && ancestor.removeEventListener('scroll', update);\n      ancestorResize && ancestor.removeEventListener('resize', update);\n    });\n    cleanupIo && cleanupIo();\n    resizeObserver && resizeObserver.disconnect();\n    resizeObserver = null;\n    if (animationFrame) {\n      cancelAnimationFrame(frameId);\n    }\n  };\n}\n\n/**\n * Computes the `x` and `y` coordinates that will place the floating element\n * next to a reference element when it is given a certain CSS positioning\n * strategy.\n */\nconst computePosition = (reference, floating, options) => {\n  // This caches the expensive `getClippingElementAncestors` function so that\n  // multiple lifecycle resets re-use the same result. It only lives for a\n  // single call. If other functions become expensive, we can add them as well.\n  const cache = new Map();\n  const mergedOptions = {\n    platform,\n    ...options\n  };\n  const platformWithCache = {\n    ...mergedOptions.platform,\n    _c: cache\n  };\n  return computePosition$1(reference, floating, {\n    ...mergedOptions,\n    platform: platformWithCache\n  });\n};\n\nexport { autoUpdate, computePosition, platform };\n"], "mappings": "AAAA,SAASA,gBAAgB,EAAEC,eAAe,IAAIC,iBAAiB,QAAQ,mBAAmB;AAC1F,SAASC,KAAK,EAAEC,aAAa,EAAEC,cAAc,EAAEC,IAAI,EAAEC,IAAI,EAAEC,MAAM,EAAEC,UAAU,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,QAAQ,mBAAmB;AAC7H,SAASC,KAAK,EAAEC,YAAY,EAAEC,GAAG,EAAEC,GAAG,EAAEC,KAAK,QAAQ,oBAAoB;AACzE,SAASC,gBAAgB,EAAEC,aAAa,EAAEC,SAAS,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,kBAAkB,EAAEC,WAAW,EAAEC,iBAAiB,EAAEC,aAAa,EAAEC,oBAAoB,EAAEC,aAAa,EAAEC,qBAAqB,EAAEC,iBAAiB,EAAEC,cAAc,EAAEC,kBAAkB,QAAQ,wBAAwB;AAC9R,SAASL,oBAAoB,QAAQ,wBAAwB;AAE7D,SAASM,gBAAgBA,CAACC,OAAO,EAAE;EACjC,MAAMC,GAAG,GAAGjB,gBAAgB,CAACgB,OAAO,CAAC;EACrC;EACA;EACA,IAAIE,KAAK,GAAGC,UAAU,CAACF,GAAG,CAACC,KAAK,CAAC,IAAI,CAAC;EACtC,IAAIE,MAAM,GAAGD,UAAU,CAACF,GAAG,CAACG,MAAM,CAAC,IAAI,CAAC;EACxC,MAAMC,SAAS,GAAGpB,aAAa,CAACe,OAAO,CAAC;EACxC,MAAMM,WAAW,GAAGD,SAAS,GAAGL,OAAO,CAACM,WAAW,GAAGJ,KAAK;EAC3D,MAAMK,YAAY,GAAGF,SAAS,GAAGL,OAAO,CAACO,YAAY,GAAGH,MAAM;EAC9D,MAAMI,cAAc,GAAG7B,KAAK,CAACuB,KAAK,CAAC,KAAKI,WAAW,IAAI3B,KAAK,CAACyB,MAAM,CAAC,KAAKG,YAAY;EACrF,IAAIC,cAAc,EAAE;IAClBN,KAAK,GAAGI,WAAW;IACnBF,MAAM,GAAGG,YAAY;EACvB;EACA,OAAO;IACLL,KAAK;IACLE,MAAM;IACNK,CAAC,EAAED;EACL,CAAC;AACH;AAEA,SAASE,aAAaA,CAACV,OAAO,EAAE;EAC9B,OAAO,CAACd,SAAS,CAACc,OAAO,CAAC,GAAGA,OAAO,CAACW,cAAc,GAAGX,OAAO;AAC/D;AAEA,SAASY,QAAQA,CAACZ,OAAO,EAAE;EACzB,MAAMa,UAAU,GAAGH,aAAa,CAACV,OAAO,CAAC;EACzC,IAAI,CAACf,aAAa,CAAC4B,UAAU,CAAC,EAAE;IAC9B,OAAOjC,YAAY,CAAC,CAAC,CAAC;EACxB;EACA,MAAMkC,IAAI,GAAGD,UAAU,CAACE,qBAAqB,CAAC,CAAC;EAC/C,MAAM;IACJb,KAAK;IACLE,MAAM;IACNK;EACF,CAAC,GAAGV,gBAAgB,CAACc,UAAU,CAAC;EAChC,IAAIG,CAAC,GAAG,CAACP,CAAC,GAAG9B,KAAK,CAACmC,IAAI,CAACZ,KAAK,CAAC,GAAGY,IAAI,CAACZ,KAAK,IAAIA,KAAK;EACpD,IAAIe,CAAC,GAAG,CAACR,CAAC,GAAG9B,KAAK,CAACmC,IAAI,CAACV,MAAM,CAAC,GAAGU,IAAI,CAACV,MAAM,IAAIA,MAAM;;EAEvD;;EAEA,IAAI,CAACY,CAAC,IAAI,CAACE,MAAM,CAACC,QAAQ,CAACH,CAAC,CAAC,EAAE;IAC7BA,CAAC,GAAG,CAAC;EACP;EACA,IAAI,CAACC,CAAC,IAAI,CAACC,MAAM,CAACC,QAAQ,CAACF,CAAC,CAAC,EAAE;IAC7BA,CAAC,GAAG,CAAC;EACP;EACA,OAAO;IACLD,CAAC;IACDC;EACF,CAAC;AACH;AAEA,MAAMG,SAAS,GAAG,aAAaxC,YAAY,CAAC,CAAC,CAAC;AAC9C,SAASyC,gBAAgBA,CAACrB,OAAO,EAAE;EACjC,MAAMsB,GAAG,GAAGnC,SAAS,CAACa,OAAO,CAAC;EAC9B,IAAI,CAACZ,QAAQ,CAAC,CAAC,IAAI,CAACkC,GAAG,CAACC,cAAc,EAAE;IACtC,OAAOH,SAAS;EAClB;EACA,OAAO;IACLJ,CAAC,EAAEM,GAAG,CAACC,cAAc,CAACC,UAAU;IAChCP,CAAC,EAAEK,GAAG,CAACC,cAAc,CAACE;EACxB,CAAC;AACH;AACA,SAASC,sBAAsBA,CAAC1B,OAAO,EAAE2B,OAAO,EAAEC,oBAAoB,EAAE;EACtE,IAAID,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,KAAK;EACjB;EACA,IAAI,CAACC,oBAAoB,IAAID,OAAO,IAAIC,oBAAoB,KAAKzC,SAAS,CAACa,OAAO,CAAC,EAAE;IACnF,OAAO,KAAK;EACd;EACA,OAAO2B,OAAO;AAChB;AAEA,SAASZ,qBAAqBA,CAACf,OAAO,EAAE6B,YAAY,EAAEC,eAAe,EAAEC,YAAY,EAAE;EACnF,IAAIF,YAAY,KAAK,KAAK,CAAC,EAAE;IAC3BA,YAAY,GAAG,KAAK;EACtB;EACA,IAAIC,eAAe,KAAK,KAAK,CAAC,EAAE;IAC9BA,eAAe,GAAG,KAAK;EACzB;EACA,MAAME,UAAU,GAAGhC,OAAO,CAACe,qBAAqB,CAAC,CAAC;EAClD,MAAMF,UAAU,GAAGH,aAAa,CAACV,OAAO,CAAC;EACzC,IAAIiC,KAAK,GAAGrD,YAAY,CAAC,CAAC,CAAC;EAC3B,IAAIiD,YAAY,EAAE;IAChB,IAAIE,YAAY,EAAE;MAChB,IAAI7C,SAAS,CAAC6C,YAAY,CAAC,EAAE;QAC3BE,KAAK,GAAGrB,QAAQ,CAACmB,YAAY,CAAC;MAChC;IACF,CAAC,MAAM;MACLE,KAAK,GAAGrB,QAAQ,CAACZ,OAAO,CAAC;IAC3B;EACF;EACA,MAAMkC,aAAa,GAAGR,sBAAsB,CAACb,UAAU,EAAEiB,eAAe,EAAEC,YAAY,CAAC,GAAGV,gBAAgB,CAACR,UAAU,CAAC,GAAGjC,YAAY,CAAC,CAAC,CAAC;EACxI,IAAIoC,CAAC,GAAG,CAACgB,UAAU,CAACG,IAAI,GAAGD,aAAa,CAAClB,CAAC,IAAIiB,KAAK,CAACjB,CAAC;EACrD,IAAIC,CAAC,GAAG,CAACe,UAAU,CAACI,GAAG,GAAGF,aAAa,CAACjB,CAAC,IAAIgB,KAAK,CAAChB,CAAC;EACpD,IAAIf,KAAK,GAAG8B,UAAU,CAAC9B,KAAK,GAAG+B,KAAK,CAACjB,CAAC;EACtC,IAAIZ,MAAM,GAAG4B,UAAU,CAAC5B,MAAM,GAAG6B,KAAK,CAAChB,CAAC;EACxC,IAAIJ,UAAU,EAAE;IACd,MAAMS,GAAG,GAAGnC,SAAS,CAAC0B,UAAU,CAAC;IACjC,MAAMwB,SAAS,GAAGN,YAAY,IAAI7C,SAAS,CAAC6C,YAAY,CAAC,GAAG5C,SAAS,CAAC4C,YAAY,CAAC,GAAGA,YAAY;IAClG,IAAIO,aAAa,GAAGhB,GAAG,CAACiB,YAAY;IACpC,OAAOD,aAAa,IAAIP,YAAY,IAAIM,SAAS,KAAKf,GAAG,EAAE;MACzD,MAAMkB,WAAW,GAAG5B,QAAQ,CAAC0B,aAAa,CAAC;MAC3C,MAAMG,UAAU,GAAGH,aAAa,CAACvB,qBAAqB,CAAC,CAAC;MACxD,MAAMd,GAAG,GAAGjB,gBAAgB,CAACsD,aAAa,CAAC;MAC3C,MAAMH,IAAI,GAAGM,UAAU,CAACN,IAAI,GAAG,CAACG,aAAa,CAACI,UAAU,GAAGvC,UAAU,CAACF,GAAG,CAAC0C,WAAW,CAAC,IAAIH,WAAW,CAACxB,CAAC;MACvG,MAAMoB,GAAG,GAAGK,UAAU,CAACL,GAAG,GAAG,CAACE,aAAa,CAACM,SAAS,GAAGzC,UAAU,CAACF,GAAG,CAAC4C,UAAU,CAAC,IAAIL,WAAW,CAACvB,CAAC;MACnGD,CAAC,IAAIwB,WAAW,CAACxB,CAAC;MAClBC,CAAC,IAAIuB,WAAW,CAACvB,CAAC;MAClBf,KAAK,IAAIsC,WAAW,CAACxB,CAAC;MACtBZ,MAAM,IAAIoC,WAAW,CAACvB,CAAC;MACvBD,CAAC,IAAImB,IAAI;MACTlB,CAAC,IAAImB,GAAG;MACRE,aAAa,GAAGnD,SAAS,CAACmD,aAAa,CAAC,CAACC,YAAY;IACvD;EACF;EACA,OAAOzE,gBAAgB,CAAC;IACtBoC,KAAK;IACLE,MAAM;IACNY,CAAC;IACDC;EACF,CAAC,CAAC;AACJ;AAEA,SAAS6B,qDAAqDA,CAACC,IAAI,EAAE;EACnE,IAAI;IACFjC,IAAI;IACJiB,YAAY;IACZiB;EACF,CAAC,GAAGD,IAAI;EACR,MAAME,uBAAuB,GAAGhE,aAAa,CAAC8C,YAAY,CAAC;EAC3D,MAAMmB,eAAe,GAAG7D,kBAAkB,CAAC0C,YAAY,CAAC;EACxD,IAAIA,YAAY,KAAKmB,eAAe,EAAE;IACpC,OAAOpC,IAAI;EACb;EACA,IAAIqC,MAAM,GAAG;IACXC,UAAU,EAAE,CAAC;IACbC,SAAS,EAAE;EACb,CAAC;EACD,IAAIpB,KAAK,GAAGrD,YAAY,CAAC,CAAC,CAAC;EAC3B,MAAM0E,OAAO,GAAG1E,YAAY,CAAC,CAAC,CAAC;EAC/B,IAAIqE,uBAAuB,IAAI,CAACA,uBAAuB,IAAID,QAAQ,KAAK,OAAO,EAAE;IAC/E,IAAI1D,WAAW,CAACyC,YAAY,CAAC,KAAK,MAAM,IAAIxC,iBAAiB,CAAC2D,eAAe,CAAC,EAAE;MAC9EC,MAAM,GAAG3D,aAAa,CAACuC,YAAY,CAAC;IACtC;IACA,IAAI9C,aAAa,CAAC8C,YAAY,CAAC,EAAE;MAC/B,MAAMwB,UAAU,GAAGxC,qBAAqB,CAACgB,YAAY,CAAC;MACtDE,KAAK,GAAGrB,QAAQ,CAACmB,YAAY,CAAC;MAC9BuB,OAAO,CAACtC,CAAC,GAAGuC,UAAU,CAACvC,CAAC,GAAGe,YAAY,CAACW,UAAU;MAClDY,OAAO,CAACrC,CAAC,GAAGsC,UAAU,CAACtC,CAAC,GAAGc,YAAY,CAACa,SAAS;IACnD;EACF;EACA,OAAO;IACL1C,KAAK,EAAEY,IAAI,CAACZ,KAAK,GAAG+B,KAAK,CAACjB,CAAC;IAC3BZ,MAAM,EAAEU,IAAI,CAACV,MAAM,GAAG6B,KAAK,CAAChB,CAAC;IAC7BD,CAAC,EAAEF,IAAI,CAACE,CAAC,GAAGiB,KAAK,CAACjB,CAAC,GAAGmC,MAAM,CAACC,UAAU,GAAGnB,KAAK,CAACjB,CAAC,GAAGsC,OAAO,CAACtC,CAAC;IAC7DC,CAAC,EAAEH,IAAI,CAACG,CAAC,GAAGgB,KAAK,CAAChB,CAAC,GAAGkC,MAAM,CAACE,SAAS,GAAGpB,KAAK,CAAChB,CAAC,GAAGqC,OAAO,CAACrC;EAC7D,CAAC;AACH;AAEA,SAASuC,cAAcA,CAACxD,OAAO,EAAE;EAC/B,OAAOyD,KAAK,CAACC,IAAI,CAAC1D,OAAO,CAACwD,cAAc,CAAC,CAAC,CAAC;AAC7C;AAEA,SAASG,mBAAmBA,CAAC3D,OAAO,EAAE;EACpC;EACA;EACA,OAAOe,qBAAqB,CAAC1B,kBAAkB,CAACW,OAAO,CAAC,CAAC,CAACmC,IAAI,GAAG3C,aAAa,CAACQ,OAAO,CAAC,CAACoD,UAAU;AACpG;;AAEA;AACA;AACA,SAASQ,eAAeA,CAAC5D,OAAO,EAAE;EAChC,MAAM6D,IAAI,GAAGxE,kBAAkB,CAACW,OAAO,CAAC;EACxC,MAAMmD,MAAM,GAAG3D,aAAa,CAACQ,OAAO,CAAC;EACrC,MAAM8D,IAAI,GAAG9D,OAAO,CAAC+D,aAAa,CAACD,IAAI;EACvC,MAAM5D,KAAK,GAAGrB,GAAG,CAACgF,IAAI,CAACG,WAAW,EAAEH,IAAI,CAACI,WAAW,EAAEH,IAAI,CAACE,WAAW,EAAEF,IAAI,CAACG,WAAW,CAAC;EACzF,MAAM7D,MAAM,GAAGvB,GAAG,CAACgF,IAAI,CAACK,YAAY,EAAEL,IAAI,CAACM,YAAY,EAAEL,IAAI,CAACI,YAAY,EAAEJ,IAAI,CAACK,YAAY,CAAC;EAC9F,IAAInD,CAAC,GAAG,CAACmC,MAAM,CAACC,UAAU,GAAGO,mBAAmB,CAAC3D,OAAO,CAAC;EACzD,MAAMiB,CAAC,GAAG,CAACkC,MAAM,CAACE,SAAS;EAC3B,IAAIrE,gBAAgB,CAAC8E,IAAI,CAAC,CAACM,SAAS,KAAK,KAAK,EAAE;IAC9CpD,CAAC,IAAInC,GAAG,CAACgF,IAAI,CAACI,WAAW,EAAEH,IAAI,CAACG,WAAW,CAAC,GAAG/D,KAAK;EACtD;EACA,OAAO;IACLA,KAAK;IACLE,MAAM;IACNY,CAAC;IACDC;EACF,CAAC;AACH;AAEA,SAASoD,eAAeA,CAACrE,OAAO,EAAEgD,QAAQ,EAAE;EAC1C,MAAM1B,GAAG,GAAGnC,SAAS,CAACa,OAAO,CAAC;EAC9B,MAAM6D,IAAI,GAAGxE,kBAAkB,CAACW,OAAO,CAAC;EACxC,MAAMuB,cAAc,GAAGD,GAAG,CAACC,cAAc;EACzC,IAAIrB,KAAK,GAAG2D,IAAI,CAACI,WAAW;EAC5B,IAAI7D,MAAM,GAAGyD,IAAI,CAACM,YAAY;EAC9B,IAAInD,CAAC,GAAG,CAAC;EACT,IAAIC,CAAC,GAAG,CAAC;EACT,IAAIM,cAAc,EAAE;IAClBrB,KAAK,GAAGqB,cAAc,CAACrB,KAAK;IAC5BE,MAAM,GAAGmB,cAAc,CAACnB,MAAM;IAC9B,MAAMkE,mBAAmB,GAAGlF,QAAQ,CAAC,CAAC;IACtC,IAAI,CAACkF,mBAAmB,IAAIA,mBAAmB,IAAItB,QAAQ,KAAK,OAAO,EAAE;MACvEhC,CAAC,GAAGO,cAAc,CAACC,UAAU;MAC7BP,CAAC,GAAGM,cAAc,CAACE,SAAS;IAC9B;EACF;EACA,OAAO;IACLvB,KAAK;IACLE,MAAM;IACNY,CAAC;IACDC;EACF,CAAC;AACH;;AAEA;AACA,SAASsD,0BAA0BA,CAACvE,OAAO,EAAEgD,QAAQ,EAAE;EACrD,MAAMhB,UAAU,GAAGjB,qBAAqB,CAACf,OAAO,EAAE,IAAI,EAAEgD,QAAQ,KAAK,OAAO,CAAC;EAC7E,MAAMZ,GAAG,GAAGJ,UAAU,CAACI,GAAG,GAAGpC,OAAO,CAAC4C,SAAS;EAC9C,MAAMT,IAAI,GAAGH,UAAU,CAACG,IAAI,GAAGnC,OAAO,CAAC0C,UAAU;EACjD,MAAMT,KAAK,GAAGhD,aAAa,CAACe,OAAO,CAAC,GAAGY,QAAQ,CAACZ,OAAO,CAAC,GAAGpB,YAAY,CAAC,CAAC,CAAC;EAC1E,MAAMsB,KAAK,GAAGF,OAAO,CAACiE,WAAW,GAAGhC,KAAK,CAACjB,CAAC;EAC3C,MAAMZ,MAAM,GAAGJ,OAAO,CAACmE,YAAY,GAAGlC,KAAK,CAAChB,CAAC;EAC7C,MAAMD,CAAC,GAAGmB,IAAI,GAAGF,KAAK,CAACjB,CAAC;EACxB,MAAMC,CAAC,GAAGmB,GAAG,GAAGH,KAAK,CAAChB,CAAC;EACvB,OAAO;IACLf,KAAK;IACLE,MAAM;IACNY,CAAC;IACDC;EACF,CAAC;AACH;AACA,SAASuD,iCAAiCA,CAACxE,OAAO,EAAEyE,gBAAgB,EAAEzB,QAAQ,EAAE;EAC9E,IAAIlC,IAAI;EACR,IAAI2D,gBAAgB,KAAK,UAAU,EAAE;IACnC3D,IAAI,GAAGuD,eAAe,CAACrE,OAAO,EAAEgD,QAAQ,CAAC;EAC3C,CAAC,MAAM,IAAIyB,gBAAgB,KAAK,UAAU,EAAE;IAC1C3D,IAAI,GAAG8C,eAAe,CAACvE,kBAAkB,CAACW,OAAO,CAAC,CAAC;EACrD,CAAC,MAAM,IAAId,SAAS,CAACuF,gBAAgB,CAAC,EAAE;IACtC3D,IAAI,GAAGyD,0BAA0B,CAACE,gBAAgB,EAAEzB,QAAQ,CAAC;EAC/D,CAAC,MAAM;IACL,MAAMd,aAAa,GAAGb,gBAAgB,CAACrB,OAAO,CAAC;IAC/Cc,IAAI,GAAG;MACL,GAAG2D,gBAAgB;MACnBzD,CAAC,EAAEyD,gBAAgB,CAACzD,CAAC,GAAGkB,aAAa,CAAClB,CAAC;MACvCC,CAAC,EAAEwD,gBAAgB,CAACxD,CAAC,GAAGiB,aAAa,CAACjB;IACxC,CAAC;EACH;EACA,OAAOnD,gBAAgB,CAACgD,IAAI,CAAC;AAC/B;AACA,SAAS4D,wBAAwBA,CAAC1E,OAAO,EAAE2E,QAAQ,EAAE;EACnD,MAAMC,UAAU,GAAGlF,aAAa,CAACM,OAAO,CAAC;EACzC,IAAI4E,UAAU,KAAKD,QAAQ,IAAI,CAACzF,SAAS,CAAC0F,UAAU,CAAC,IAAIjF,qBAAqB,CAACiF,UAAU,CAAC,EAAE;IAC1F,OAAO,KAAK;EACd;EACA,OAAO5F,gBAAgB,CAAC4F,UAAU,CAAC,CAACC,QAAQ,KAAK,OAAO,IAAIH,wBAAwB,CAACE,UAAU,EAAED,QAAQ,CAAC;AAC5G;;AAEA;AACA;AACA;AACA,SAASG,2BAA2BA,CAAC9E,OAAO,EAAE+E,KAAK,EAAE;EACnD,MAAMC,YAAY,GAAGD,KAAK,CAACE,GAAG,CAACjF,OAAO,CAAC;EACvC,IAAIgF,YAAY,EAAE;IAChB,OAAOA,YAAY;EACrB;EACA,IAAIE,MAAM,GAAGzF,oBAAoB,CAACO,OAAO,EAAE,EAAE,EAAE,KAAK,CAAC,CAACmF,MAAM,CAACC,EAAE,IAAIlG,SAAS,CAACkG,EAAE,CAAC,IAAI9F,WAAW,CAAC8F,EAAE,CAAC,KAAK,MAAM,CAAC;EAC/G,IAAIC,mCAAmC,GAAG,IAAI;EAC9C,MAAMC,cAAc,GAAGtG,gBAAgB,CAACgB,OAAO,CAAC,CAAC6E,QAAQ,KAAK,OAAO;EACrE,IAAIU,WAAW,GAAGD,cAAc,GAAG5F,aAAa,CAACM,OAAO,CAAC,GAAGA,OAAO;;EAEnE;EACA,OAAOd,SAAS,CAACqG,WAAW,CAAC,IAAI,CAAC5F,qBAAqB,CAAC4F,WAAW,CAAC,EAAE;IACpE,MAAMC,aAAa,GAAGxG,gBAAgB,CAACuG,WAAW,CAAC;IACnD,MAAME,uBAAuB,GAAG7F,iBAAiB,CAAC2F,WAAW,CAAC;IAC9D,IAAI,CAACE,uBAAuB,IAAID,aAAa,CAACX,QAAQ,KAAK,OAAO,EAAE;MAClEQ,mCAAmC,GAAG,IAAI;IAC5C;IACA,MAAMK,qBAAqB,GAAGJ,cAAc,GAAG,CAACG,uBAAuB,IAAI,CAACJ,mCAAmC,GAAG,CAACI,uBAAuB,IAAID,aAAa,CAACX,QAAQ,KAAK,QAAQ,IAAI,CAAC,CAACQ,mCAAmC,IAAI,CAAC,UAAU,EAAE,OAAO,CAAC,CAACM,QAAQ,CAACN,mCAAmC,CAACR,QAAQ,CAAC,IAAItF,iBAAiB,CAACgG,WAAW,CAAC,IAAI,CAACE,uBAAuB,IAAIf,wBAAwB,CAAC1E,OAAO,EAAEuF,WAAW,CAAC;IAC1Z,IAAIG,qBAAqB,EAAE;MACzB;MACAR,MAAM,GAAGA,MAAM,CAACC,MAAM,CAACS,QAAQ,IAAIA,QAAQ,KAAKL,WAAW,CAAC;IAC9D,CAAC,MAAM;MACL;MACAF,mCAAmC,GAAGG,aAAa;IACrD;IACAD,WAAW,GAAG7F,aAAa,CAAC6F,WAAW,CAAC;EAC1C;EACAR,KAAK,CAACc,GAAG,CAAC7F,OAAO,EAAEkF,MAAM,CAAC;EAC1B,OAAOA,MAAM;AACf;;AAEA;AACA;AACA,SAASY,eAAeA,CAAC/C,IAAI,EAAE;EAC7B,IAAI;IACF/C,OAAO;IACP+F,QAAQ;IACRC,YAAY;IACZhD;EACF,CAAC,GAAGD,IAAI;EACR,MAAMkD,wBAAwB,GAAGF,QAAQ,KAAK,mBAAmB,GAAGjB,2BAA2B,CAAC9E,OAAO,EAAE,IAAI,CAACkG,EAAE,CAAC,GAAG,EAAE,CAACC,MAAM,CAACJ,QAAQ,CAAC;EACvI,MAAMK,iBAAiB,GAAG,CAAC,GAAGH,wBAAwB,EAAED,YAAY,CAAC;EACrE,MAAMK,qBAAqB,GAAGD,iBAAiB,CAAC,CAAC,CAAC;EAClD,MAAME,YAAY,GAAGF,iBAAiB,CAACG,MAAM,CAAC,CAACC,OAAO,EAAE/B,gBAAgB,KAAK;IAC3E,MAAM3D,IAAI,GAAG0D,iCAAiC,CAACxE,OAAO,EAAEyE,gBAAgB,EAAEzB,QAAQ,CAAC;IACnFwD,OAAO,CAACpE,GAAG,GAAGvD,GAAG,CAACiC,IAAI,CAACsB,GAAG,EAAEoE,OAAO,CAACpE,GAAG,CAAC;IACxCoE,OAAO,CAACC,KAAK,GAAG3H,GAAG,CAACgC,IAAI,CAAC2F,KAAK,EAAED,OAAO,CAACC,KAAK,CAAC;IAC9CD,OAAO,CAACE,MAAM,GAAG5H,GAAG,CAACgC,IAAI,CAAC4F,MAAM,EAAEF,OAAO,CAACE,MAAM,CAAC;IACjDF,OAAO,CAACrE,IAAI,GAAGtD,GAAG,CAACiC,IAAI,CAACqB,IAAI,EAAEqE,OAAO,CAACrE,IAAI,CAAC;IAC3C,OAAOqE,OAAO;EAChB,CAAC,EAAEhC,iCAAiC,CAACxE,OAAO,EAAEqG,qBAAqB,EAAErD,QAAQ,CAAC,CAAC;EAC/E,OAAO;IACL9C,KAAK,EAAEoG,YAAY,CAACG,KAAK,GAAGH,YAAY,CAACnE,IAAI;IAC7C/B,MAAM,EAAEkG,YAAY,CAACI,MAAM,GAAGJ,YAAY,CAAClE,GAAG;IAC9CpB,CAAC,EAAEsF,YAAY,CAACnE,IAAI;IACpBlB,CAAC,EAAEqF,YAAY,CAAClE;EAClB,CAAC;AACH;AAEA,SAASuE,aAAaA,CAAC3G,OAAO,EAAE;EAC9B,OAAOD,gBAAgB,CAACC,OAAO,CAAC;AAClC;AAEA,SAAS4G,6BAA6BA,CAAC5G,OAAO,EAAE+B,YAAY,EAAEiB,QAAQ,EAAE;EACtE,MAAMC,uBAAuB,GAAGhE,aAAa,CAAC8C,YAAY,CAAC;EAC3D,MAAMmB,eAAe,GAAG7D,kBAAkB,CAAC0C,YAAY,CAAC;EACxD,MAAMJ,OAAO,GAAGqB,QAAQ,KAAK,OAAO;EACpC,MAAMlC,IAAI,GAAGC,qBAAqB,CAACf,OAAO,EAAE,IAAI,EAAE2B,OAAO,EAAEI,YAAY,CAAC;EACxE,IAAIoB,MAAM,GAAG;IACXC,UAAU,EAAE,CAAC;IACbC,SAAS,EAAE;EACb,CAAC;EACD,MAAMC,OAAO,GAAG1E,YAAY,CAAC,CAAC,CAAC;EAC/B,IAAIqE,uBAAuB,IAAI,CAACA,uBAAuB,IAAI,CAACtB,OAAO,EAAE;IACnE,IAAIrC,WAAW,CAACyC,YAAY,CAAC,KAAK,MAAM,IAAIxC,iBAAiB,CAAC2D,eAAe,CAAC,EAAE;MAC9EC,MAAM,GAAG3D,aAAa,CAACuC,YAAY,CAAC;IACtC;IACA,IAAIkB,uBAAuB,EAAE;MAC3B,MAAMM,UAAU,GAAGxC,qBAAqB,CAACgB,YAAY,EAAE,IAAI,EAAEJ,OAAO,EAAEI,YAAY,CAAC;MACnFuB,OAAO,CAACtC,CAAC,GAAGuC,UAAU,CAACvC,CAAC,GAAGe,YAAY,CAACW,UAAU;MAClDY,OAAO,CAACrC,CAAC,GAAGsC,UAAU,CAACtC,CAAC,GAAGc,YAAY,CAACa,SAAS;IACnD,CAAC,MAAM,IAAIM,eAAe,EAAE;MAC1BI,OAAO,CAACtC,CAAC,GAAG2C,mBAAmB,CAACT,eAAe,CAAC;IAClD;EACF;EACA,OAAO;IACLlC,CAAC,EAAEF,IAAI,CAACqB,IAAI,GAAGgB,MAAM,CAACC,UAAU,GAAGE,OAAO,CAACtC,CAAC;IAC5CC,CAAC,EAAEH,IAAI,CAACsB,GAAG,GAAGe,MAAM,CAACE,SAAS,GAAGC,OAAO,CAACrC,CAAC;IAC1Cf,KAAK,EAAEY,IAAI,CAACZ,KAAK;IACjBE,MAAM,EAAEU,IAAI,CAACV;EACf,CAAC;AACH;AAEA,SAASyG,mBAAmBA,CAAC7G,OAAO,EAAE8G,QAAQ,EAAE;EAC9C,IAAI,CAAC7H,aAAa,CAACe,OAAO,CAAC,IAAIhB,gBAAgB,CAACgB,OAAO,CAAC,CAAC6E,QAAQ,KAAK,OAAO,EAAE;IAC7E,OAAO,IAAI;EACb;EACA,IAAIiC,QAAQ,EAAE;IACZ,OAAOA,QAAQ,CAAC9G,OAAO,CAAC;EAC1B;EACA,OAAOA,OAAO,CAAC+B,YAAY;AAC7B;;AAEA;AACA;AACA,SAASgF,eAAeA,CAAC/G,OAAO,EAAE8G,QAAQ,EAAE;EAC1C,MAAME,MAAM,GAAG7H,SAAS,CAACa,OAAO,CAAC;EACjC,IAAI,CAACf,aAAa,CAACe,OAAO,CAAC,EAAE;IAC3B,OAAOgH,MAAM;EACf;EACA,IAAIjF,YAAY,GAAG8E,mBAAmB,CAAC7G,OAAO,EAAE8G,QAAQ,CAAC;EACzD,OAAO/E,YAAY,IAAIlC,cAAc,CAACkC,YAAY,CAAC,IAAI/C,gBAAgB,CAAC+C,YAAY,CAAC,CAAC8C,QAAQ,KAAK,QAAQ,EAAE;IAC3G9C,YAAY,GAAG8E,mBAAmB,CAAC9E,YAAY,EAAE+E,QAAQ,CAAC;EAC5D;EACA,IAAI/E,YAAY,KAAKzC,WAAW,CAACyC,YAAY,CAAC,KAAK,MAAM,IAAIzC,WAAW,CAACyC,YAAY,CAAC,KAAK,MAAM,IAAI/C,gBAAgB,CAAC+C,YAAY,CAAC,CAAC8C,QAAQ,KAAK,QAAQ,IAAI,CAACjF,iBAAiB,CAACmC,YAAY,CAAC,CAAC,EAAE;IAC9L,OAAOiF,MAAM;EACf;EACA,OAAOjF,YAAY,IAAIjC,kBAAkB,CAACE,OAAO,CAAC,IAAIgH,MAAM;AAC9D;AAEA,MAAMC,eAAe,GAAG,eAAAA,CAAgBlE,IAAI,EAAE;EAC5C,IAAI;IACFmE,SAAS;IACTC,QAAQ;IACRnE;EACF,CAAC,GAAGD,IAAI;EACR,MAAMqE,iBAAiB,GAAG,IAAI,CAACL,eAAe,IAAIA,eAAe;EACjE,MAAMM,eAAe,GAAG,IAAI,CAACV,aAAa;EAC1C,OAAO;IACLO,SAAS,EAAEN,6BAA6B,CAACM,SAAS,EAAE,MAAME,iBAAiB,CAACD,QAAQ,CAAC,EAAEnE,QAAQ,CAAC;IAChGmE,QAAQ,EAAE;MACRnG,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE,CAAC;MACJ,IAAI,MAAMoG,eAAe,CAACF,QAAQ,CAAC;IACrC;EACF,CAAC;AACH,CAAC;AAED,SAASG,KAAKA,CAACtH,OAAO,EAAE;EACtB,OAAOhB,gBAAgB,CAACgB,OAAO,CAAC,CAACoE,SAAS,KAAK,KAAK;AACtD;AAEA,MAAMmD,QAAQ,GAAG;EACfzE,qDAAqD;EACrDzD,kBAAkB;EAClByG,eAAe;EACfiB,eAAe;EACfE,eAAe;EACfzD,cAAc;EACdmD,aAAa;EACb/F,QAAQ;EACR1B,SAAS;EACToI;AACF,CAAC;;AAED;AACA,SAASE,WAAWA,CAACxH,OAAO,EAAEyH,MAAM,EAAE;EACpC,IAAIC,EAAE,GAAG,IAAI;EACb,IAAIC,SAAS;EACb,MAAMC,IAAI,GAAGvI,kBAAkB,CAACW,OAAO,CAAC;EACxC,SAAS6H,OAAOA,CAAA,EAAG;IACjBC,YAAY,CAACH,SAAS,CAAC;IACvBD,EAAE,IAAIA,EAAE,CAACK,UAAU,CAAC,CAAC;IACrBL,EAAE,GAAG,IAAI;EACX;EACA,SAASM,OAAOA,CAACC,IAAI,EAAEC,SAAS,EAAE;IAChC,IAAID,IAAI,KAAK,KAAK,CAAC,EAAE;MACnBA,IAAI,GAAG,KAAK;IACd;IACA,IAAIC,SAAS,KAAK,KAAK,CAAC,EAAE;MACxBA,SAAS,GAAG,CAAC;IACf;IACAL,OAAO,CAAC,CAAC;IACT,MAAM;MACJ1F,IAAI;MACJC,GAAG;MACHlC,KAAK;MACLE;IACF,CAAC,GAAGJ,OAAO,CAACe,qBAAqB,CAAC,CAAC;IACnC,IAAI,CAACkH,IAAI,EAAE;MACTR,MAAM,CAAC,CAAC;IACV;IACA,IAAI,CAACvH,KAAK,IAAI,CAACE,MAAM,EAAE;MACrB;IACF;IACA,MAAM+H,QAAQ,GAAGpJ,KAAK,CAACqD,GAAG,CAAC;IAC3B,MAAMgG,UAAU,GAAGrJ,KAAK,CAAC6I,IAAI,CAAC3D,WAAW,IAAI9B,IAAI,GAAGjC,KAAK,CAAC,CAAC;IAC3D,MAAMmI,WAAW,GAAGtJ,KAAK,CAAC6I,IAAI,CAACzD,YAAY,IAAI/B,GAAG,GAAGhC,MAAM,CAAC,CAAC;IAC7D,MAAMkI,SAAS,GAAGvJ,KAAK,CAACoD,IAAI,CAAC;IAC7B,MAAMoG,UAAU,GAAG,CAACJ,QAAQ,GAAG,KAAK,GAAG,CAACC,UAAU,GAAG,KAAK,GAAG,CAACC,WAAW,GAAG,KAAK,GAAG,CAACC,SAAS,GAAG,IAAI;IACrG,MAAME,OAAO,GAAG;MACdD,UAAU;MACVL,SAAS,EAAErJ,GAAG,CAAC,CAAC,EAAEC,GAAG,CAAC,CAAC,EAAEoJ,SAAS,CAAC,CAAC,IAAI;IAC1C,CAAC;IACD,IAAIO,aAAa,GAAG,IAAI;IACxB,SAASC,aAAaA,CAACC,OAAO,EAAE;MAC9B,MAAMC,KAAK,GAAGD,OAAO,CAAC,CAAC,CAAC,CAACE,iBAAiB;MAC1C,IAAID,KAAK,KAAKV,SAAS,EAAE;QACvB,IAAI,CAACO,aAAa,EAAE;UAClB,OAAOT,OAAO,CAAC,CAAC;QAClB;QACA,IAAI,CAACY,KAAK,EAAE;UACVjB,SAAS,GAAGmB,UAAU,CAAC,MAAM;YAC3Bd,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC;UACtB,CAAC,EAAE,GAAG,CAAC;QACT,CAAC,MAAM;UACLA,OAAO,CAAC,KAAK,EAAEY,KAAK,CAAC;QACvB;MACF;MACAH,aAAa,GAAG,KAAK;IACvB;;IAEA;IACA;IACA,IAAI;MACFf,EAAE,GAAG,IAAIqB,oBAAoB,CAACL,aAAa,EAAE;QAC3C,GAAGF,OAAO;QACV;QACAZ,IAAI,EAAEA,IAAI,CAAC7D;MACb,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOiF,CAAC,EAAE;MACVtB,EAAE,GAAG,IAAIqB,oBAAoB,CAACL,aAAa,EAAEF,OAAO,CAAC;IACvD;IACAd,EAAE,CAACuB,OAAO,CAACjJ,OAAO,CAAC;EACrB;EACAgI,OAAO,CAAC,IAAI,CAAC;EACb,OAAOH,OAAO;AAChB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASqB,UAAUA,CAAChC,SAAS,EAAEC,QAAQ,EAAEgC,MAAM,EAAEX,OAAO,EAAE;EACxD,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,CAAC,CAAC;EACd;EACA,MAAM;IACJY,cAAc,GAAG,IAAI;IACrBC,cAAc,GAAG,IAAI;IACrBC,aAAa,GAAG,OAAOC,cAAc,KAAK,UAAU;IACpDC,WAAW,GAAG,OAAOT,oBAAoB,KAAK,UAAU;IACxDU,cAAc,GAAG;EACnB,CAAC,GAAGjB,OAAO;EACX,MAAMkB,WAAW,GAAGhJ,aAAa,CAACwG,SAAS,CAAC;EAC5C,MAAMyC,SAAS,GAAGP,cAAc,IAAIC,cAAc,GAAG,CAAC,IAAIK,WAAW,GAAGjK,oBAAoB,CAACiK,WAAW,CAAC,GAAG,EAAE,CAAC,EAAE,GAAGjK,oBAAoB,CAAC0H,QAAQ,CAAC,CAAC,GAAG,EAAE;EACxJwC,SAAS,CAACC,OAAO,CAAChE,QAAQ,IAAI;IAC5BwD,cAAc,IAAIxD,QAAQ,CAACiE,gBAAgB,CAAC,QAAQ,EAAEV,MAAM,EAAE;MAC5DW,OAAO,EAAE;IACX,CAAC,CAAC;IACFT,cAAc,IAAIzD,QAAQ,CAACiE,gBAAgB,CAAC,QAAQ,EAAEV,MAAM,CAAC;EAC/D,CAAC,CAAC;EACF,MAAMY,SAAS,GAAGL,WAAW,IAAIF,WAAW,GAAGhC,WAAW,CAACkC,WAAW,EAAEP,MAAM,CAAC,GAAG,IAAI;EACtF,IAAIa,cAAc,GAAG,CAAC,CAAC;EACvB,IAAIC,cAAc,GAAG,IAAI;EACzB,IAAIX,aAAa,EAAE;IACjBW,cAAc,GAAG,IAAIV,cAAc,CAACxG,IAAI,IAAI;MAC1C,IAAI,CAACmH,UAAU,CAAC,GAAGnH,IAAI;MACvB,IAAImH,UAAU,IAAIA,UAAU,CAACC,MAAM,KAAKT,WAAW,IAAIO,cAAc,EAAE;QACrE;QACA;QACAA,cAAc,CAACG,SAAS,CAACjD,QAAQ,CAAC;QAClCkD,oBAAoB,CAACL,cAAc,CAAC;QACpCA,cAAc,GAAGM,qBAAqB,CAAC,MAAM;UAC3CL,cAAc,IAAIA,cAAc,CAAChB,OAAO,CAAC9B,QAAQ,CAAC;QACpD,CAAC,CAAC;MACJ;MACAgC,MAAM,CAAC,CAAC;IACV,CAAC,CAAC;IACF,IAAIO,WAAW,IAAI,CAACD,cAAc,EAAE;MAClCQ,cAAc,CAAChB,OAAO,CAACS,WAAW,CAAC;IACrC;IACAO,cAAc,CAAChB,OAAO,CAAC9B,QAAQ,CAAC;EAClC;EACA,IAAIoD,OAAO;EACX,IAAIC,WAAW,GAAGf,cAAc,GAAG1I,qBAAqB,CAACmG,SAAS,CAAC,GAAG,IAAI;EAC1E,IAAIuC,cAAc,EAAE;IAClBgB,SAAS,CAAC,CAAC;EACb;EACA,SAASA,SAASA,CAAA,EAAG;IACnB,MAAMC,WAAW,GAAG3J,qBAAqB,CAACmG,SAAS,CAAC;IACpD,IAAIsD,WAAW,KAAKE,WAAW,CAAC1J,CAAC,KAAKwJ,WAAW,CAACxJ,CAAC,IAAI0J,WAAW,CAACzJ,CAAC,KAAKuJ,WAAW,CAACvJ,CAAC,IAAIyJ,WAAW,CAACxK,KAAK,KAAKsK,WAAW,CAACtK,KAAK,IAAIwK,WAAW,CAACtK,MAAM,KAAKoK,WAAW,CAACpK,MAAM,CAAC,EAAE;MAC/K+I,MAAM,CAAC,CAAC;IACV;IACAqB,WAAW,GAAGE,WAAW;IACzBH,OAAO,GAAGD,qBAAqB,CAACG,SAAS,CAAC;EAC5C;EACAtB,MAAM,CAAC,CAAC;EACR,OAAO,MAAM;IACXQ,SAAS,CAACC,OAAO,CAAChE,QAAQ,IAAI;MAC5BwD,cAAc,IAAIxD,QAAQ,CAAC+E,mBAAmB,CAAC,QAAQ,EAAExB,MAAM,CAAC;MAChEE,cAAc,IAAIzD,QAAQ,CAAC+E,mBAAmB,CAAC,QAAQ,EAAExB,MAAM,CAAC;IAClE,CAAC,CAAC;IACFY,SAAS,IAAIA,SAAS,CAAC,CAAC;IACxBE,cAAc,IAAIA,cAAc,CAAClC,UAAU,CAAC,CAAC;IAC7CkC,cAAc,GAAG,IAAI;IACrB,IAAIR,cAAc,EAAE;MAClBY,oBAAoB,CAACE,OAAO,CAAC;IAC/B;EACF,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMxM,eAAe,GAAGA,CAACmJ,SAAS,EAAEC,QAAQ,EAAEqB,OAAO,KAAK;EACxD;EACA;EACA;EACA,MAAMzD,KAAK,GAAG,IAAI6F,GAAG,CAAC,CAAC;EACvB,MAAMC,aAAa,GAAG;IACpBtD,QAAQ;IACR,GAAGiB;EACL,CAAC;EACD,MAAMsC,iBAAiB,GAAG;IACxB,GAAGD,aAAa,CAACtD,QAAQ;IACzBrB,EAAE,EAAEnB;EACN,CAAC;EACD,OAAO/G,iBAAiB,CAACkJ,SAAS,EAAEC,QAAQ,EAAE;IAC5C,GAAG0D,aAAa;IAChBtD,QAAQ,EAAEuD;EACZ,CAAC,CAAC;AACJ,CAAC;AAED,SAAS5B,UAAU,EAAEnL,eAAe,EAAEwJ,QAAQ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}