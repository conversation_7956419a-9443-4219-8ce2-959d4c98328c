{"ast": null, "code": "import { generateUtilityClass } from '../generateUtilityClass';\nimport { generateUtilityClasses } from '../generateUtilityClasses';\nexport function getSelectUtilityClass(slot) {\n  return generateUtilityClass('MuiSelect', slot);\n}\nexport const selectClasses = generateUtilityClasses('MuiSelect', ['root', 'button', 'listbox', 'popper', 'active', 'expanded', 'disabled', 'focusVisible']);", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getSelectUtilityClass", "slot", "selectClasses"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/base/Select/selectClasses.js"], "sourcesContent": ["import { generateUtilityClass } from '../generateUtilityClass';\nimport { generateUtilityClasses } from '../generateUtilityClasses';\nexport function getSelectUtilityClass(slot) {\n  return generateUtilityClass('MuiSelect', slot);\n}\nexport const selectClasses = generateUtilityClasses('MuiSelect', ['root', 'button', 'listbox', 'popper', 'active', 'expanded', 'disabled', 'focusVisible']);"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,yBAAyB;AAC9D,SAASC,sBAAsB,QAAQ,2BAA2B;AAClE,OAAO,SAASC,qBAAqBA,CAACC,IAAI,EAAE;EAC1C,OAAOH,oBAAoB,CAAC,WAAW,EAAEG,IAAI,CAAC;AAChD;AACA,OAAO,MAAMC,aAAa,GAAGH,sBAAsB,CAAC,WAAW,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,cAAc,CAAC,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}