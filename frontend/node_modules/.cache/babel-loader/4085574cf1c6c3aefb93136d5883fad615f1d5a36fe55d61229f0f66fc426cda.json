{"ast": null, "code": "function simpleClamp(val, min = Number.MIN_SAFE_INTEGER, max = Number.MAX_SAFE_INTEGER) {\n  return Math.max(min, Math.min(val, max));\n}\nexport function clamp(val, min = Number.MIN_SAFE_INTEGER, max = Number.MAX_SAFE_INTEGER, stepProp = NaN) {\n  if (Number.isNaN(stepProp)) {\n    return simpleClamp(val, min, max);\n  }\n  const step = stepProp || 1;\n  const remainder = val % step;\n  const positivity = Math.sign(remainder);\n  if (Math.abs(remainder) > step / 2) {\n    return simpleClamp(val + positivity * (step - Math.abs(remainder)), min, max);\n  }\n  return simpleClamp(val - positivity * Math.abs(remainder), min, max);\n}\nexport function isNumber(val) {\n  return typeof val === 'number' && !Number.isNaN(val) && Number.isFinite(val);\n}", "map": {"version": 3, "names": ["simpleClamp", "val", "min", "Number", "MIN_SAFE_INTEGER", "max", "MAX_SAFE_INTEGER", "Math", "clamp", "stepProp", "NaN", "isNaN", "step", "remainder", "positivity", "sign", "abs", "isNumber", "isFinite"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/base/unstable_useNumberInput/utils.js"], "sourcesContent": ["function simpleClamp(val, min = Number.MIN_SAFE_INTEGER, max = Number.MAX_SAFE_INTEGER) {\n  return Math.max(min, Math.min(val, max));\n}\nexport function clamp(val, min = Number.MIN_SAFE_INTEGER, max = Number.MAX_SAFE_INTEGER, stepProp = NaN) {\n  if (Number.isNaN(stepProp)) {\n    return simpleClamp(val, min, max);\n  }\n  const step = stepProp || 1;\n  const remainder = val % step;\n  const positivity = Math.sign(remainder);\n  if (Math.abs(remainder) > step / 2) {\n    return simpleClamp(val + positivity * (step - Math.abs(remainder)), min, max);\n  }\n  return simpleClamp(val - positivity * Math.abs(remainder), min, max);\n}\nexport function isNumber(val) {\n  return typeof val === 'number' && !Number.isNaN(val) && Number.isFinite(val);\n}"], "mappings": "AAAA,SAASA,WAAWA,CAACC,GAAG,EAAEC,GAAG,GAAGC,MAAM,CAACC,gBAAgB,EAAEC,GAAG,GAAGF,MAAM,CAACG,gBAAgB,EAAE;EACtF,OAAOC,IAAI,CAACF,GAAG,CAACH,GAAG,EAAEK,IAAI,CAACL,GAAG,CAACD,GAAG,EAAEI,GAAG,CAAC,CAAC;AAC1C;AACA,OAAO,SAASG,KAAKA,CAACP,GAAG,EAAEC,GAAG,GAAGC,MAAM,CAACC,gBAAgB,EAAEC,GAAG,GAAGF,MAAM,CAACG,gBAAgB,EAAEG,QAAQ,GAAGC,GAAG,EAAE;EACvG,IAAIP,MAAM,CAACQ,KAAK,CAACF,QAAQ,CAAC,EAAE;IAC1B,OAAOT,WAAW,CAACC,GAAG,EAAEC,GAAG,EAAEG,GAAG,CAAC;EACnC;EACA,MAAMO,IAAI,GAAGH,QAAQ,IAAI,CAAC;EAC1B,MAAMI,SAAS,GAAGZ,GAAG,GAAGW,IAAI;EAC5B,MAAME,UAAU,GAAGP,IAAI,CAACQ,IAAI,CAACF,SAAS,CAAC;EACvC,IAAIN,IAAI,CAACS,GAAG,CAACH,SAAS,CAAC,GAAGD,IAAI,GAAG,CAAC,EAAE;IAClC,OAAOZ,WAAW,CAACC,GAAG,GAAGa,UAAU,IAAIF,IAAI,GAAGL,IAAI,CAACS,GAAG,CAACH,SAAS,CAAC,CAAC,EAAEX,GAAG,EAAEG,GAAG,CAAC;EAC/E;EACA,OAAOL,WAAW,CAACC,GAAG,GAAGa,UAAU,GAAGP,IAAI,CAACS,GAAG,CAACH,SAAS,CAAC,EAAEX,GAAG,EAAEG,GAAG,CAAC;AACtE;AACA,OAAO,SAASY,QAAQA,CAAChB,GAAG,EAAE;EAC5B,OAAO,OAAOA,GAAG,KAAK,QAAQ,IAAI,CAACE,MAAM,CAACQ,KAAK,CAACV,GAAG,CAAC,IAAIE,MAAM,CAACe,QAAQ,CAACjB,GAAG,CAAC;AAC9E"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}