{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_useForkRef as useForkRef, unstable_useId as useId, unstable_useEnhancedEffect as useEnhancedEffect } from '@mui/utils';\nimport { useButton } from '../useButton';\nimport { SelectActionTypes } from './useSelect.types';\nimport { ListActionTypes, useList } from '../useList';\nimport { defaultOptionStringifier } from './defaultOptionStringifier';\nimport { useCompoundParent } from '../useCompound';\nimport { extractEventHandlers } from '../utils/extractEventHandlers';\nimport { selectReducer } from './selectReducer';\nimport { combineHooksSlotProps } from '../utils/combineHooksSlotProps';\n// visually hidden style based on https://webaim.org/techniques/css/invisiblecontent/\nconst visuallyHiddenStyle = {\n  clip: 'rect(1px, 1px, 1px, 1px)',\n  clipPath: 'inset(50%)',\n  height: '1px',\n  width: '1px',\n  margin: '-1px',\n  overflow: 'hidden',\n  padding: 0,\n  position: 'absolute',\n  left: '50%',\n  bottom: 0 // to display the native browser validation error at the bottom of the Select.\n};\nfunction defaultFormValueProvider(selectedOption) {\n  if (Array.isArray(selectedOption)) {\n    if (selectedOption.length === 0) {\n      return '';\n    }\n    return JSON.stringify(selectedOption.map(o => o.value));\n  }\n  if ((selectedOption == null ? void 0 : selectedOption.value) == null) {\n    return '';\n  }\n  if (typeof selectedOption.value === 'string' || typeof selectedOption.value === 'number') {\n    return selectedOption.value;\n  }\n  return JSON.stringify(selectedOption.value);\n}\nfunction preventDefault(event) {\n  event.preventDefault();\n}\n\n/**\n *\n * Demos:\n *\n * - [Select](https://mui.com/base-ui/react-select/#hooks)\n *\n * API:\n *\n * - [useSelect API](https://mui.com/base-ui/react-select/hooks-api/#use-select)\n */\nfunction useSelect(props) {\n  const {\n    areOptionsEqual,\n    buttonRef: buttonRefProp,\n    defaultOpen = false,\n    defaultValue: defaultValueProp,\n    disabled = false,\n    listboxId: listboxIdProp,\n    listboxRef: listboxRefProp,\n    multiple = false,\n    name,\n    required,\n    onChange,\n    onHighlightChange,\n    onOpenChange,\n    open: openProp,\n    options: optionsParam,\n    getOptionAsString = defaultOptionStringifier,\n    getSerializedValue = defaultFormValueProvider,\n    value: valueProp,\n    componentName = 'useSelect'\n  } = props;\n  const buttonRef = React.useRef(null);\n  const handleButtonRef = useForkRef(buttonRefProp, buttonRef);\n  const listboxRef = React.useRef(null);\n  const listboxId = useId(listboxIdProp);\n  let defaultValue;\n  if (valueProp === undefined && defaultValueProp === undefined) {\n    defaultValue = [];\n  } else if (defaultValueProp !== undefined) {\n    if (multiple) {\n      defaultValue = defaultValueProp;\n    } else {\n      defaultValue = defaultValueProp == null ? [] : [defaultValueProp];\n    }\n  }\n  const value = React.useMemo(() => {\n    if (valueProp !== undefined) {\n      if (multiple) {\n        return valueProp;\n      }\n      return valueProp == null ? [] : [valueProp];\n    }\n    return undefined;\n  }, [valueProp, multiple]);\n  const {\n    subitems,\n    contextValue: compoundComponentContextValue\n  } = useCompoundParent();\n  const options = React.useMemo(() => {\n    if (optionsParam != null) {\n      return new Map(optionsParam.map((option, index) => [option.value, {\n        value: option.value,\n        label: option.label,\n        disabled: option.disabled,\n        ref: /*#__PURE__*/React.createRef(),\n        id: `${listboxId}_${index}`\n      }]));\n    }\n    return subitems;\n  }, [optionsParam, subitems, listboxId]);\n  const handleListboxRef = useForkRef(listboxRefProp, listboxRef);\n  const {\n    getRootProps: getButtonRootProps,\n    active: buttonActive,\n    focusVisible: buttonFocusVisible,\n    rootRef: mergedButtonRef\n  } = useButton({\n    disabled,\n    rootRef: handleButtonRef\n  });\n  const optionValues = React.useMemo(() => Array.from(options.keys()), [options]);\n  const getOptionByValue = React.useCallback(valueToGet => {\n    // This can't be simply `options.get(valueToGet)` because of the `areOptionsEqual` prop.\n    // If it's provided, we assume that the user wants to compare the options by value.\n    if (areOptionsEqual !== undefined) {\n      const similarValue = optionValues.find(optionValue => areOptionsEqual(optionValue, valueToGet));\n      return options.get(similarValue);\n    }\n    return options.get(valueToGet);\n  }, [options, areOptionsEqual, optionValues]);\n  const isItemDisabled = React.useCallback(valueToCheck => {\n    var _option$disabled;\n    const option = getOptionByValue(valueToCheck);\n    return (_option$disabled = option == null ? void 0 : option.disabled) != null ? _option$disabled : false;\n  }, [getOptionByValue]);\n  const stringifyOption = React.useCallback(valueToCheck => {\n    const option = getOptionByValue(valueToCheck);\n    if (!option) {\n      return '';\n    }\n    return getOptionAsString(option);\n  }, [getOptionByValue, getOptionAsString]);\n  const controlledState = React.useMemo(() => ({\n    selectedValues: value,\n    open: openProp\n  }), [value, openProp]);\n  const getItemId = React.useCallback(itemValue => {\n    var _options$get;\n    return (_options$get = options.get(itemValue)) == null ? void 0 : _options$get.id;\n  }, [options]);\n  const handleSelectionChange = React.useCallback((event, newValues) => {\n    if (multiple) {\n      onChange == null || onChange(event, newValues);\n    } else {\n      var _newValues$;\n      onChange == null || onChange(event, (_newValues$ = newValues[0]) != null ? _newValues$ : null);\n    }\n  }, [multiple, onChange]);\n  const handleHighlightChange = React.useCallback((event, newValue) => {\n    onHighlightChange == null || onHighlightChange(event, newValue != null ? newValue : null);\n  }, [onHighlightChange]);\n  const handleStateChange = React.useCallback((event, field, fieldValue) => {\n    if (field === 'open') {\n      onOpenChange == null || onOpenChange(fieldValue);\n      if (fieldValue === false && (event == null ? void 0 : event.type) !== 'blur') {\n        var _buttonRef$current;\n        (_buttonRef$current = buttonRef.current) == null || _buttonRef$current.focus();\n      }\n    }\n  }, [onOpenChange]);\n  const useListParameters = {\n    getInitialState: () => {\n      var _defaultValue;\n      return {\n        highlightedValue: null,\n        selectedValues: (_defaultValue = defaultValue) != null ? _defaultValue : [],\n        open: defaultOpen\n      };\n    },\n    getItemId,\n    controlledProps: controlledState,\n    itemComparer: areOptionsEqual,\n    isItemDisabled,\n    rootRef: mergedButtonRef,\n    onChange: handleSelectionChange,\n    onHighlightChange: handleHighlightChange,\n    onStateChange: handleStateChange,\n    reducerActionContext: React.useMemo(() => ({\n      multiple\n    }), [multiple]),\n    items: optionValues,\n    getItemAsString: stringifyOption,\n    selectionMode: multiple ? 'multiple' : 'single',\n    stateReducer: selectReducer,\n    componentName\n  };\n  const {\n    dispatch,\n    getRootProps: getListboxRootProps,\n    contextValue: listContextValue,\n    state: {\n      open,\n      highlightedValue: highlightedOption,\n      selectedValues: selectedOptions\n    },\n    rootRef: mergedListRootRef\n  } = useList(useListParameters);\n  const createHandleButtonMouseDown = externalEventHandlers => event => {\n    var _externalEventHandler;\n    externalEventHandlers == null || (_externalEventHandler = externalEventHandlers.onMouseDown) == null || _externalEventHandler.call(externalEventHandlers, event);\n    if (!event.defaultMuiPrevented) {\n      const action = {\n        type: SelectActionTypes.buttonClick,\n        event\n      };\n      dispatch(action);\n    }\n  };\n  useEnhancedEffect(() => {\n    // Scroll to the currently highlighted option.\n    if (highlightedOption != null) {\n      var _getOptionByValue;\n      const optionRef = (_getOptionByValue = getOptionByValue(highlightedOption)) == null ? void 0 : _getOptionByValue.ref;\n      if (!listboxRef.current || !(optionRef != null && optionRef.current)) {\n        return;\n      }\n      const listboxClientRect = listboxRef.current.getBoundingClientRect();\n      const optionClientRect = optionRef.current.getBoundingClientRect();\n      if (optionClientRect.top < listboxClientRect.top) {\n        listboxRef.current.scrollTop -= listboxClientRect.top - optionClientRect.top;\n      } else if (optionClientRect.bottom > listboxClientRect.bottom) {\n        listboxRef.current.scrollTop += optionClientRect.bottom - listboxClientRect.bottom;\n      }\n    }\n  }, [highlightedOption, getOptionByValue]);\n  const getOptionMetadata = React.useCallback(optionValue => getOptionByValue(optionValue), [getOptionByValue]);\n  const getSelectTriggerProps = (otherHandlers = {}) => {\n    return _extends({}, otherHandlers, {\n      onMouseDown: createHandleButtonMouseDown(otherHandlers),\n      ref: mergedListRootRef,\n      role: 'combobox',\n      'aria-expanded': open,\n      'aria-controls': listboxId\n    });\n  };\n  const getButtonProps = (externalProps = {}) => {\n    const externalEventHandlers = extractEventHandlers(externalProps);\n    const listboxAndButtonProps = combineHooksSlotProps(getButtonRootProps, getListboxRootProps);\n    const combinedProps = combineHooksSlotProps(listboxAndButtonProps, getSelectTriggerProps);\n    return _extends({}, externalProps, combinedProps(externalEventHandlers));\n  };\n  const getListboxProps = (externalProps = {}) => {\n    return _extends({}, externalProps, {\n      id: listboxId,\n      role: 'listbox',\n      'aria-multiselectable': multiple ? 'true' : undefined,\n      ref: handleListboxRef,\n      onMouseDown: preventDefault // to prevent the button from losing focus when interacting with the listbox\n    });\n  };\n  React.useDebugValue({\n    selectedOptions,\n    highlightedOption,\n    open\n  });\n  const contextValue = React.useMemo(() => _extends({}, listContextValue, compoundComponentContextValue), [listContextValue, compoundComponentContextValue]);\n  let selectValue;\n  if (props.multiple) {\n    selectValue = selectedOptions;\n  } else {\n    selectValue = selectedOptions.length > 0 ? selectedOptions[0] : null;\n  }\n  let selectedOptionsMetadata;\n  if (multiple) {\n    selectedOptionsMetadata = selectValue.map(v => getOptionMetadata(v)).filter(o => o !== undefined);\n  } else {\n    var _getOptionMetadata;\n    selectedOptionsMetadata = (_getOptionMetadata = getOptionMetadata(selectValue)) != null ? _getOptionMetadata : null;\n  }\n  const createHandleHiddenInputChange = externalEventHandlers => event => {\n    var _externalEventHandler2;\n    externalEventHandlers == null || (_externalEventHandler2 = externalEventHandlers.onChange) == null || _externalEventHandler2.call(externalEventHandlers, event);\n    if (event.defaultMuiPrevented) {\n      return;\n    }\n    const option = options.get(event.target.value);\n\n    // support autofill\n    if (event.target.value === '') {\n      dispatch({\n        type: ListActionTypes.clearSelection\n      });\n    } else if (option !== undefined) {\n      dispatch({\n        type: SelectActionTypes.browserAutoFill,\n        item: option.value,\n        event\n      });\n    }\n  };\n  const getHiddenInputProps = (externalProps = {}) => {\n    const externalEventHandlers = extractEventHandlers(externalProps);\n    return _extends({\n      name,\n      tabIndex: -1,\n      'aria-hidden': true,\n      required: required ? true : undefined,\n      value: getSerializedValue(selectedOptionsMetadata),\n      style: visuallyHiddenStyle\n    }, externalProps, {\n      onChange: createHandleHiddenInputChange(externalEventHandlers)\n    });\n  };\n  return {\n    buttonActive,\n    buttonFocusVisible,\n    buttonRef: mergedButtonRef,\n    contextValue,\n    disabled,\n    dispatch,\n    getButtonProps,\n    getHiddenInputProps,\n    getListboxProps,\n    getOptionMetadata,\n    listboxRef: mergedListRootRef,\n    open,\n    options: optionValues,\n    value: selectValue,\n    highlightedOption\n  };\n}\nexport { useSelect };", "map": {"version": 3, "names": ["_extends", "React", "unstable_useForkRef", "useForkRef", "unstable_useId", "useId", "unstable_useEnhancedEffect", "useEnhancedEffect", "useButton", "SelectActionTypes", "ListActionTypes", "useList", "defaultOptionStringifier", "useCompoundParent", "extractEventHandlers", "selectReducer", "combineHooksSlotProps", "visuallyHiddenStyle", "clip", "clipPath", "height", "width", "margin", "overflow", "padding", "position", "left", "bottom", "defaultFormValueProvider", "selectedOption", "Array", "isArray", "length", "JSON", "stringify", "map", "o", "value", "preventDefault", "event", "useSelect", "props", "areOptionsEqual", "buttonRef", "buttonRefProp", "defaultOpen", "defaultValue", "defaultValueProp", "disabled", "listboxId", "listboxIdProp", "listboxRef", "listboxRefProp", "multiple", "name", "required", "onChange", "onHighlightChange", "onOpenChange", "open", "openProp", "options", "optionsParam", "getOptionAsString", "getSerializedValue", "valueProp", "componentName", "useRef", "handleButtonRef", "undefined", "useMemo", "subitems", "contextValue", "compoundComponentContextValue", "Map", "option", "index", "label", "ref", "createRef", "id", "handleListboxRef", "getRootProps", "getButtonRootProps", "active", "buttonActive", "focusVisible", "buttonFocusVisible", "rootRef", "mergedButtonRef", "optionValues", "from", "keys", "getOptionByValue", "useCallback", "valueToGet", "similarValue", "find", "optionValue", "get", "isItemDisabled", "valueToCheck", "_option$disabled", "stringifyOption", "controlledState", "<PERSON><PERSON><PERSON><PERSON>", "getItemId", "itemValue", "_options$get", "handleSelectionChange", "newValues", "_newValues$", "handleHighlightChange", "newValue", "handleStateChange", "field", "fieldValue", "type", "_buttonRef$current", "current", "focus", "useListParameters", "getInitialState", "_defaultValue", "highlightedValue", "controlledProps", "itemComparer", "onStateChange", "reducerActionContext", "items", "getItemAsString", "selectionMode", "stateReducer", "dispatch", "getListboxRootProps", "listContextValue", "state", "highlightedOption", "selectedOptions", "mergedListRootRef", "createHandleButtonMouseDown", "externalEventHandlers", "_externalEventHandler", "onMouseDown", "call", "defaultMuiPrevented", "action", "buttonClick", "_getOptionByValue", "optionRef", "listboxClientRect", "getBoundingClientRect", "optionClientRect", "top", "scrollTop", "getOptionMetadata", "getSelectTriggerProps", "otherHandlers", "role", "getButtonProps", "externalProps", "listboxAndButtonProps", "combinedProps", "getListboxProps", "useDebugValue", "selectValue", "selectedOptionsMetadata", "v", "filter", "_getOptionMetadata", "createHandleHiddenInputChange", "_externalEventHandler2", "target", "clearSelection", "browserAutoFill", "item", "getHiddenInputProps", "tabIndex", "style"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/base/useSelect/useSelect.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_useForkRef as useForkRef, unstable_useId as useId, unstable_useEnhancedEffect as useEnhancedEffect } from '@mui/utils';\nimport { useButton } from '../useButton';\nimport { SelectActionTypes } from './useSelect.types';\nimport { ListActionTypes, useList } from '../useList';\nimport { defaultOptionStringifier } from './defaultOptionStringifier';\nimport { useCompoundParent } from '../useCompound';\nimport { extractEventHandlers } from '../utils/extractEventHandlers';\nimport { selectReducer } from './selectReducer';\nimport { combineHooksSlotProps } from '../utils/combineHooksSlotProps';\n// visually hidden style based on https://webaim.org/techniques/css/invisiblecontent/\nconst visuallyHiddenStyle = {\n  clip: 'rect(1px, 1px, 1px, 1px)',\n  clipPath: 'inset(50%)',\n  height: '1px',\n  width: '1px',\n  margin: '-1px',\n  overflow: 'hidden',\n  padding: 0,\n  position: 'absolute',\n  left: '50%',\n  bottom: 0 // to display the native browser validation error at the bottom of the Select.\n};\nfunction defaultFormValueProvider(selectedOption) {\n  if (Array.isArray(selectedOption)) {\n    if (selectedOption.length === 0) {\n      return '';\n    }\n    return JSON.stringify(selectedOption.map(o => o.value));\n  }\n  if ((selectedOption == null ? void 0 : selectedOption.value) == null) {\n    return '';\n  }\n  if (typeof selectedOption.value === 'string' || typeof selectedOption.value === 'number') {\n    return selectedOption.value;\n  }\n  return JSON.stringify(selectedOption.value);\n}\nfunction preventDefault(event) {\n  event.preventDefault();\n}\n\n/**\n *\n * Demos:\n *\n * - [Select](https://mui.com/base-ui/react-select/#hooks)\n *\n * API:\n *\n * - [useSelect API](https://mui.com/base-ui/react-select/hooks-api/#use-select)\n */\nfunction useSelect(props) {\n  const {\n    areOptionsEqual,\n    buttonRef: buttonRefProp,\n    defaultOpen = false,\n    defaultValue: defaultValueProp,\n    disabled = false,\n    listboxId: listboxIdProp,\n    listboxRef: listboxRefProp,\n    multiple = false,\n    name,\n    required,\n    onChange,\n    onHighlightChange,\n    onOpenChange,\n    open: openProp,\n    options: optionsParam,\n    getOptionAsString = defaultOptionStringifier,\n    getSerializedValue = defaultFormValueProvider,\n    value: valueProp,\n    componentName = 'useSelect'\n  } = props;\n  const buttonRef = React.useRef(null);\n  const handleButtonRef = useForkRef(buttonRefProp, buttonRef);\n  const listboxRef = React.useRef(null);\n  const listboxId = useId(listboxIdProp);\n  let defaultValue;\n  if (valueProp === undefined && defaultValueProp === undefined) {\n    defaultValue = [];\n  } else if (defaultValueProp !== undefined) {\n    if (multiple) {\n      defaultValue = defaultValueProp;\n    } else {\n      defaultValue = defaultValueProp == null ? [] : [defaultValueProp];\n    }\n  }\n  const value = React.useMemo(() => {\n    if (valueProp !== undefined) {\n      if (multiple) {\n        return valueProp;\n      }\n      return valueProp == null ? [] : [valueProp];\n    }\n    return undefined;\n  }, [valueProp, multiple]);\n  const {\n    subitems,\n    contextValue: compoundComponentContextValue\n  } = useCompoundParent();\n  const options = React.useMemo(() => {\n    if (optionsParam != null) {\n      return new Map(optionsParam.map((option, index) => [option.value, {\n        value: option.value,\n        label: option.label,\n        disabled: option.disabled,\n        ref: /*#__PURE__*/React.createRef(),\n        id: `${listboxId}_${index}`\n      }]));\n    }\n    return subitems;\n  }, [optionsParam, subitems, listboxId]);\n  const handleListboxRef = useForkRef(listboxRefProp, listboxRef);\n  const {\n    getRootProps: getButtonRootProps,\n    active: buttonActive,\n    focusVisible: buttonFocusVisible,\n    rootRef: mergedButtonRef\n  } = useButton({\n    disabled,\n    rootRef: handleButtonRef\n  });\n  const optionValues = React.useMemo(() => Array.from(options.keys()), [options]);\n  const getOptionByValue = React.useCallback(valueToGet => {\n    // This can't be simply `options.get(valueToGet)` because of the `areOptionsEqual` prop.\n    // If it's provided, we assume that the user wants to compare the options by value.\n    if (areOptionsEqual !== undefined) {\n      const similarValue = optionValues.find(optionValue => areOptionsEqual(optionValue, valueToGet));\n      return options.get(similarValue);\n    }\n    return options.get(valueToGet);\n  }, [options, areOptionsEqual, optionValues]);\n  const isItemDisabled = React.useCallback(valueToCheck => {\n    var _option$disabled;\n    const option = getOptionByValue(valueToCheck);\n    return (_option$disabled = option == null ? void 0 : option.disabled) != null ? _option$disabled : false;\n  }, [getOptionByValue]);\n  const stringifyOption = React.useCallback(valueToCheck => {\n    const option = getOptionByValue(valueToCheck);\n    if (!option) {\n      return '';\n    }\n    return getOptionAsString(option);\n  }, [getOptionByValue, getOptionAsString]);\n  const controlledState = React.useMemo(() => ({\n    selectedValues: value,\n    open: openProp\n  }), [value, openProp]);\n  const getItemId = React.useCallback(itemValue => {\n    var _options$get;\n    return (_options$get = options.get(itemValue)) == null ? void 0 : _options$get.id;\n  }, [options]);\n  const handleSelectionChange = React.useCallback((event, newValues) => {\n    if (multiple) {\n      onChange == null || onChange(event, newValues);\n    } else {\n      var _newValues$;\n      onChange == null || onChange(event, (_newValues$ = newValues[0]) != null ? _newValues$ : null);\n    }\n  }, [multiple, onChange]);\n  const handleHighlightChange = React.useCallback((event, newValue) => {\n    onHighlightChange == null || onHighlightChange(event, newValue != null ? newValue : null);\n  }, [onHighlightChange]);\n  const handleStateChange = React.useCallback((event, field, fieldValue) => {\n    if (field === 'open') {\n      onOpenChange == null || onOpenChange(fieldValue);\n      if (fieldValue === false && (event == null ? void 0 : event.type) !== 'blur') {\n        var _buttonRef$current;\n        (_buttonRef$current = buttonRef.current) == null || _buttonRef$current.focus();\n      }\n    }\n  }, [onOpenChange]);\n  const useListParameters = {\n    getInitialState: () => {\n      var _defaultValue;\n      return {\n        highlightedValue: null,\n        selectedValues: (_defaultValue = defaultValue) != null ? _defaultValue : [],\n        open: defaultOpen\n      };\n    },\n    getItemId,\n    controlledProps: controlledState,\n    itemComparer: areOptionsEqual,\n    isItemDisabled,\n    rootRef: mergedButtonRef,\n    onChange: handleSelectionChange,\n    onHighlightChange: handleHighlightChange,\n    onStateChange: handleStateChange,\n    reducerActionContext: React.useMemo(() => ({\n      multiple\n    }), [multiple]),\n    items: optionValues,\n    getItemAsString: stringifyOption,\n    selectionMode: multiple ? 'multiple' : 'single',\n    stateReducer: selectReducer,\n    componentName\n  };\n  const {\n    dispatch,\n    getRootProps: getListboxRootProps,\n    contextValue: listContextValue,\n    state: {\n      open,\n      highlightedValue: highlightedOption,\n      selectedValues: selectedOptions\n    },\n    rootRef: mergedListRootRef\n  } = useList(useListParameters);\n  const createHandleButtonMouseDown = externalEventHandlers => event => {\n    var _externalEventHandler;\n    externalEventHandlers == null || (_externalEventHandler = externalEventHandlers.onMouseDown) == null || _externalEventHandler.call(externalEventHandlers, event);\n    if (!event.defaultMuiPrevented) {\n      const action = {\n        type: SelectActionTypes.buttonClick,\n        event\n      };\n      dispatch(action);\n    }\n  };\n  useEnhancedEffect(() => {\n    // Scroll to the currently highlighted option.\n    if (highlightedOption != null) {\n      var _getOptionByValue;\n      const optionRef = (_getOptionByValue = getOptionByValue(highlightedOption)) == null ? void 0 : _getOptionByValue.ref;\n      if (!listboxRef.current || !(optionRef != null && optionRef.current)) {\n        return;\n      }\n      const listboxClientRect = listboxRef.current.getBoundingClientRect();\n      const optionClientRect = optionRef.current.getBoundingClientRect();\n      if (optionClientRect.top < listboxClientRect.top) {\n        listboxRef.current.scrollTop -= listboxClientRect.top - optionClientRect.top;\n      } else if (optionClientRect.bottom > listboxClientRect.bottom) {\n        listboxRef.current.scrollTop += optionClientRect.bottom - listboxClientRect.bottom;\n      }\n    }\n  }, [highlightedOption, getOptionByValue]);\n  const getOptionMetadata = React.useCallback(optionValue => getOptionByValue(optionValue), [getOptionByValue]);\n  const getSelectTriggerProps = (otherHandlers = {}) => {\n    return _extends({}, otherHandlers, {\n      onMouseDown: createHandleButtonMouseDown(otherHandlers),\n      ref: mergedListRootRef,\n      role: 'combobox',\n      'aria-expanded': open,\n      'aria-controls': listboxId\n    });\n  };\n  const getButtonProps = (externalProps = {}) => {\n    const externalEventHandlers = extractEventHandlers(externalProps);\n    const listboxAndButtonProps = combineHooksSlotProps(getButtonRootProps, getListboxRootProps);\n    const combinedProps = combineHooksSlotProps(listboxAndButtonProps, getSelectTriggerProps);\n    return _extends({}, externalProps, combinedProps(externalEventHandlers));\n  };\n  const getListboxProps = (externalProps = {}) => {\n    return _extends({}, externalProps, {\n      id: listboxId,\n      role: 'listbox',\n      'aria-multiselectable': multiple ? 'true' : undefined,\n      ref: handleListboxRef,\n      onMouseDown: preventDefault // to prevent the button from losing focus when interacting with the listbox\n    });\n  };\n  React.useDebugValue({\n    selectedOptions,\n    highlightedOption,\n    open\n  });\n  const contextValue = React.useMemo(() => _extends({}, listContextValue, compoundComponentContextValue), [listContextValue, compoundComponentContextValue]);\n  let selectValue;\n  if (props.multiple) {\n    selectValue = selectedOptions;\n  } else {\n    selectValue = selectedOptions.length > 0 ? selectedOptions[0] : null;\n  }\n  let selectedOptionsMetadata;\n  if (multiple) {\n    selectedOptionsMetadata = selectValue.map(v => getOptionMetadata(v)).filter(o => o !== undefined);\n  } else {\n    var _getOptionMetadata;\n    selectedOptionsMetadata = (_getOptionMetadata = getOptionMetadata(selectValue)) != null ? _getOptionMetadata : null;\n  }\n  const createHandleHiddenInputChange = externalEventHandlers => event => {\n    var _externalEventHandler2;\n    externalEventHandlers == null || (_externalEventHandler2 = externalEventHandlers.onChange) == null || _externalEventHandler2.call(externalEventHandlers, event);\n    if (event.defaultMuiPrevented) {\n      return;\n    }\n    const option = options.get(event.target.value);\n\n    // support autofill\n    if (event.target.value === '') {\n      dispatch({\n        type: ListActionTypes.clearSelection\n      });\n    } else if (option !== undefined) {\n      dispatch({\n        type: SelectActionTypes.browserAutoFill,\n        item: option.value,\n        event\n      });\n    }\n  };\n  const getHiddenInputProps = (externalProps = {}) => {\n    const externalEventHandlers = extractEventHandlers(externalProps);\n    return _extends({\n      name,\n      tabIndex: -1,\n      'aria-hidden': true,\n      required: required ? true : undefined,\n      value: getSerializedValue(selectedOptionsMetadata),\n      style: visuallyHiddenStyle\n    }, externalProps, {\n      onChange: createHandleHiddenInputChange(externalEventHandlers)\n    });\n  };\n  return {\n    buttonActive,\n    buttonFocusVisible,\n    buttonRef: mergedButtonRef,\n    contextValue,\n    disabled,\n    dispatch,\n    getButtonProps,\n    getHiddenInputProps,\n    getListboxProps,\n    getOptionMetadata,\n    listboxRef: mergedListRootRef,\n    open,\n    options: optionValues,\n    value: selectValue,\n    highlightedOption\n  };\n}\nexport { useSelect };"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,mBAAmB,IAAIC,UAAU,EAAEC,cAAc,IAAIC,KAAK,EAAEC,0BAA0B,IAAIC,iBAAiB,QAAQ,YAAY;AACxI,SAASC,SAAS,QAAQ,cAAc;AACxC,SAASC,iBAAiB,QAAQ,mBAAmB;AACrD,SAASC,eAAe,EAAEC,OAAO,QAAQ,YAAY;AACrD,SAASC,wBAAwB,QAAQ,4BAA4B;AACrE,SAASC,iBAAiB,QAAQ,gBAAgB;AAClD,SAASC,oBAAoB,QAAQ,+BAA+B;AACpE,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,qBAAqB,QAAQ,gCAAgC;AACtE;AACA,MAAMC,mBAAmB,GAAG;EAC1BC,IAAI,EAAE,0BAA0B;EAChCC,QAAQ,EAAE,YAAY;EACtBC,MAAM,EAAE,KAAK;EACbC,KAAK,EAAE,KAAK;EACZC,MAAM,EAAE,MAAM;EACdC,QAAQ,EAAE,QAAQ;EAClBC,OAAO,EAAE,CAAC;EACVC,QAAQ,EAAE,UAAU;EACpBC,IAAI,EAAE,KAAK;EACXC,MAAM,EAAE,CAAC,CAAC;AACZ,CAAC;AACD,SAASC,wBAAwBA,CAACC,cAAc,EAAE;EAChD,IAAIC,KAAK,CAACC,OAAO,CAACF,cAAc,CAAC,EAAE;IACjC,IAAIA,cAAc,CAACG,MAAM,KAAK,CAAC,EAAE;MAC/B,OAAO,EAAE;IACX;IACA,OAAOC,IAAI,CAACC,SAAS,CAACL,cAAc,CAACM,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,KAAK,CAAC,CAAC;EACzD;EACA,IAAI,CAACR,cAAc,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACQ,KAAK,KAAK,IAAI,EAAE;IACpE,OAAO,EAAE;EACX;EACA,IAAI,OAAOR,cAAc,CAACQ,KAAK,KAAK,QAAQ,IAAI,OAAOR,cAAc,CAACQ,KAAK,KAAK,QAAQ,EAAE;IACxF,OAAOR,cAAc,CAACQ,KAAK;EAC7B;EACA,OAAOJ,IAAI,CAACC,SAAS,CAACL,cAAc,CAACQ,KAAK,CAAC;AAC7C;AACA,SAASC,cAAcA,CAACC,KAAK,EAAE;EAC7BA,KAAK,CAACD,cAAc,CAAC,CAAC;AACxB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,SAASA,CAACC,KAAK,EAAE;EACxB,MAAM;IACJC,eAAe;IACfC,SAAS,EAAEC,aAAa;IACxBC,WAAW,GAAG,KAAK;IACnBC,YAAY,EAAEC,gBAAgB;IAC9BC,QAAQ,GAAG,KAAK;IAChBC,SAAS,EAAEC,aAAa;IACxBC,UAAU,EAAEC,cAAc;IAC1BC,QAAQ,GAAG,KAAK;IAChBC,IAAI;IACJC,QAAQ;IACRC,QAAQ;IACRC,iBAAiB;IACjBC,YAAY;IACZC,IAAI,EAAEC,QAAQ;IACdC,OAAO,EAAEC,YAAY;IACrBC,iBAAiB,GAAGnD,wBAAwB;IAC5CoD,kBAAkB,GAAGpC,wBAAwB;IAC7CS,KAAK,EAAE4B,SAAS;IAChBC,aAAa,GAAG;EAClB,CAAC,GAAGzB,KAAK;EACT,MAAME,SAAS,GAAG1C,KAAK,CAACkE,MAAM,CAAC,IAAI,CAAC;EACpC,MAAMC,eAAe,GAAGjE,UAAU,CAACyC,aAAa,EAAED,SAAS,CAAC;EAC5D,MAAMQ,UAAU,GAAGlD,KAAK,CAACkE,MAAM,CAAC,IAAI,CAAC;EACrC,MAAMlB,SAAS,GAAG5C,KAAK,CAAC6C,aAAa,CAAC;EACtC,IAAIJ,YAAY;EAChB,IAAImB,SAAS,KAAKI,SAAS,IAAItB,gBAAgB,KAAKsB,SAAS,EAAE;IAC7DvB,YAAY,GAAG,EAAE;EACnB,CAAC,MAAM,IAAIC,gBAAgB,KAAKsB,SAAS,EAAE;IACzC,IAAIhB,QAAQ,EAAE;MACZP,YAAY,GAAGC,gBAAgB;IACjC,CAAC,MAAM;MACLD,YAAY,GAAGC,gBAAgB,IAAI,IAAI,GAAG,EAAE,GAAG,CAACA,gBAAgB,CAAC;IACnE;EACF;EACA,MAAMV,KAAK,GAAGpC,KAAK,CAACqE,OAAO,CAAC,MAAM;IAChC,IAAIL,SAAS,KAAKI,SAAS,EAAE;MAC3B,IAAIhB,QAAQ,EAAE;QACZ,OAAOY,SAAS;MAClB;MACA,OAAOA,SAAS,IAAI,IAAI,GAAG,EAAE,GAAG,CAACA,SAAS,CAAC;IAC7C;IACA,OAAOI,SAAS;EAClB,CAAC,EAAE,CAACJ,SAAS,EAAEZ,QAAQ,CAAC,CAAC;EACzB,MAAM;IACJkB,QAAQ;IACRC,YAAY,EAAEC;EAChB,CAAC,GAAG5D,iBAAiB,CAAC,CAAC;EACvB,MAAMgD,OAAO,GAAG5D,KAAK,CAACqE,OAAO,CAAC,MAAM;IAClC,IAAIR,YAAY,IAAI,IAAI,EAAE;MACxB,OAAO,IAAIY,GAAG,CAACZ,YAAY,CAAC3B,GAAG,CAAC,CAACwC,MAAM,EAAEC,KAAK,KAAK,CAACD,MAAM,CAACtC,KAAK,EAAE;QAChEA,KAAK,EAAEsC,MAAM,CAACtC,KAAK;QACnBwC,KAAK,EAAEF,MAAM,CAACE,KAAK;QACnB7B,QAAQ,EAAE2B,MAAM,CAAC3B,QAAQ;QACzB8B,GAAG,EAAE,aAAa7E,KAAK,CAAC8E,SAAS,CAAC,CAAC;QACnCC,EAAE,EAAG,GAAE/B,SAAU,IAAG2B,KAAM;MAC5B,CAAC,CAAC,CAAC,CAAC;IACN;IACA,OAAOL,QAAQ;EACjB,CAAC,EAAE,CAACT,YAAY,EAAES,QAAQ,EAAEtB,SAAS,CAAC,CAAC;EACvC,MAAMgC,gBAAgB,GAAG9E,UAAU,CAACiD,cAAc,EAAED,UAAU,CAAC;EAC/D,MAAM;IACJ+B,YAAY,EAAEC,kBAAkB;IAChCC,MAAM,EAAEC,YAAY;IACpBC,YAAY,EAAEC,kBAAkB;IAChCC,OAAO,EAAEC;EACX,CAAC,GAAGjF,SAAS,CAAC;IACZwC,QAAQ;IACRwC,OAAO,EAAEpB;EACX,CAAC,CAAC;EACF,MAAMsB,YAAY,GAAGzF,KAAK,CAACqE,OAAO,CAAC,MAAMxC,KAAK,CAAC6D,IAAI,CAAC9B,OAAO,CAAC+B,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC/B,OAAO,CAAC,CAAC;EAC/E,MAAMgC,gBAAgB,GAAG5F,KAAK,CAAC6F,WAAW,CAACC,UAAU,IAAI;IACvD;IACA;IACA,IAAIrD,eAAe,KAAK2B,SAAS,EAAE;MACjC,MAAM2B,YAAY,GAAGN,YAAY,CAACO,IAAI,CAACC,WAAW,IAAIxD,eAAe,CAACwD,WAAW,EAAEH,UAAU,CAAC,CAAC;MAC/F,OAAOlC,OAAO,CAACsC,GAAG,CAACH,YAAY,CAAC;IAClC;IACA,OAAOnC,OAAO,CAACsC,GAAG,CAACJ,UAAU,CAAC;EAChC,CAAC,EAAE,CAAClC,OAAO,EAAEnB,eAAe,EAAEgD,YAAY,CAAC,CAAC;EAC5C,MAAMU,cAAc,GAAGnG,KAAK,CAAC6F,WAAW,CAACO,YAAY,IAAI;IACvD,IAAIC,gBAAgB;IACpB,MAAM3B,MAAM,GAAGkB,gBAAgB,CAACQ,YAAY,CAAC;IAC7C,OAAO,CAACC,gBAAgB,GAAG3B,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC3B,QAAQ,KAAK,IAAI,GAAGsD,gBAAgB,GAAG,KAAK;EAC1G,CAAC,EAAE,CAACT,gBAAgB,CAAC,CAAC;EACtB,MAAMU,eAAe,GAAGtG,KAAK,CAAC6F,WAAW,CAACO,YAAY,IAAI;IACxD,MAAM1B,MAAM,GAAGkB,gBAAgB,CAACQ,YAAY,CAAC;IAC7C,IAAI,CAAC1B,MAAM,EAAE;MACX,OAAO,EAAE;IACX;IACA,OAAOZ,iBAAiB,CAACY,MAAM,CAAC;EAClC,CAAC,EAAE,CAACkB,gBAAgB,EAAE9B,iBAAiB,CAAC,CAAC;EACzC,MAAMyC,eAAe,GAAGvG,KAAK,CAACqE,OAAO,CAAC,OAAO;IAC3CmC,cAAc,EAAEpE,KAAK;IACrBsB,IAAI,EAAEC;EACR,CAAC,CAAC,EAAE,CAACvB,KAAK,EAAEuB,QAAQ,CAAC,CAAC;EACtB,MAAM8C,SAAS,GAAGzG,KAAK,CAAC6F,WAAW,CAACa,SAAS,IAAI;IAC/C,IAAIC,YAAY;IAChB,OAAO,CAACA,YAAY,GAAG/C,OAAO,CAACsC,GAAG,CAACQ,SAAS,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGC,YAAY,CAAC5B,EAAE;EACnF,CAAC,EAAE,CAACnB,OAAO,CAAC,CAAC;EACb,MAAMgD,qBAAqB,GAAG5G,KAAK,CAAC6F,WAAW,CAAC,CAACvD,KAAK,EAAEuE,SAAS,KAAK;IACpE,IAAIzD,QAAQ,EAAE;MACZG,QAAQ,IAAI,IAAI,IAAIA,QAAQ,CAACjB,KAAK,EAAEuE,SAAS,CAAC;IAChD,CAAC,MAAM;MACL,IAAIC,WAAW;MACfvD,QAAQ,IAAI,IAAI,IAAIA,QAAQ,CAACjB,KAAK,EAAE,CAACwE,WAAW,GAAGD,SAAS,CAAC,CAAC,CAAC,KAAK,IAAI,GAAGC,WAAW,GAAG,IAAI,CAAC;IAChG;EACF,CAAC,EAAE,CAAC1D,QAAQ,EAAEG,QAAQ,CAAC,CAAC;EACxB,MAAMwD,qBAAqB,GAAG/G,KAAK,CAAC6F,WAAW,CAAC,CAACvD,KAAK,EAAE0E,QAAQ,KAAK;IACnExD,iBAAiB,IAAI,IAAI,IAAIA,iBAAiB,CAAClB,KAAK,EAAE0E,QAAQ,IAAI,IAAI,GAAGA,QAAQ,GAAG,IAAI,CAAC;EAC3F,CAAC,EAAE,CAACxD,iBAAiB,CAAC,CAAC;EACvB,MAAMyD,iBAAiB,GAAGjH,KAAK,CAAC6F,WAAW,CAAC,CAACvD,KAAK,EAAE4E,KAAK,EAAEC,UAAU,KAAK;IACxE,IAAID,KAAK,KAAK,MAAM,EAAE;MACpBzD,YAAY,IAAI,IAAI,IAAIA,YAAY,CAAC0D,UAAU,CAAC;MAChD,IAAIA,UAAU,KAAK,KAAK,IAAI,CAAC7E,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAAC8E,IAAI,MAAM,MAAM,EAAE;QAC5E,IAAIC,kBAAkB;QACtB,CAACA,kBAAkB,GAAG3E,SAAS,CAAC4E,OAAO,KAAK,IAAI,IAAID,kBAAkB,CAACE,KAAK,CAAC,CAAC;MAChF;IACF;EACF,CAAC,EAAE,CAAC9D,YAAY,CAAC,CAAC;EAClB,MAAM+D,iBAAiB,GAAG;IACxBC,eAAe,EAAEA,CAAA,KAAM;MACrB,IAAIC,aAAa;MACjB,OAAO;QACLC,gBAAgB,EAAE,IAAI;QACtBnB,cAAc,EAAE,CAACkB,aAAa,GAAG7E,YAAY,KAAK,IAAI,GAAG6E,aAAa,GAAG,EAAE;QAC3EhE,IAAI,EAAEd;MACR,CAAC;IACH,CAAC;IACD6D,SAAS;IACTmB,eAAe,EAAErB,eAAe;IAChCsB,YAAY,EAAEpF,eAAe;IAC7B0D,cAAc;IACdZ,OAAO,EAAEC,eAAe;IACxBjC,QAAQ,EAAEqD,qBAAqB;IAC/BpD,iBAAiB,EAAEuD,qBAAqB;IACxCe,aAAa,EAAEb,iBAAiB;IAChCc,oBAAoB,EAAE/H,KAAK,CAACqE,OAAO,CAAC,OAAO;MACzCjB;IACF,CAAC,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;IACf4E,KAAK,EAAEvC,YAAY;IACnBwC,eAAe,EAAE3B,eAAe;IAChC4B,aAAa,EAAE9E,QAAQ,GAAG,UAAU,GAAG,QAAQ;IAC/C+E,YAAY,EAAErH,aAAa;IAC3BmD;EACF,CAAC;EACD,MAAM;IACJmE,QAAQ;IACRnD,YAAY,EAAEoD,mBAAmB;IACjC9D,YAAY,EAAE+D,gBAAgB;IAC9BC,KAAK,EAAE;MACL7E,IAAI;MACJiE,gBAAgB,EAAEa,iBAAiB;MACnChC,cAAc,EAAEiC;IAClB,CAAC;IACDlD,OAAO,EAAEmD;EACX,CAAC,GAAGhI,OAAO,CAAC8G,iBAAiB,CAAC;EAC9B,MAAMmB,2BAA2B,GAAGC,qBAAqB,IAAItG,KAAK,IAAI;IACpE,IAAIuG,qBAAqB;IACzBD,qBAAqB,IAAI,IAAI,IAAI,CAACC,qBAAqB,GAAGD,qBAAqB,CAACE,WAAW,KAAK,IAAI,IAAID,qBAAqB,CAACE,IAAI,CAACH,qBAAqB,EAAEtG,KAAK,CAAC;IAChK,IAAI,CAACA,KAAK,CAAC0G,mBAAmB,EAAE;MAC9B,MAAMC,MAAM,GAAG;QACb7B,IAAI,EAAE5G,iBAAiB,CAAC0I,WAAW;QACnC5G;MACF,CAAC;MACD8F,QAAQ,CAACa,MAAM,CAAC;IAClB;EACF,CAAC;EACD3I,iBAAiB,CAAC,MAAM;IACtB;IACA,IAAIkI,iBAAiB,IAAI,IAAI,EAAE;MAC7B,IAAIW,iBAAiB;MACrB,MAAMC,SAAS,GAAG,CAACD,iBAAiB,GAAGvD,gBAAgB,CAAC4C,iBAAiB,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGW,iBAAiB,CAACtE,GAAG;MACpH,IAAI,CAAC3B,UAAU,CAACoE,OAAO,IAAI,EAAE8B,SAAS,IAAI,IAAI,IAAIA,SAAS,CAAC9B,OAAO,CAAC,EAAE;QACpE;MACF;MACA,MAAM+B,iBAAiB,GAAGnG,UAAU,CAACoE,OAAO,CAACgC,qBAAqB,CAAC,CAAC;MACpE,MAAMC,gBAAgB,GAAGH,SAAS,CAAC9B,OAAO,CAACgC,qBAAqB,CAAC,CAAC;MAClE,IAAIC,gBAAgB,CAACC,GAAG,GAAGH,iBAAiB,CAACG,GAAG,EAAE;QAChDtG,UAAU,CAACoE,OAAO,CAACmC,SAAS,IAAIJ,iBAAiB,CAACG,GAAG,GAAGD,gBAAgB,CAACC,GAAG;MAC9E,CAAC,MAAM,IAAID,gBAAgB,CAAC7H,MAAM,GAAG2H,iBAAiB,CAAC3H,MAAM,EAAE;QAC7DwB,UAAU,CAACoE,OAAO,CAACmC,SAAS,IAAIF,gBAAgB,CAAC7H,MAAM,GAAG2H,iBAAiB,CAAC3H,MAAM;MACpF;IACF;EACF,CAAC,EAAE,CAAC8G,iBAAiB,EAAE5C,gBAAgB,CAAC,CAAC;EACzC,MAAM8D,iBAAiB,GAAG1J,KAAK,CAAC6F,WAAW,CAACI,WAAW,IAAIL,gBAAgB,CAACK,WAAW,CAAC,EAAE,CAACL,gBAAgB,CAAC,CAAC;EAC7G,MAAM+D,qBAAqB,GAAGA,CAACC,aAAa,GAAG,CAAC,CAAC,KAAK;IACpD,OAAO7J,QAAQ,CAAC,CAAC,CAAC,EAAE6J,aAAa,EAAE;MACjCd,WAAW,EAAEH,2BAA2B,CAACiB,aAAa,CAAC;MACvD/E,GAAG,EAAE6D,iBAAiB;MACtBmB,IAAI,EAAE,UAAU;MAChB,eAAe,EAAEnG,IAAI;MACrB,eAAe,EAAEV;IACnB,CAAC,CAAC;EACJ,CAAC;EACD,MAAM8G,cAAc,GAAGA,CAACC,aAAa,GAAG,CAAC,CAAC,KAAK;IAC7C,MAAMnB,qBAAqB,GAAG/H,oBAAoB,CAACkJ,aAAa,CAAC;IACjE,MAAMC,qBAAqB,GAAGjJ,qBAAqB,CAACmE,kBAAkB,EAAEmD,mBAAmB,CAAC;IAC5F,MAAM4B,aAAa,GAAGlJ,qBAAqB,CAACiJ,qBAAqB,EAAEL,qBAAqB,CAAC;IACzF,OAAO5J,QAAQ,CAAC,CAAC,CAAC,EAAEgK,aAAa,EAAEE,aAAa,CAACrB,qBAAqB,CAAC,CAAC;EAC1E,CAAC;EACD,MAAMsB,eAAe,GAAGA,CAACH,aAAa,GAAG,CAAC,CAAC,KAAK;IAC9C,OAAOhK,QAAQ,CAAC,CAAC,CAAC,EAAEgK,aAAa,EAAE;MACjChF,EAAE,EAAE/B,SAAS;MACb6G,IAAI,EAAE,SAAS;MACf,sBAAsB,EAAEzG,QAAQ,GAAG,MAAM,GAAGgB,SAAS;MACrDS,GAAG,EAAEG,gBAAgB;MACrB8D,WAAW,EAAEzG,cAAc,CAAC;IAC9B,CAAC,CAAC;EACJ,CAAC;EACDrC,KAAK,CAACmK,aAAa,CAAC;IAClB1B,eAAe;IACfD,iBAAiB;IACjB9E;EACF,CAAC,CAAC;EACF,MAAMa,YAAY,GAAGvE,KAAK,CAACqE,OAAO,CAAC,MAAMtE,QAAQ,CAAC,CAAC,CAAC,EAAEuI,gBAAgB,EAAE9D,6BAA6B,CAAC,EAAE,CAAC8D,gBAAgB,EAAE9D,6BAA6B,CAAC,CAAC;EAC1J,IAAI4F,WAAW;EACf,IAAI5H,KAAK,CAACY,QAAQ,EAAE;IAClBgH,WAAW,GAAG3B,eAAe;EAC/B,CAAC,MAAM;IACL2B,WAAW,GAAG3B,eAAe,CAAC1G,MAAM,GAAG,CAAC,GAAG0G,eAAe,CAAC,CAAC,CAAC,GAAG,IAAI;EACtE;EACA,IAAI4B,uBAAuB;EAC3B,IAAIjH,QAAQ,EAAE;IACZiH,uBAAuB,GAAGD,WAAW,CAAClI,GAAG,CAACoI,CAAC,IAAIZ,iBAAiB,CAACY,CAAC,CAAC,CAAC,CAACC,MAAM,CAACpI,CAAC,IAAIA,CAAC,KAAKiC,SAAS,CAAC;EACnG,CAAC,MAAM;IACL,IAAIoG,kBAAkB;IACtBH,uBAAuB,GAAG,CAACG,kBAAkB,GAAGd,iBAAiB,CAACU,WAAW,CAAC,KAAK,IAAI,GAAGI,kBAAkB,GAAG,IAAI;EACrH;EACA,MAAMC,6BAA6B,GAAG7B,qBAAqB,IAAItG,KAAK,IAAI;IACtE,IAAIoI,sBAAsB;IAC1B9B,qBAAqB,IAAI,IAAI,IAAI,CAAC8B,sBAAsB,GAAG9B,qBAAqB,CAACrF,QAAQ,KAAK,IAAI,IAAImH,sBAAsB,CAAC3B,IAAI,CAACH,qBAAqB,EAAEtG,KAAK,CAAC;IAC/J,IAAIA,KAAK,CAAC0G,mBAAmB,EAAE;MAC7B;IACF;IACA,MAAMtE,MAAM,GAAGd,OAAO,CAACsC,GAAG,CAAC5D,KAAK,CAACqI,MAAM,CAACvI,KAAK,CAAC;;IAE9C;IACA,IAAIE,KAAK,CAACqI,MAAM,CAACvI,KAAK,KAAK,EAAE,EAAE;MAC7BgG,QAAQ,CAAC;QACPhB,IAAI,EAAE3G,eAAe,CAACmK;MACxB,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIlG,MAAM,KAAKN,SAAS,EAAE;MAC/BgE,QAAQ,CAAC;QACPhB,IAAI,EAAE5G,iBAAiB,CAACqK,eAAe;QACvCC,IAAI,EAAEpG,MAAM,CAACtC,KAAK;QAClBE;MACF,CAAC,CAAC;IACJ;EACF,CAAC;EACD,MAAMyI,mBAAmB,GAAGA,CAAChB,aAAa,GAAG,CAAC,CAAC,KAAK;IAClD,MAAMnB,qBAAqB,GAAG/H,oBAAoB,CAACkJ,aAAa,CAAC;IACjE,OAAOhK,QAAQ,CAAC;MACdsD,IAAI;MACJ2H,QAAQ,EAAE,CAAC,CAAC;MACZ,aAAa,EAAE,IAAI;MACnB1H,QAAQ,EAAEA,QAAQ,GAAG,IAAI,GAAGc,SAAS;MACrChC,KAAK,EAAE2B,kBAAkB,CAACsG,uBAAuB,CAAC;MAClDY,KAAK,EAAEjK;IACT,CAAC,EAAE+I,aAAa,EAAE;MAChBxG,QAAQ,EAAEkH,6BAA6B,CAAC7B,qBAAqB;IAC/D,CAAC,CAAC;EACJ,CAAC;EACD,OAAO;IACLxD,YAAY;IACZE,kBAAkB;IAClB5C,SAAS,EAAE8C,eAAe;IAC1BjB,YAAY;IACZxB,QAAQ;IACRqF,QAAQ;IACR0B,cAAc;IACdiB,mBAAmB;IACnBb,eAAe;IACfR,iBAAiB;IACjBxG,UAAU,EAAEwF,iBAAiB;IAC7BhF,IAAI;IACJE,OAAO,EAAE6B,YAAY;IACrBrD,KAAK,EAAEgI,WAAW;IAClB5B;EACF,CAAC;AACH;AACA,SAASjG,SAAS"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}