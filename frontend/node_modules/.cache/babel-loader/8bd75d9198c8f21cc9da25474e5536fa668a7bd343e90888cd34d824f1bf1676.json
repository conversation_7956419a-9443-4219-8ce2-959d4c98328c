{"ast": null, "code": "import { unstable_isMuiElement as isMuiElement } from '@mui/utils';\nexport default isMuiElement;", "map": {"version": 3, "names": ["unstable_isMuiElement", "isMuiElement"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/material/utils/isMuiElement.js"], "sourcesContent": ["import { unstable_isMuiElement as isMuiElement } from '@mui/utils';\nexport default isMuiElement;"], "mappings": "AAAA,SAASA,qBAAqB,IAAIC,YAAY,QAAQ,YAAY;AAClE,eAAeA,YAAY"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}