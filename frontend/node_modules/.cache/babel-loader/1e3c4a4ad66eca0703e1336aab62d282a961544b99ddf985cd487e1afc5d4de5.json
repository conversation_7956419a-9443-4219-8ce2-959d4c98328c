{"ast": null, "code": "import { unstable_generateUtilityClasses as generateUtilityClasses } from '@mui/utils';\nimport generateUtilityClass from '../generateUtilityClass';\nexport function getAvatarGroupUtilityClass(slot) {\n  return generateUtilityClass('MuiAvatarGroup', slot);\n}\nconst avatarGroupClasses = generateUtilityClasses('MuiAvatarGroup', ['root', 'avatar']);\nexport default avatarGroupClasses;", "map": {"version": 3, "names": ["unstable_generateUtilityClasses", "generateUtilityClasses", "generateUtilityClass", "getAvatarGroupUtilityClass", "slot", "avatarGroupClasses"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/material/AvatarGroup/avatarGroupClasses.js"], "sourcesContent": ["import { unstable_generateUtilityClasses as generateUtilityClasses } from '@mui/utils';\nimport generateUtilityClass from '../generateUtilityClass';\nexport function getAvatarGroupUtilityClass(slot) {\n  return generateUtilityClass('MuiAvatarGroup', slot);\n}\nconst avatarGroupClasses = generateUtilityClasses('MuiAvatarGroup', ['root', 'avatar']);\nexport default avatarGroupClasses;"], "mappings": "AAAA,SAASA,+BAA+B,IAAIC,sBAAsB,QAAQ,YAAY;AACtF,OAAOC,oBAAoB,MAAM,yBAAyB;AAC1D,OAAO,SAASC,0BAA0BA,CAACC,IAAI,EAAE;EAC/C,OAAOF,oBAAoB,CAAC,gBAAgB,EAAEE,IAAI,CAAC;AACrD;AACA,MAAMC,kBAAkB,GAAGJ,sBAAsB,CAAC,gBAAgB,EAAE,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AACvF,eAAeI,kBAAkB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}