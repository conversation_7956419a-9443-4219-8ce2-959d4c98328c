{"ast": null, "code": "'use client';\n\nexport { useAutocomplete as default } from '@mui/base/useAutocomplete';\nexport * from '@mui/base/useAutocomplete';", "map": {"version": 3, "names": ["useAutocomplete", "default"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/material/useAutocomplete/useAutocomplete.js"], "sourcesContent": ["'use client';\n\nexport { useAutocomplete as default } from '@mui/base/useAutocomplete';\nexport * from '@mui/base/useAutocomplete';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,eAAe,IAAIC,OAAO,QAAQ,2BAA2B;AACtE,cAAc,2BAA2B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}