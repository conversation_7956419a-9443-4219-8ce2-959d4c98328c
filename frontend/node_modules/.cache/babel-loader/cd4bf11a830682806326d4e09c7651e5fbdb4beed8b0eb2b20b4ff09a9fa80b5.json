{"ast": null, "code": "export default {\n  disabled: false\n};", "map": {"version": 3, "names": ["disabled"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/react-transition-group/esm/config.js"], "sourcesContent": ["export default {\n  disabled: false\n};"], "mappings": "AAAA,eAAe;EACbA,QAAQ,EAAE;AACZ,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}