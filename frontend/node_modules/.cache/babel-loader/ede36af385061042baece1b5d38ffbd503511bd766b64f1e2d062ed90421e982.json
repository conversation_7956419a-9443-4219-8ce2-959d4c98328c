{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { ListActionTypes, listReducer } from '../useList';\nexport function menuReducer(state, action) {\n  if (action.type === ListActionTypes.itemHover) {\n    return state;\n  }\n  const newState = listReducer(state, action);\n\n  // make sure an item is always highlighted\n  if (newState.highlightedValue === null && action.context.items.length > 0) {\n    return _extends({}, newState, {\n      highlightedValue: action.context.items[0]\n    });\n  }\n  if (action.type === ListActionTypes.keyDown) {\n    if (action.event.key === 'Escape') {\n      return _extends({}, newState, {\n        open: false\n      });\n    }\n  }\n  if (action.type === ListActionTypes.blur) {\n    var _action$context$listb;\n    if (!((_action$context$listb = action.context.listboxRef.current) != null && _action$context$listb.contains(action.event.relatedTarget))) {\n      var _action$context$listb2, _action$event$related;\n      // To prevent the menu from closing when the focus leaves the menu to the button.\n      // For more details, see https://github.com/mui/material-ui/pull/36917#issuecomment-1566992698\n      const listboxId = (_action$context$listb2 = action.context.listboxRef.current) == null ? void 0 : _action$context$listb2.getAttribute('id');\n      const controlledBy = (_action$event$related = action.event.relatedTarget) == null ? void 0 : _action$event$related.getAttribute('aria-controls');\n      if (listboxId && controlledBy && listboxId === controlledBy) {\n        return newState;\n      }\n      return _extends({}, newState, {\n        open: false,\n        highlightedValue: action.context.items[0]\n      });\n    }\n  }\n  return newState;\n}", "map": {"version": 3, "names": ["_extends", "ListActionTypes", "listReducer", "menuReducer", "state", "action", "type", "itemHover", "newState", "highlightedValue", "context", "items", "length", "keyDown", "event", "key", "open", "blur", "_action$context$listb", "listboxRef", "current", "contains", "relatedTarget", "_action$context$listb2", "_action$event$related", "listboxId", "getAttribute", "controlledBy"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/base/useMenu/menuReducer.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { ListActionTypes, listReducer } from '../useList';\nexport function menuReducer(state, action) {\n  if (action.type === ListActionTypes.itemHover) {\n    return state;\n  }\n  const newState = listReducer(state, action);\n\n  // make sure an item is always highlighted\n  if (newState.highlightedValue === null && action.context.items.length > 0) {\n    return _extends({}, newState, {\n      highlightedValue: action.context.items[0]\n    });\n  }\n  if (action.type === ListActionTypes.keyDown) {\n    if (action.event.key === 'Escape') {\n      return _extends({}, newState, {\n        open: false\n      });\n    }\n  }\n  if (action.type === ListActionTypes.blur) {\n    var _action$context$listb;\n    if (!((_action$context$listb = action.context.listboxRef.current) != null && _action$context$listb.contains(action.event.relatedTarget))) {\n      var _action$context$listb2, _action$event$related;\n      // To prevent the menu from closing when the focus leaves the menu to the button.\n      // For more details, see https://github.com/mui/material-ui/pull/36917#issuecomment-1566992698\n      const listboxId = (_action$context$listb2 = action.context.listboxRef.current) == null ? void 0 : _action$context$listb2.getAttribute('id');\n      const controlledBy = (_action$event$related = action.event.relatedTarget) == null ? void 0 : _action$event$related.getAttribute('aria-controls');\n      if (listboxId && controlledBy && listboxId === controlledBy) {\n        return newState;\n      }\n      return _extends({}, newState, {\n        open: false,\n        highlightedValue: action.context.items[0]\n      });\n    }\n  }\n  return newState;\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,SAASC,eAAe,EAAEC,WAAW,QAAQ,YAAY;AACzD,OAAO,SAASC,WAAWA,CAACC,KAAK,EAAEC,MAAM,EAAE;EACzC,IAAIA,MAAM,CAACC,IAAI,KAAKL,eAAe,CAACM,SAAS,EAAE;IAC7C,OAAOH,KAAK;EACd;EACA,MAAMI,QAAQ,GAAGN,WAAW,CAACE,KAAK,EAAEC,MAAM,CAAC;;EAE3C;EACA,IAAIG,QAAQ,CAACC,gBAAgB,KAAK,IAAI,IAAIJ,MAAM,CAACK,OAAO,CAACC,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;IACzE,OAAOZ,QAAQ,CAAC,CAAC,CAAC,EAAEQ,QAAQ,EAAE;MAC5BC,gBAAgB,EAAEJ,MAAM,CAACK,OAAO,CAACC,KAAK,CAAC,CAAC;IAC1C,CAAC,CAAC;EACJ;EACA,IAAIN,MAAM,CAACC,IAAI,KAAKL,eAAe,CAACY,OAAO,EAAE;IAC3C,IAAIR,MAAM,CAACS,KAAK,CAACC,GAAG,KAAK,QAAQ,EAAE;MACjC,OAAOf,QAAQ,CAAC,CAAC,CAAC,EAAEQ,QAAQ,EAAE;QAC5BQ,IAAI,EAAE;MACR,CAAC,CAAC;IACJ;EACF;EACA,IAAIX,MAAM,CAACC,IAAI,KAAKL,eAAe,CAACgB,IAAI,EAAE;IACxC,IAAIC,qBAAqB;IACzB,IAAI,EAAE,CAACA,qBAAqB,GAAGb,MAAM,CAACK,OAAO,CAACS,UAAU,CAACC,OAAO,KAAK,IAAI,IAAIF,qBAAqB,CAACG,QAAQ,CAAChB,MAAM,CAACS,KAAK,CAACQ,aAAa,CAAC,CAAC,EAAE;MACxI,IAAIC,sBAAsB,EAAEC,qBAAqB;MACjD;MACA;MACA,MAAMC,SAAS,GAAG,CAACF,sBAAsB,GAAGlB,MAAM,CAACK,OAAO,CAACS,UAAU,CAACC,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGG,sBAAsB,CAACG,YAAY,CAAC,IAAI,CAAC;MAC3I,MAAMC,YAAY,GAAG,CAACH,qBAAqB,GAAGnB,MAAM,CAACS,KAAK,CAACQ,aAAa,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGE,qBAAqB,CAACE,YAAY,CAAC,eAAe,CAAC;MAChJ,IAAID,SAAS,IAAIE,YAAY,IAAIF,SAAS,KAAKE,YAAY,EAAE;QAC3D,OAAOnB,QAAQ;MACjB;MACA,OAAOR,QAAQ,CAAC,CAAC,CAAC,EAAEQ,QAAQ,EAAE;QAC5BQ,IAAI,EAAE,KAAK;QACXP,gBAAgB,EAAEJ,MAAM,CAACK,OAAO,CAACC,KAAK,CAAC,CAAC;MAC1C,CAAC,CAAC;IACJ;EACF;EACA,OAAOH,QAAQ;AACjB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}