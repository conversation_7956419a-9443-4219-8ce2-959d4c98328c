{"ast": null, "code": "import { unstable_debounce as debounce } from '@mui/utils';\nexport default debounce;", "map": {"version": 3, "names": ["unstable_debounce", "debounce"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/material/utils/debounce.js"], "sourcesContent": ["import { unstable_debounce as debounce } from '@mui/utils';\nexport default debounce;"], "mappings": "AAAA,SAASA,iBAAiB,IAAIC,QAAQ,QAAQ,YAAY;AAC1D,eAAeA,QAAQ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}