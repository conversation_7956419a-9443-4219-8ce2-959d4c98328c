{"ast": null, "code": "const defaultOptionStringifier = option => {\n  const {\n    label,\n    value\n  } = option;\n  if (typeof label === 'string') {\n    return label;\n  }\n  if (typeof value === 'string') {\n    return value;\n  }\n\n  // Fallback string representation\n  return String(option);\n};\nexport { defaultOptionStringifier };", "map": {"version": 3, "names": ["defaultOptionStringifier", "option", "label", "value", "String"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/base/useSelect/defaultOptionStringifier.js"], "sourcesContent": ["const defaultOptionStringifier = option => {\n  const {\n    label,\n    value\n  } = option;\n  if (typeof label === 'string') {\n    return label;\n  }\n  if (typeof value === 'string') {\n    return value;\n  }\n\n  // Fallback string representation\n  return String(option);\n};\nexport { defaultOptionStringifier };"], "mappings": "AAAA,MAAMA,wBAAwB,GAAGC,MAAM,IAAI;EACzC,MAAM;IACJC,KAAK;IACLC;EACF,CAAC,GAAGF,MAAM;EACV,IAAI,OAAOC,KAAK,KAAK,QAAQ,EAAE;IAC7B,OAAOA,KAAK;EACd;EACA,IAAI,OAAOC,KAAK,KAAK,QAAQ,EAAE;IAC7B,OAAOA,KAAK;EACd;;EAEA;EACA,OAAOC,MAAM,CAACH,MAAM,CAAC;AACvB,CAAC;AACD,SAASD,wBAAwB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}