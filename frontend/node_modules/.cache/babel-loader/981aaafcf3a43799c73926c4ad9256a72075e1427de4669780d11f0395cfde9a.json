{"ast": null, "code": "import { generateUtilityClass } from '../generateUtilityClass';\nimport { generateUtilityClasses } from '../generateUtilityClasses';\nexport function getPopupUtilityClass(slot) {\n  return generateUtilityClass('MuiPopup', slot);\n}\nexport const popupClasses = generateUtilityClasses('MuiPopup', ['root', 'open']);", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getPopupUtilityClass", "slot", "popupClasses"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/base/Unstable_Popup/popupClasses.js"], "sourcesContent": ["import { generateUtilityClass } from '../generateUtilityClass';\nimport { generateUtilityClasses } from '../generateUtilityClasses';\nexport function getPopupUtilityClass(slot) {\n  return generateUtilityClass('MuiPopup', slot);\n}\nexport const popupClasses = generateUtilityClasses('MuiPopup', ['root', 'open']);"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,yBAAyB;AAC9D,SAASC,sBAAsB,QAAQ,2BAA2B;AAClE,OAAO,SAASC,oBAAoBA,CAACC,IAAI,EAAE;EACzC,OAAOH,oBAAoB,CAAC,UAAU,EAAEG,IAAI,CAAC;AAC/C;AACA,OAAO,MAAMC,YAAY,GAAGH,sBAAsB,CAAC,UAAU,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}