{"ast": null, "code": "export { Modal } from './Modal';\nexport * from './Modal.types';\nexport * from './modalClasses';", "map": {"version": 3, "names": ["Modal"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/base/Modal/index.js"], "sourcesContent": ["export { Modal } from './Modal';\nexport * from './Modal.types';\nexport * from './modalClasses';"], "mappings": "AAAA,SAASA,KAAK,QAAQ,SAAS;AAC/B,cAAc,eAAe;AAC7B,cAAc,gBAAgB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}