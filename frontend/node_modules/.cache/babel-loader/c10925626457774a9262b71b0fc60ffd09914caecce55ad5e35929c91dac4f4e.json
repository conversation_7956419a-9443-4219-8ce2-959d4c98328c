{"ast": null, "code": "import * as React from 'react';\nconst DropdownContext = /*#__PURE__*/React.createContext(null);\nexport { DropdownContext };", "map": {"version": 3, "names": ["React", "DropdownContext", "createContext"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/base/useDropdown/DropdownContext.js"], "sourcesContent": ["import * as React from 'react';\nconst DropdownContext = /*#__PURE__*/React.createContext(null);\nexport { DropdownContext };"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,MAAMC,eAAe,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,IAAI,CAAC;AAC9D,SAASD,eAAe"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}