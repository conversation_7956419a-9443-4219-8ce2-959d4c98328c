{"ast": null, "code": "import { unstable_generateUtilityClasses as generateUtilityClasses } from '@mui/utils';\nimport generateUtilityClass from '../generateUtilityClass';\nexport function getToolbarUtilityClass(slot) {\n  return generateUtilityClass('MuiToolbar', slot);\n}\nconst toolbarClasses = generateUtilityClasses('MuiToolbar', ['root', 'gutters', 'regular', 'dense']);\nexport default toolbarClasses;", "map": {"version": 3, "names": ["unstable_generateUtilityClasses", "generateUtilityClasses", "generateUtilityClass", "getToolbarUtilityClass", "slot", "toolbarClasses"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/material/Toolbar/toolbarClasses.js"], "sourcesContent": ["import { unstable_generateUtilityClasses as generateUtilityClasses } from '@mui/utils';\nimport generateUtilityClass from '../generateUtilityClass';\nexport function getToolbarUtilityClass(slot) {\n  return generateUtilityClass('MuiToolbar', slot);\n}\nconst toolbarClasses = generateUtilityClasses('MuiToolbar', ['root', 'gutters', 'regular', 'dense']);\nexport default toolbarClasses;"], "mappings": "AAAA,SAASA,+BAA+B,IAAIC,sBAAsB,QAAQ,YAAY;AACtF,OAAOC,oBAAoB,MAAM,yBAAyB;AAC1D,OAAO,SAASC,sBAAsBA,CAACC,IAAI,EAAE;EAC3C,OAAOF,oBAAoB,CAAC,YAAY,EAAEE,IAAI,CAAC;AACjD;AACA,MAAMC,cAAc,GAAGJ,sBAAsB,CAAC,YAAY,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;AACpG,eAAeI,cAAc"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}