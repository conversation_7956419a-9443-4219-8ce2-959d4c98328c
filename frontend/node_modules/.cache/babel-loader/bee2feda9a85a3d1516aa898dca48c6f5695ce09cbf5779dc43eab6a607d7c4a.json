{"ast": null, "code": "/**\n * Determines if a given element is a DOM element name (i.e. not a React component).\n */\nexport function isHostComponent(element) {\n  return typeof element === 'string';\n}", "map": {"version": 3, "names": ["isHostComponent", "element"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/base/utils/isHostComponent.js"], "sourcesContent": ["/**\n * Determines if a given element is a DOM element name (i.e. not a React component).\n */\nexport function isHostComponent(element) {\n  return typeof element === 'string';\n}"], "mappings": "AAAA;AACA;AACA;AACA,OAAO,SAASA,eAAeA,CAACC,OAAO,EAAE;EACvC,OAAO,OAAOA,OAAO,KAAK,QAAQ;AACpC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}