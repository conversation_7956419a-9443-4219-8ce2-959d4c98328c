{"ast": null, "code": "import { generateUtilityClass } from '../generateUtilityClass';\nimport { generateUtilityClasses } from '../generateUtilityClasses';\nexport function getOptionGroupUtilityClass(slot) {\n  return generateUtilityClass('MuiOptionGroup', slot);\n}\nexport const optionGroupClasses = generateUtilityClasses('MuiOptionGroup', ['root', 'disabled', 'label', 'list']);", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getOptionGroupUtilityClass", "slot", "optionGroupClasses"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/base/OptionGroup/optionGroupClasses.js"], "sourcesContent": ["import { generateUtilityClass } from '../generateUtilityClass';\nimport { generateUtilityClasses } from '../generateUtilityClasses';\nexport function getOptionGroupUtilityClass(slot) {\n  return generateUtilityClass('MuiOptionGroup', slot);\n}\nexport const optionGroupClasses = generateUtilityClasses('MuiOptionGroup', ['root', 'disabled', 'label', 'list']);"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,yBAAyB;AAC9D,SAASC,sBAAsB,QAAQ,2BAA2B;AAClE,OAAO,SAASC,0BAA0BA,CAACC,IAAI,EAAE;EAC/C,OAAOH,oBAAoB,CAAC,gBAAgB,EAAEG,IAAI,CAAC;AACrD;AACA,OAAO,MAAMC,kBAAkB,GAAGH,sBAAsB,CAAC,gBAAgB,EAAE,CAAC,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}