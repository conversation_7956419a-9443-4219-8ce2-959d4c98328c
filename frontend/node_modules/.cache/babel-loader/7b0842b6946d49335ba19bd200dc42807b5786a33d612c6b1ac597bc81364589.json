{"ast": null, "code": "export const TabsListActionTypes = {\n  valueChange: 'valueChange'\n};", "map": {"version": 3, "names": ["TabsListActionTypes", "valueChange"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/base/useTabsList/useTabsList.types.js"], "sourcesContent": ["export const TabsListActionTypes = {\n  valueChange: 'valueChange'\n};"], "mappings": "AAAA,OAAO,MAAMA,mBAAmB,GAAG;EACjCC,WAAW,EAAE;AACf,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}