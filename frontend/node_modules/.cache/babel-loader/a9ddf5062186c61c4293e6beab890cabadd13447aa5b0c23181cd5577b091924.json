{"ast": null, "code": "'use client';\n\nexport * from './Option';\nexport * from './Option.types';\nexport * from './optionClasses';", "map": {"version": 3, "names": [], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/base/Option/index.js"], "sourcesContent": ["'use client';\n\nexport * from './Option';\nexport * from './Option.types';\nexport * from './optionClasses';"], "mappings": "AAAA,YAAY;;AAEZ,cAAc,UAAU;AACxB,cAAc,gBAAgB;AAC9B,cAAc,iBAAiB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}