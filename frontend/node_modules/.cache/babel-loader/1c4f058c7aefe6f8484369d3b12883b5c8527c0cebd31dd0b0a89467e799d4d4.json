{"ast": null, "code": "import { unstable_deprecatedPropType as deprecatedPropType } from '@mui/utils';\nexport default deprecatedPropType;", "map": {"version": 3, "names": ["unstable_deprecatedPropType", "deprecatedPropType"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/material/utils/deprecatedPropType.js"], "sourcesContent": ["import { unstable_deprecatedPropType as deprecatedPropType } from '@mui/utils';\nexport default deprecatedPropType;"], "mappings": "AAAA,SAASA,2BAA2B,IAAIC,kBAAkB,QAAQ,YAAY;AAC9E,eAAeA,kBAAkB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}