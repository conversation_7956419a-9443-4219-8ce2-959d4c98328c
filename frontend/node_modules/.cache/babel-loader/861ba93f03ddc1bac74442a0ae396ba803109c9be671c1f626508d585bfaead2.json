{"ast": null, "code": "'use strict';\n\nvar $documentAll = require('../internals/document-all');\nvar documentAll = $documentAll.all;\n\n// `IsCallable` abstract operation\n// https://tc39.es/ecma262/#sec-iscallable\nmodule.exports = $documentAll.IS_HTMLDDA ? function (argument) {\n  return typeof argument == 'function' || argument === documentAll;\n} : function (argument) {\n  return typeof argument == 'function';\n};", "map": {"version": 3, "names": ["$documentAll", "require", "documentAll", "all", "module", "exports", "IS_HTMLDDA", "argument"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/core-js-pure/internals/is-callable.js"], "sourcesContent": ["'use strict';\nvar $documentAll = require('../internals/document-all');\n\nvar documentAll = $documentAll.all;\n\n// `IsCallable` abstract operation\n// https://tc39.es/ecma262/#sec-iscallable\nmodule.exports = $documentAll.IS_HTMLDDA ? function (argument) {\n  return typeof argument == 'function' || argument === documentAll;\n} : function (argument) {\n  return typeof argument == 'function';\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,YAAY,GAAGC,OAAO,CAAC,2BAA2B,CAAC;AAEvD,IAAIC,WAAW,GAAGF,YAAY,CAACG,GAAG;;AAElC;AACA;AACAC,MAAM,CAACC,OAAO,GAAGL,YAAY,CAACM,UAAU,GAAG,UAAUC,QAAQ,EAAE;EAC7D,OAAO,OAAOA,QAAQ,IAAI,UAAU,IAAIA,QAAQ,KAAKL,WAAW;AAClE,CAAC,GAAG,UAAUK,QAAQ,EAAE;EACtB,OAAO,OAAOA,QAAQ,IAAI,UAAU;AACtC,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}