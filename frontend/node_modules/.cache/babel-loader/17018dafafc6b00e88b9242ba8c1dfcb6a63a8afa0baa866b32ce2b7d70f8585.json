{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"children\", \"className\", \"cols\", \"component\", \"rows\", \"style\"];\nimport { unstable_composeClasses as composeClasses } from '@mui/base/composeClasses';\nimport { integerPropType } from '@mui/utils';\nimport clsx from 'clsx';\nimport PropTypes from 'prop-types';\nimport * as React from 'react';\nimport { isFragment } from 'react-is';\nimport ImageListContext from '../ImageList/ImageListContext';\nimport styled from '../styles/styled';\nimport useThemeProps from '../styles/useThemeProps';\nimport isMuiElement from '../utils/isMuiElement';\nimport imageListItemClasses, { getImageListItemUtilityClass } from './imageListItemClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    variant\n  } = ownerState;\n  const slots = {\n    root: ['root', variant],\n    img: ['img']\n  };\n  return composeClasses(slots, getImageListItemUtilityClass, classes);\n};\nconst ImageListItemRoot = styled('li', {\n  name: 'MuiImageListItem',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [{\n      [`& .${imageListItemClasses.img}`]: styles.img\n    }, styles.root, styles[ownerState.variant]];\n  }\n})(({\n  ownerState\n}) => _extends({\n  display: 'block',\n  position: 'relative'\n}, ownerState.variant === 'standard' && {\n  // For titlebar under list item\n  display: 'flex',\n  flexDirection: 'column'\n}, ownerState.variant === 'woven' && {\n  height: '100%',\n  alignSelf: 'center',\n  '&:nth-of-type(even)': {\n    height: '70%'\n  }\n}, {\n  [`& .${imageListItemClasses.img}`]: _extends({\n    objectFit: 'cover',\n    width: '100%',\n    height: '100%',\n    display: 'block'\n  }, ownerState.variant === 'standard' && {\n    height: 'auto',\n    flexGrow: 1\n  })\n}));\nconst ImageListItem = /*#__PURE__*/React.forwardRef(function ImageListItem(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiImageListItem'\n  });\n\n  // TODO: - Use jsdoc @default?: \"cols rows default values are for docs only\"\n  const {\n      children,\n      className,\n      cols = 1,\n      component = 'li',\n      rows = 1,\n      style\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    rowHeight = 'auto',\n    gap,\n    variant\n  } = React.useContext(ImageListContext);\n  let height = 'auto';\n  if (variant === 'woven') {\n    height = undefined;\n  } else if (rowHeight !== 'auto') {\n    height = rowHeight * rows + gap * (rows - 1);\n  }\n  const ownerState = _extends({}, props, {\n    cols,\n    component,\n    gap,\n    rowHeight,\n    rows,\n    variant\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(ImageListItemRoot, _extends({\n    as: component,\n    className: clsx(classes.root, classes[variant], className),\n    ref: ref,\n    style: _extends({\n      height,\n      gridColumnEnd: variant !== 'masonry' ? `span ${cols}` : undefined,\n      gridRowEnd: variant !== 'masonry' ? `span ${rows}` : undefined,\n      marginBottom: variant === 'masonry' ? gap : undefined,\n      breakInside: variant === 'masonry' ? 'avoid' : undefined\n    }, style),\n    ownerState: ownerState\n  }, other, {\n    children: React.Children.map(children, child => {\n      if (! /*#__PURE__*/React.isValidElement(child)) {\n        return null;\n      }\n      if (process.env.NODE_ENV !== 'production') {\n        if (isFragment(child)) {\n          console.error([\"MUI: The ImageListItem component doesn't accept a Fragment as a child.\", 'Consider providing an array instead.'].join('\\n'));\n        }\n      }\n      if (child.type === 'img' || isMuiElement(child, ['Image'])) {\n        return /*#__PURE__*/React.cloneElement(child, {\n          className: clsx(classes.img, child.props.className)\n        });\n      }\n      return child;\n    })\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? ImageListItem.propTypes /* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit the d.ts file and run \"yarn proptypes\"     |\n  // ----------------------------------------------------------------------\n  /**\n   * The content of the component, normally an `<img>`.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * Width of the item in number of grid columns.\n   * @default 1\n   */\n  cols: integerPropType,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * Height of the item in number of grid rows.\n   * @default 1\n   */\n  rows: integerPropType,\n  /**\n   * @ignore\n   */\n  style: PropTypes.object,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default ImageListItem;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "unstable_composeClasses", "composeClasses", "integerPropType", "clsx", "PropTypes", "React", "isFragment", "ImageListContext", "styled", "useThemeProps", "isMuiElement", "imageListItemClasses", "getImageListItemUtilityClass", "jsx", "_jsx", "useUtilityClasses", "ownerState", "classes", "variant", "slots", "root", "img", "ImageListItemRoot", "name", "slot", "overridesResolver", "props", "styles", "display", "position", "flexDirection", "height", "alignSelf", "objectFit", "width", "flexGrow", "ImageListItem", "forwardRef", "inProps", "ref", "children", "className", "cols", "component", "rows", "style", "other", "rowHeight", "gap", "useContext", "undefined", "as", "gridColumnEnd", "gridRowEnd", "marginBottom", "breakInside", "Children", "map", "child", "isValidElement", "process", "env", "NODE_ENV", "console", "error", "join", "type", "cloneElement", "propTypes", "node", "object", "string", "elementType", "sx", "oneOfType", "arrayOf", "func", "bool"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/material/ImageListItem/ImageListItem.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"children\", \"className\", \"cols\", \"component\", \"rows\", \"style\"];\nimport { unstable_composeClasses as composeClasses } from '@mui/base/composeClasses';\nimport { integerPropType } from '@mui/utils';\nimport clsx from 'clsx';\nimport PropTypes from 'prop-types';\nimport * as React from 'react';\nimport { isFragment } from 'react-is';\nimport ImageListContext from '../ImageList/ImageListContext';\nimport styled from '../styles/styled';\nimport useThemeProps from '../styles/useThemeProps';\nimport isMuiElement from '../utils/isMuiElement';\nimport imageListItemClasses, { getImageListItemUtilityClass } from './imageListItemClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    variant\n  } = ownerState;\n  const slots = {\n    root: ['root', variant],\n    img: ['img']\n  };\n  return composeClasses(slots, getImageListItemUtilityClass, classes);\n};\nconst ImageListItemRoot = styled('li', {\n  name: 'MuiImageListItem',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [{\n      [`& .${imageListItemClasses.img}`]: styles.img\n    }, styles.root, styles[ownerState.variant]];\n  }\n})(({\n  ownerState\n}) => _extends({\n  display: 'block',\n  position: 'relative'\n}, ownerState.variant === 'standard' && {\n  // For titlebar under list item\n  display: 'flex',\n  flexDirection: 'column'\n}, ownerState.variant === 'woven' && {\n  height: '100%',\n  alignSelf: 'center',\n  '&:nth-of-type(even)': {\n    height: '70%'\n  }\n}, {\n  [`& .${imageListItemClasses.img}`]: _extends({\n    objectFit: 'cover',\n    width: '100%',\n    height: '100%',\n    display: 'block'\n  }, ownerState.variant === 'standard' && {\n    height: 'auto',\n    flexGrow: 1\n  })\n}));\nconst ImageListItem = /*#__PURE__*/React.forwardRef(function ImageListItem(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiImageListItem'\n  });\n\n  // TODO: - Use jsdoc @default?: \"cols rows default values are for docs only\"\n  const {\n      children,\n      className,\n      cols = 1,\n      component = 'li',\n      rows = 1,\n      style\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    rowHeight = 'auto',\n    gap,\n    variant\n  } = React.useContext(ImageListContext);\n  let height = 'auto';\n  if (variant === 'woven') {\n    height = undefined;\n  } else if (rowHeight !== 'auto') {\n    height = rowHeight * rows + gap * (rows - 1);\n  }\n  const ownerState = _extends({}, props, {\n    cols,\n    component,\n    gap,\n    rowHeight,\n    rows,\n    variant\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(ImageListItemRoot, _extends({\n    as: component,\n    className: clsx(classes.root, classes[variant], className),\n    ref: ref,\n    style: _extends({\n      height,\n      gridColumnEnd: variant !== 'masonry' ? `span ${cols}` : undefined,\n      gridRowEnd: variant !== 'masonry' ? `span ${rows}` : undefined,\n      marginBottom: variant === 'masonry' ? gap : undefined,\n      breakInside: variant === 'masonry' ? 'avoid' : undefined\n    }, style),\n    ownerState: ownerState\n  }, other, {\n    children: React.Children.map(children, child => {\n      if (! /*#__PURE__*/React.isValidElement(child)) {\n        return null;\n      }\n      if (process.env.NODE_ENV !== 'production') {\n        if (isFragment(child)) {\n          console.error([\"MUI: The ImageListItem component doesn't accept a Fragment as a child.\", 'Consider providing an array instead.'].join('\\n'));\n        }\n      }\n      if (child.type === 'img' || isMuiElement(child, ['Image'])) {\n        return /*#__PURE__*/React.cloneElement(child, {\n          className: clsx(classes.img, child.props.className)\n        });\n      }\n      return child;\n    })\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? ImageListItem.propTypes /* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit the d.ts file and run \"yarn proptypes\"     |\n  // ----------------------------------------------------------------------\n  /**\n   * The content of the component, normally an `<img>`.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * Width of the item in number of grid columns.\n   * @default 1\n   */\n  cols: integerPropType,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * Height of the item in number of grid rows.\n   * @default 1\n   */\n  rows: integerPropType,\n  /**\n   * @ignore\n   */\n  style: PropTypes.object,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default ImageListItem;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,CAAC;AACjF,SAASC,uBAAuB,IAAIC,cAAc,QAAQ,0BAA0B;AACpF,SAASC,eAAe,QAAQ,YAAY;AAC5C,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,UAAU;AACrC,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,OAAOC,MAAM,MAAM,kBAAkB;AACrC,OAAOC,aAAa,MAAM,yBAAyB;AACnD,OAAOC,YAAY,MAAM,uBAAuB;AAChD,OAAOC,oBAAoB,IAAIC,4BAA4B,QAAQ,wBAAwB;AAC3F,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,OAAO;IACPC;EACF,CAAC,GAAGF,UAAU;EACd,MAAMG,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEF,OAAO,CAAC;IACvBG,GAAG,EAAE,CAAC,KAAK;EACb,CAAC;EACD,OAAOpB,cAAc,CAACkB,KAAK,EAAEP,4BAA4B,EAAEK,OAAO,CAAC;AACrE,CAAC;AACD,MAAMK,iBAAiB,GAAGd,MAAM,CAAC,IAAI,EAAE;EACrCe,IAAI,EAAE,kBAAkB;EACxBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJX;IACF,CAAC,GAAGU,KAAK;IACT,OAAO,CAAC;MACN,CAAE,MAAKf,oBAAoB,CAACU,GAAI,EAAC,GAAGM,MAAM,CAACN;IAC7C,CAAC,EAAEM,MAAM,CAACP,IAAI,EAAEO,MAAM,CAACX,UAAU,CAACE,OAAO,CAAC,CAAC;EAC7C;AACF,CAAC,CAAC,CAAC,CAAC;EACFF;AACF,CAAC,KAAKlB,QAAQ,CAAC;EACb8B,OAAO,EAAE,OAAO;EAChBC,QAAQ,EAAE;AACZ,CAAC,EAAEb,UAAU,CAACE,OAAO,KAAK,UAAU,IAAI;EACtC;EACAU,OAAO,EAAE,MAAM;EACfE,aAAa,EAAE;AACjB,CAAC,EAAEd,UAAU,CAACE,OAAO,KAAK,OAAO,IAAI;EACnCa,MAAM,EAAE,MAAM;EACdC,SAAS,EAAE,QAAQ;EACnB,qBAAqB,EAAE;IACrBD,MAAM,EAAE;EACV;AACF,CAAC,EAAE;EACD,CAAE,MAAKpB,oBAAoB,CAACU,GAAI,EAAC,GAAGvB,QAAQ,CAAC;IAC3CmC,SAAS,EAAE,OAAO;IAClBC,KAAK,EAAE,MAAM;IACbH,MAAM,EAAE,MAAM;IACdH,OAAO,EAAE;EACX,CAAC,EAAEZ,UAAU,CAACE,OAAO,KAAK,UAAU,IAAI;IACtCa,MAAM,EAAE,MAAM;IACdI,QAAQ,EAAE;EACZ,CAAC;AACH,CAAC,CAAC,CAAC;AACH,MAAMC,aAAa,GAAG,aAAa/B,KAAK,CAACgC,UAAU,CAAC,SAASD,aAAaA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACvF,MAAMb,KAAK,GAAGjB,aAAa,CAAC;IAC1BiB,KAAK,EAAEY,OAAO;IACdf,IAAI,EAAE;EACR,CAAC,CAAC;;EAEF;EACA,MAAM;MACFiB,QAAQ;MACRC,SAAS;MACTC,IAAI,GAAG,CAAC;MACRC,SAAS,GAAG,IAAI;MAChBC,IAAI,GAAG,CAAC;MACRC;IACF,CAAC,GAAGnB,KAAK;IACToB,KAAK,GAAGjD,6BAA6B,CAAC6B,KAAK,EAAE3B,SAAS,CAAC;EACzD,MAAM;IACJgD,SAAS,GAAG,MAAM;IAClBC,GAAG;IACH9B;EACF,CAAC,GAAGb,KAAK,CAAC4C,UAAU,CAAC1C,gBAAgB,CAAC;EACtC,IAAIwB,MAAM,GAAG,MAAM;EACnB,IAAIb,OAAO,KAAK,OAAO,EAAE;IACvBa,MAAM,GAAGmB,SAAS;EACpB,CAAC,MAAM,IAAIH,SAAS,KAAK,MAAM,EAAE;IAC/BhB,MAAM,GAAGgB,SAAS,GAAGH,IAAI,GAAGI,GAAG,IAAIJ,IAAI,GAAG,CAAC,CAAC;EAC9C;EACA,MAAM5B,UAAU,GAAGlB,QAAQ,CAAC,CAAC,CAAC,EAAE4B,KAAK,EAAE;IACrCgB,IAAI;IACJC,SAAS;IACTK,GAAG;IACHD,SAAS;IACTH,IAAI;IACJ1B;EACF,CAAC,CAAC;EACF,MAAMD,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,OAAO,aAAaF,IAAI,CAACQ,iBAAiB,EAAExB,QAAQ,CAAC;IACnDqD,EAAE,EAAER,SAAS;IACbF,SAAS,EAAEtC,IAAI,CAACc,OAAO,CAACG,IAAI,EAAEH,OAAO,CAACC,OAAO,CAAC,EAAEuB,SAAS,CAAC;IAC1DF,GAAG,EAAEA,GAAG;IACRM,KAAK,EAAE/C,QAAQ,CAAC;MACdiC,MAAM;MACNqB,aAAa,EAAElC,OAAO,KAAK,SAAS,GAAI,QAAOwB,IAAK,EAAC,GAAGQ,SAAS;MACjEG,UAAU,EAAEnC,OAAO,KAAK,SAAS,GAAI,QAAO0B,IAAK,EAAC,GAAGM,SAAS;MAC9DI,YAAY,EAAEpC,OAAO,KAAK,SAAS,GAAG8B,GAAG,GAAGE,SAAS;MACrDK,WAAW,EAAErC,OAAO,KAAK,SAAS,GAAG,OAAO,GAAGgC;IACjD,CAAC,EAAEL,KAAK,CAAC;IACT7B,UAAU,EAAEA;EACd,CAAC,EAAE8B,KAAK,EAAE;IACRN,QAAQ,EAAEnC,KAAK,CAACmD,QAAQ,CAACC,GAAG,CAACjB,QAAQ,EAAEkB,KAAK,IAAI;MAC9C,IAAI,EAAE,aAAarD,KAAK,CAACsD,cAAc,CAACD,KAAK,CAAC,EAAE;QAC9C,OAAO,IAAI;MACb;MACA,IAAIE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC,IAAIxD,UAAU,CAACoD,KAAK,CAAC,EAAE;UACrBK,OAAO,CAACC,KAAK,CAAC,CAAC,wEAAwE,EAAE,sCAAsC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9I;MACF;MACA,IAAIP,KAAK,CAACQ,IAAI,KAAK,KAAK,IAAIxD,YAAY,CAACgD,KAAK,EAAE,CAAC,OAAO,CAAC,CAAC,EAAE;QAC1D,OAAO,aAAarD,KAAK,CAAC8D,YAAY,CAACT,KAAK,EAAE;UAC5CjB,SAAS,EAAEtC,IAAI,CAACc,OAAO,CAACI,GAAG,EAAEqC,KAAK,CAAChC,KAAK,CAACe,SAAS;QACpD,CAAC,CAAC;MACJ;MACA,OAAOiB,KAAK;IACd,CAAC;EACH,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG1B,aAAa,CAACgC,SAAS,CAAC,yBAAyB;EACvF;EACA;EACA;EACA;EACA;AACF;AACA;EACE5B,QAAQ,EAAEpC,SAAS,CAACiE,IAAI;EACxB;AACF;AACA;EACEpD,OAAO,EAAEb,SAAS,CAACkE,MAAM;EACzB;AACF;AACA;EACE7B,SAAS,EAAErC,SAAS,CAACmE,MAAM;EAC3B;AACF;AACA;AACA;EACE7B,IAAI,EAAExC,eAAe;EACrB;AACF;AACA;AACA;EACEyC,SAAS,EAAEvC,SAAS,CAACoE,WAAW;EAChC;AACF;AACA;AACA;EACE5B,IAAI,EAAE1C,eAAe;EACrB;AACF;AACA;EACE2C,KAAK,EAAEzC,SAAS,CAACkE,MAAM;EACvB;AACF;AACA;EACEG,EAAE,EAAErE,SAAS,CAACsE,SAAS,CAAC,CAACtE,SAAS,CAACuE,OAAO,CAACvE,SAAS,CAACsE,SAAS,CAAC,CAACtE,SAAS,CAACwE,IAAI,EAAExE,SAAS,CAACkE,MAAM,EAAElE,SAAS,CAACyE,IAAI,CAAC,CAAC,CAAC,EAAEzE,SAAS,CAACwE,IAAI,EAAExE,SAAS,CAACkE,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAelC,aAAa"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}