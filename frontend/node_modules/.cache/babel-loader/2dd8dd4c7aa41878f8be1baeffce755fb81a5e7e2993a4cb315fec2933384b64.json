{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_useEventCallback as useEventCallback } from '@mui/utils';\nimport { extractEventHandlers } from '../utils/extractEventHandlers';\n/**\n * The basic building block for creating custom snackbar.\n *\n * Demos:\n *\n * - [Snackbar](https://mui.com/base-ui/react-snackbar/#hook)\n *\n * API:\n *\n * - [useSnackbar API](https://mui.com/base-ui/react-snackbar/hooks-api/#use-snackbar)\n */\nexport function useSnackbar(parameters = {}) {\n  const {\n    autoHideDuration = null,\n    disableWindowBlurListener = false,\n    onClose,\n    open,\n    resumeHideDuration\n  } = parameters;\n  const timerAutoHide = React.useRef();\n  React.useEffect(() => {\n    if (!open) {\n      return undefined;\n    }\n\n    /**\n     * @param {KeyboardEvent} nativeEvent\n     */\n    function handleKeyDown(nativeEvent) {\n      if (!nativeEvent.defaultPrevented) {\n        // IE11, Edge (prior to using Blink?) use 'Esc'\n        if (nativeEvent.key === 'Escape' || nativeEvent.key === 'Esc') {\n          // not calling `preventDefault` since we don't know if people may ignore this event e.g. a permanently open snackbar\n          onClose == null || onClose(nativeEvent, 'escapeKeyDown');\n        }\n      }\n    }\n    document.addEventListener('keydown', handleKeyDown);\n    return () => {\n      document.removeEventListener('keydown', handleKeyDown);\n    };\n  }, [open, onClose]);\n  const handleClose = useEventCallback((event, reason) => {\n    onClose == null || onClose(event, reason);\n  });\n  const setAutoHideTimer = useEventCallback(autoHideDurationParam => {\n    if (!onClose || autoHideDurationParam == null) {\n      return;\n    }\n    clearTimeout(timerAutoHide.current);\n    timerAutoHide.current = setTimeout(() => {\n      handleClose(null, 'timeout');\n    }, autoHideDurationParam);\n  });\n  React.useEffect(() => {\n    if (open) {\n      setAutoHideTimer(autoHideDuration);\n    }\n    return () => {\n      clearTimeout(timerAutoHide.current);\n    };\n  }, [open, autoHideDuration, setAutoHideTimer]);\n  const handleClickAway = event => {\n    onClose == null || onClose(event, 'clickaway');\n  };\n\n  // Pause the timer when the user is interacting with the Snackbar\n  // or when the user hide the window.\n  const handlePause = () => {\n    clearTimeout(timerAutoHide.current);\n  };\n\n  // Restart the timer when the user is no longer interacting with the Snackbar\n  // or when the window is shown back.\n  const handleResume = React.useCallback(() => {\n    if (autoHideDuration != null) {\n      setAutoHideTimer(resumeHideDuration != null ? resumeHideDuration : autoHideDuration * 0.5);\n    }\n  }, [autoHideDuration, resumeHideDuration, setAutoHideTimer]);\n  const createHandleBlur = otherHandlers => event => {\n    const onBlurCallback = otherHandlers.onBlur;\n    onBlurCallback == null || onBlurCallback(event);\n    handleResume();\n  };\n  const createHandleFocus = otherHandlers => event => {\n    const onFocusCallback = otherHandlers.onFocus;\n    onFocusCallback == null || onFocusCallback(event);\n    handlePause();\n  };\n  const createMouseEnter = otherHandlers => event => {\n    const onMouseEnterCallback = otherHandlers.onMouseEnter;\n    onMouseEnterCallback == null || onMouseEnterCallback(event);\n    handlePause();\n  };\n  const createMouseLeave = otherHandlers => event => {\n    const onMouseLeaveCallback = otherHandlers.onMouseLeave;\n    onMouseLeaveCallback == null || onMouseLeaveCallback(event);\n    handleResume();\n  };\n  React.useEffect(() => {\n    // TODO: window global should be refactored here\n    if (!disableWindowBlurListener && open) {\n      window.addEventListener('focus', handleResume);\n      window.addEventListener('blur', handlePause);\n      return () => {\n        window.removeEventListener('focus', handleResume);\n        window.removeEventListener('blur', handlePause);\n      };\n    }\n    return undefined;\n  }, [disableWindowBlurListener, handleResume, open]);\n  const getRootProps = (externalProps = {}) => {\n    const externalEventHandlers = _extends({}, extractEventHandlers(parameters), extractEventHandlers(externalProps));\n    return _extends({\n      // ClickAwayListener adds an `onClick` prop which results in the alert not being announced.\n      // See https://github.com/mui/material-ui/issues/29080\n      role: 'presentation'\n    }, externalProps, externalEventHandlers, {\n      onBlur: createHandleBlur(externalEventHandlers),\n      onFocus: createHandleFocus(externalEventHandlers),\n      onMouseEnter: createMouseEnter(externalEventHandlers),\n      onMouseLeave: createMouseLeave(externalEventHandlers)\n    });\n  };\n  return {\n    getRootProps,\n    onClickAway: handleClickAway\n  };\n}", "map": {"version": 3, "names": ["_extends", "React", "unstable_useEventCallback", "useEventCallback", "extractEventHandlers", "useSnackbar", "parameters", "autoHideDuration", "disableWindowBlurListener", "onClose", "open", "resumeHideDuration", "timerAutoHide", "useRef", "useEffect", "undefined", "handleKeyDown", "nativeEvent", "defaultPrevented", "key", "document", "addEventListener", "removeEventListener", "handleClose", "event", "reason", "setAutoHideTimer", "autoHideDurationParam", "clearTimeout", "current", "setTimeout", "handleClickAway", "handlePause", "handleResume", "useCallback", "createHandleBlur", "otherHandlers", "onBlurCallback", "onBlur", "createHandleFocus", "onFocusCallback", "onFocus", "createMouseEnter", "onMouseEnterCallback", "onMouseEnter", "createMouseLeave", "onMouseLeaveCallback", "onMouseLeave", "window", "getRootProps", "externalProps", "externalEventHandlers", "role", "onClickAway"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/base/useSnackbar/useSnackbar.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_useEventCallback as useEventCallback } from '@mui/utils';\nimport { extractEventHandlers } from '../utils/extractEventHandlers';\n/**\n * The basic building block for creating custom snackbar.\n *\n * Demos:\n *\n * - [Snackbar](https://mui.com/base-ui/react-snackbar/#hook)\n *\n * API:\n *\n * - [useSnackbar API](https://mui.com/base-ui/react-snackbar/hooks-api/#use-snackbar)\n */\nexport function useSnackbar(parameters = {}) {\n  const {\n    autoHideDuration = null,\n    disableWindowBlurListener = false,\n    onClose,\n    open,\n    resumeHideDuration\n  } = parameters;\n  const timerAutoHide = React.useRef();\n  React.useEffect(() => {\n    if (!open) {\n      return undefined;\n    }\n\n    /**\n     * @param {KeyboardEvent} nativeEvent\n     */\n    function handleKeyDown(nativeEvent) {\n      if (!nativeEvent.defaultPrevented) {\n        // IE11, Edge (prior to using Blink?) use 'Esc'\n        if (nativeEvent.key === 'Escape' || nativeEvent.key === 'Esc') {\n          // not calling `preventDefault` since we don't know if people may ignore this event e.g. a permanently open snackbar\n          onClose == null || onClose(nativeEvent, 'escapeKeyDown');\n        }\n      }\n    }\n    document.addEventListener('keydown', handleKeyDown);\n    return () => {\n      document.removeEventListener('keydown', handleKeyDown);\n    };\n  }, [open, onClose]);\n  const handleClose = useEventCallback((event, reason) => {\n    onClose == null || onClose(event, reason);\n  });\n  const setAutoHideTimer = useEventCallback(autoHideDurationParam => {\n    if (!onClose || autoHideDurationParam == null) {\n      return;\n    }\n    clearTimeout(timerAutoHide.current);\n    timerAutoHide.current = setTimeout(() => {\n      handleClose(null, 'timeout');\n    }, autoHideDurationParam);\n  });\n  React.useEffect(() => {\n    if (open) {\n      setAutoHideTimer(autoHideDuration);\n    }\n    return () => {\n      clearTimeout(timerAutoHide.current);\n    };\n  }, [open, autoHideDuration, setAutoHideTimer]);\n  const handleClickAway = event => {\n    onClose == null || onClose(event, 'clickaway');\n  };\n\n  // Pause the timer when the user is interacting with the Snackbar\n  // or when the user hide the window.\n  const handlePause = () => {\n    clearTimeout(timerAutoHide.current);\n  };\n\n  // Restart the timer when the user is no longer interacting with the Snackbar\n  // or when the window is shown back.\n  const handleResume = React.useCallback(() => {\n    if (autoHideDuration != null) {\n      setAutoHideTimer(resumeHideDuration != null ? resumeHideDuration : autoHideDuration * 0.5);\n    }\n  }, [autoHideDuration, resumeHideDuration, setAutoHideTimer]);\n  const createHandleBlur = otherHandlers => event => {\n    const onBlurCallback = otherHandlers.onBlur;\n    onBlurCallback == null || onBlurCallback(event);\n    handleResume();\n  };\n  const createHandleFocus = otherHandlers => event => {\n    const onFocusCallback = otherHandlers.onFocus;\n    onFocusCallback == null || onFocusCallback(event);\n    handlePause();\n  };\n  const createMouseEnter = otherHandlers => event => {\n    const onMouseEnterCallback = otherHandlers.onMouseEnter;\n    onMouseEnterCallback == null || onMouseEnterCallback(event);\n    handlePause();\n  };\n  const createMouseLeave = otherHandlers => event => {\n    const onMouseLeaveCallback = otherHandlers.onMouseLeave;\n    onMouseLeaveCallback == null || onMouseLeaveCallback(event);\n    handleResume();\n  };\n  React.useEffect(() => {\n    // TODO: window global should be refactored here\n    if (!disableWindowBlurListener && open) {\n      window.addEventListener('focus', handleResume);\n      window.addEventListener('blur', handlePause);\n      return () => {\n        window.removeEventListener('focus', handleResume);\n        window.removeEventListener('blur', handlePause);\n      };\n    }\n    return undefined;\n  }, [disableWindowBlurListener, handleResume, open]);\n  const getRootProps = (externalProps = {}) => {\n    const externalEventHandlers = _extends({}, extractEventHandlers(parameters), extractEventHandlers(externalProps));\n    return _extends({\n      // ClickAwayListener adds an `onClick` prop which results in the alert not being announced.\n      // See https://github.com/mui/material-ui/issues/29080\n      role: 'presentation'\n    }, externalProps, externalEventHandlers, {\n      onBlur: createHandleBlur(externalEventHandlers),\n      onFocus: createHandleFocus(externalEventHandlers),\n      onMouseEnter: createMouseEnter(externalEventHandlers),\n      onMouseLeave: createMouseLeave(externalEventHandlers)\n    });\n  };\n  return {\n    getRootProps,\n    onClickAway: handleClickAway\n  };\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,yBAAyB,IAAIC,gBAAgB,QAAQ,YAAY;AAC1E,SAASC,oBAAoB,QAAQ,+BAA+B;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,WAAWA,CAACC,UAAU,GAAG,CAAC,CAAC,EAAE;EAC3C,MAAM;IACJC,gBAAgB,GAAG,IAAI;IACvBC,yBAAyB,GAAG,KAAK;IACjCC,OAAO;IACPC,IAAI;IACJC;EACF,CAAC,GAAGL,UAAU;EACd,MAAMM,aAAa,GAAGX,KAAK,CAACY,MAAM,CAAC,CAAC;EACpCZ,KAAK,CAACa,SAAS,CAAC,MAAM;IACpB,IAAI,CAACJ,IAAI,EAAE;MACT,OAAOK,SAAS;IAClB;;IAEA;AACJ;AACA;IACI,SAASC,aAAaA,CAACC,WAAW,EAAE;MAClC,IAAI,CAACA,WAAW,CAACC,gBAAgB,EAAE;QACjC;QACA,IAAID,WAAW,CAACE,GAAG,KAAK,QAAQ,IAAIF,WAAW,CAACE,GAAG,KAAK,KAAK,EAAE;UAC7D;UACAV,OAAO,IAAI,IAAI,IAAIA,OAAO,CAACQ,WAAW,EAAE,eAAe,CAAC;QAC1D;MACF;IACF;IACAG,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAEL,aAAa,CAAC;IACnD,OAAO,MAAM;MACXI,QAAQ,CAACE,mBAAmB,CAAC,SAAS,EAAEN,aAAa,CAAC;IACxD,CAAC;EACH,CAAC,EAAE,CAACN,IAAI,EAAED,OAAO,CAAC,CAAC;EACnB,MAAMc,WAAW,GAAGpB,gBAAgB,CAAC,CAACqB,KAAK,EAAEC,MAAM,KAAK;IACtDhB,OAAO,IAAI,IAAI,IAAIA,OAAO,CAACe,KAAK,EAAEC,MAAM,CAAC;EAC3C,CAAC,CAAC;EACF,MAAMC,gBAAgB,GAAGvB,gBAAgB,CAACwB,qBAAqB,IAAI;IACjE,IAAI,CAAClB,OAAO,IAAIkB,qBAAqB,IAAI,IAAI,EAAE;MAC7C;IACF;IACAC,YAAY,CAAChB,aAAa,CAACiB,OAAO,CAAC;IACnCjB,aAAa,CAACiB,OAAO,GAAGC,UAAU,CAAC,MAAM;MACvCP,WAAW,CAAC,IAAI,EAAE,SAAS,CAAC;IAC9B,CAAC,EAAEI,qBAAqB,CAAC;EAC3B,CAAC,CAAC;EACF1B,KAAK,CAACa,SAAS,CAAC,MAAM;IACpB,IAAIJ,IAAI,EAAE;MACRgB,gBAAgB,CAACnB,gBAAgB,CAAC;IACpC;IACA,OAAO,MAAM;MACXqB,YAAY,CAAChB,aAAa,CAACiB,OAAO,CAAC;IACrC,CAAC;EACH,CAAC,EAAE,CAACnB,IAAI,EAAEH,gBAAgB,EAAEmB,gBAAgB,CAAC,CAAC;EAC9C,MAAMK,eAAe,GAAGP,KAAK,IAAI;IAC/Bf,OAAO,IAAI,IAAI,IAAIA,OAAO,CAACe,KAAK,EAAE,WAAW,CAAC;EAChD,CAAC;;EAED;EACA;EACA,MAAMQ,WAAW,GAAGA,CAAA,KAAM;IACxBJ,YAAY,CAAChB,aAAa,CAACiB,OAAO,CAAC;EACrC,CAAC;;EAED;EACA;EACA,MAAMI,YAAY,GAAGhC,KAAK,CAACiC,WAAW,CAAC,MAAM;IAC3C,IAAI3B,gBAAgB,IAAI,IAAI,EAAE;MAC5BmB,gBAAgB,CAACf,kBAAkB,IAAI,IAAI,GAAGA,kBAAkB,GAAGJ,gBAAgB,GAAG,GAAG,CAAC;IAC5F;EACF,CAAC,EAAE,CAACA,gBAAgB,EAAEI,kBAAkB,EAAEe,gBAAgB,CAAC,CAAC;EAC5D,MAAMS,gBAAgB,GAAGC,aAAa,IAAIZ,KAAK,IAAI;IACjD,MAAMa,cAAc,GAAGD,aAAa,CAACE,MAAM;IAC3CD,cAAc,IAAI,IAAI,IAAIA,cAAc,CAACb,KAAK,CAAC;IAC/CS,YAAY,CAAC,CAAC;EAChB,CAAC;EACD,MAAMM,iBAAiB,GAAGH,aAAa,IAAIZ,KAAK,IAAI;IAClD,MAAMgB,eAAe,GAAGJ,aAAa,CAACK,OAAO;IAC7CD,eAAe,IAAI,IAAI,IAAIA,eAAe,CAAChB,KAAK,CAAC;IACjDQ,WAAW,CAAC,CAAC;EACf,CAAC;EACD,MAAMU,gBAAgB,GAAGN,aAAa,IAAIZ,KAAK,IAAI;IACjD,MAAMmB,oBAAoB,GAAGP,aAAa,CAACQ,YAAY;IACvDD,oBAAoB,IAAI,IAAI,IAAIA,oBAAoB,CAACnB,KAAK,CAAC;IAC3DQ,WAAW,CAAC,CAAC;EACf,CAAC;EACD,MAAMa,gBAAgB,GAAGT,aAAa,IAAIZ,KAAK,IAAI;IACjD,MAAMsB,oBAAoB,GAAGV,aAAa,CAACW,YAAY;IACvDD,oBAAoB,IAAI,IAAI,IAAIA,oBAAoB,CAACtB,KAAK,CAAC;IAC3DS,YAAY,CAAC,CAAC;EAChB,CAAC;EACDhC,KAAK,CAACa,SAAS,CAAC,MAAM;IACpB;IACA,IAAI,CAACN,yBAAyB,IAAIE,IAAI,EAAE;MACtCsC,MAAM,CAAC3B,gBAAgB,CAAC,OAAO,EAAEY,YAAY,CAAC;MAC9Ce,MAAM,CAAC3B,gBAAgB,CAAC,MAAM,EAAEW,WAAW,CAAC;MAC5C,OAAO,MAAM;QACXgB,MAAM,CAAC1B,mBAAmB,CAAC,OAAO,EAAEW,YAAY,CAAC;QACjDe,MAAM,CAAC1B,mBAAmB,CAAC,MAAM,EAAEU,WAAW,CAAC;MACjD,CAAC;IACH;IACA,OAAOjB,SAAS;EAClB,CAAC,EAAE,CAACP,yBAAyB,EAAEyB,YAAY,EAAEvB,IAAI,CAAC,CAAC;EACnD,MAAMuC,YAAY,GAAGA,CAACC,aAAa,GAAG,CAAC,CAAC,KAAK;IAC3C,MAAMC,qBAAqB,GAAGnD,QAAQ,CAAC,CAAC,CAAC,EAAEI,oBAAoB,CAACE,UAAU,CAAC,EAAEF,oBAAoB,CAAC8C,aAAa,CAAC,CAAC;IACjH,OAAOlD,QAAQ,CAAC;MACd;MACA;MACAoD,IAAI,EAAE;IACR,CAAC,EAAEF,aAAa,EAAEC,qBAAqB,EAAE;MACvCb,MAAM,EAAEH,gBAAgB,CAACgB,qBAAqB,CAAC;MAC/CV,OAAO,EAAEF,iBAAiB,CAACY,qBAAqB,CAAC;MACjDP,YAAY,EAAEF,gBAAgB,CAACS,qBAAqB,CAAC;MACrDJ,YAAY,EAAEF,gBAAgB,CAACM,qBAAqB;IACtD,CAAC,CAAC;EACJ,CAAC;EACD,OAAO;IACLF,YAAY;IACZI,WAAW,EAAEtB;EACf,CAAC;AACH"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}