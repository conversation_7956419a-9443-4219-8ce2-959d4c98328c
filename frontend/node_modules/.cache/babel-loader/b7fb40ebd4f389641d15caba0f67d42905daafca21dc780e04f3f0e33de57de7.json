{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport { TabsContext } from '../Tabs/TabsContext';\nimport { CompoundComponentContext } from '../useCompound';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n/**\n * Sets up the contexts for the underlying Tab and TabPanel components.\n *\n * @ignore - do not document.\n */\nexport function TabsProvider(props) {\n  const {\n    value: valueProp,\n    children\n  } = props;\n  const {\n    direction,\n    getItemIndex,\n    onSelected,\n    orientation,\n    registerItem,\n    registerTabIdLookup,\n    selectionFollowsFocus,\n    totalSubitemCount,\n    value,\n    getTabId,\n    getTabPanelId\n  } = valueProp;\n  const compoundComponentContextValue = React.useMemo(() => ({\n    getItemIndex,\n    registerItem,\n    totalSubitemCount\n  }), [registerItem, getItemIndex, totalSubitemCount]);\n  const tabsContextValue = React.useMemo(() => ({\n    direction,\n    getTabId,\n    getTabPanelId,\n    onSelected,\n    orientation,\n    registerTabIdLookup,\n    selectionFollowsFocus,\n    value\n  }), [direction, getTabId, getTabPanelId, onSelected, orientation, registerTabIdLookup, selectionFollowsFocus, value]);\n  return /*#__PURE__*/_jsx(CompoundComponentContext.Provider, {\n    value: compoundComponentContextValue,\n    children: /*#__PURE__*/_jsx(TabsContext.Provider, {\n      value: tabsContextValue,\n      children: children\n    })\n  });\n}", "map": {"version": 3, "names": ["React", "TabsContext", "CompoundComponentContext", "jsx", "_jsx", "TabsProvider", "props", "value", "valueProp", "children", "direction", "getItemIndex", "onSelected", "orientation", "registerItem", "registerTabIdLookup", "selectionFollowsFocus", "totalSubitemCount", "getTabId", "getTabPanelId", "compoundComponentContextValue", "useMemo", "tabsContextValue", "Provider"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/base/useTabs/TabsProvider.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport { TabsContext } from '../Tabs/TabsContext';\nimport { CompoundComponentContext } from '../useCompound';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n/**\n * Sets up the contexts for the underlying Tab and TabPanel components.\n *\n * @ignore - do not document.\n */\nexport function TabsProvider(props) {\n  const {\n    value: valueProp,\n    children\n  } = props;\n  const {\n    direction,\n    getItemIndex,\n    onSelected,\n    orientation,\n    registerItem,\n    registerTabIdLookup,\n    selectionFollowsFocus,\n    totalSubitemCount,\n    value,\n    getTabId,\n    getTabPanelId\n  } = valueProp;\n  const compoundComponentContextValue = React.useMemo(() => ({\n    getItemIndex,\n    registerItem,\n    totalSubitemCount\n  }), [registerItem, getItemIndex, totalSubitemCount]);\n  const tabsContextValue = React.useMemo(() => ({\n    direction,\n    getTabId,\n    getTabPanelId,\n    onSelected,\n    orientation,\n    registerTabIdLookup,\n    selectionFollowsFocus,\n    value\n  }), [direction, getTabId, getTabPanelId, onSelected, orientation, registerTabIdLookup, selectionFollowsFocus, value]);\n  return /*#__PURE__*/_jsx(CompoundComponentContext.Provider, {\n    value: compoundComponentContextValue,\n    children: /*#__PURE__*/_jsx(TabsContext.Provider, {\n      value: tabsContextValue,\n      children: children\n    })\n  });\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,WAAW,QAAQ,qBAAqB;AACjD,SAASC,wBAAwB,QAAQ,gBAAgB;AACzD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,YAAYA,CAACC,KAAK,EAAE;EAClC,MAAM;IACJC,KAAK,EAAEC,SAAS;IAChBC;EACF,CAAC,GAAGH,KAAK;EACT,MAAM;IACJI,SAAS;IACTC,YAAY;IACZC,UAAU;IACVC,WAAW;IACXC,YAAY;IACZC,mBAAmB;IACnBC,qBAAqB;IACrBC,iBAAiB;IACjBV,KAAK;IACLW,QAAQ;IACRC;EACF,CAAC,GAAGX,SAAS;EACb,MAAMY,6BAA6B,GAAGpB,KAAK,CAACqB,OAAO,CAAC,OAAO;IACzDV,YAAY;IACZG,YAAY;IACZG;EACF,CAAC,CAAC,EAAE,CAACH,YAAY,EAAEH,YAAY,EAAEM,iBAAiB,CAAC,CAAC;EACpD,MAAMK,gBAAgB,GAAGtB,KAAK,CAACqB,OAAO,CAAC,OAAO;IAC5CX,SAAS;IACTQ,QAAQ;IACRC,aAAa;IACbP,UAAU;IACVC,WAAW;IACXE,mBAAmB;IACnBC,qBAAqB;IACrBT;EACF,CAAC,CAAC,EAAE,CAACG,SAAS,EAAEQ,QAAQ,EAAEC,aAAa,EAAEP,UAAU,EAAEC,WAAW,EAAEE,mBAAmB,EAAEC,qBAAqB,EAAET,KAAK,CAAC,CAAC;EACrH,OAAO,aAAaH,IAAI,CAACF,wBAAwB,CAACqB,QAAQ,EAAE;IAC1DhB,KAAK,EAAEa,6BAA6B;IACpCX,QAAQ,EAAE,aAAaL,IAAI,CAACH,WAAW,CAACsB,QAAQ,EAAE;MAChDhB,KAAK,EAAEe,gBAAgB;MACvBb,QAAQ,EAAEA;IACZ,CAAC;EACH,CAAC,CAAC;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}