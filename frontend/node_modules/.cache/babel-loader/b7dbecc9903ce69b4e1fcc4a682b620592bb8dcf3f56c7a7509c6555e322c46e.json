{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"BackdropProps\"],\n  _excluded2 = [\"anchor\", \"BackdropProps\", \"children\", \"className\", \"elevation\", \"hideBackdrop\", \"ModalProps\", \"onClose\", \"open\", \"PaperProps\", \"SlideProps\", \"TransitionComponent\", \"transitionDuration\", \"variant\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { integerPropType } from '@mui/utils';\nimport { unstable_composeClasses as composeClasses } from '@mui/base/composeClasses';\nimport Modal from '../Modal';\nimport Slide from '../Slide';\nimport Paper from '../Paper';\nimport capitalize from '../utils/capitalize';\nimport useTheme from '../styles/useTheme';\nimport useThemeProps from '../styles/useThemeProps';\nimport styled, { rootShouldForwardProp } from '../styles/styled';\nimport { getDrawerUtilityClass } from './drawerClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst overridesResolver = (props, styles) => {\n  const {\n    ownerState\n  } = props;\n  return [styles.root, (ownerState.variant === 'permanent' || ownerState.variant === 'persistent') && styles.docked, styles.modal];\n};\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    anchor,\n    variant\n  } = ownerState;\n  const slots = {\n    root: ['root'],\n    docked: [(variant === 'permanent' || variant === 'persistent') && 'docked'],\n    modal: ['modal'],\n    paper: ['paper', `paperAnchor${capitalize(anchor)}`, variant !== 'temporary' && `paperAnchorDocked${capitalize(anchor)}`]\n  };\n  return composeClasses(slots, getDrawerUtilityClass, classes);\n};\nconst DrawerRoot = styled(Modal, {\n  name: 'MuiDrawer',\n  slot: 'Root',\n  overridesResolver\n})(({\n  theme\n}) => ({\n  zIndex: (theme.vars || theme).zIndex.drawer\n}));\nconst DrawerDockedRoot = styled('div', {\n  shouldForwardProp: rootShouldForwardProp,\n  name: 'MuiDrawer',\n  slot: 'Docked',\n  skipVariantsResolver: false,\n  overridesResolver\n})({\n  flex: '0 0 auto'\n});\nconst DrawerPaper = styled(Paper, {\n  name: 'MuiDrawer',\n  slot: 'Paper',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.paper, styles[`paperAnchor${capitalize(ownerState.anchor)}`], ownerState.variant !== 'temporary' && styles[`paperAnchorDocked${capitalize(ownerState.anchor)}`]];\n  }\n})(({\n  theme,\n  ownerState\n}) => _extends({\n  overflowY: 'auto',\n  display: 'flex',\n  flexDirection: 'column',\n  height: '100%',\n  flex: '1 0 auto',\n  zIndex: (theme.vars || theme).zIndex.drawer,\n  // Add iOS momentum scrolling for iOS < 13.0\n  WebkitOverflowScrolling: 'touch',\n  // temporary style\n  position: 'fixed',\n  top: 0,\n  // We disable the focus ring for mouse, touch and keyboard users.\n  // At some point, it would be better to keep it for keyboard users.\n  // :focus-ring CSS pseudo-class will help.\n  outline: 0\n}, ownerState.anchor === 'left' && {\n  left: 0\n}, ownerState.anchor === 'top' && {\n  top: 0,\n  left: 0,\n  right: 0,\n  height: 'auto',\n  maxHeight: '100%'\n}, ownerState.anchor === 'right' && {\n  right: 0\n}, ownerState.anchor === 'bottom' && {\n  top: 'auto',\n  left: 0,\n  bottom: 0,\n  right: 0,\n  height: 'auto',\n  maxHeight: '100%'\n}, ownerState.anchor === 'left' && ownerState.variant !== 'temporary' && {\n  borderRight: `1px solid ${(theme.vars || theme).palette.divider}`\n}, ownerState.anchor === 'top' && ownerState.variant !== 'temporary' && {\n  borderBottom: `1px solid ${(theme.vars || theme).palette.divider}`\n}, ownerState.anchor === 'right' && ownerState.variant !== 'temporary' && {\n  borderLeft: `1px solid ${(theme.vars || theme).palette.divider}`\n}, ownerState.anchor === 'bottom' && ownerState.variant !== 'temporary' && {\n  borderTop: `1px solid ${(theme.vars || theme).palette.divider}`\n}));\nconst oppositeDirection = {\n  left: 'right',\n  right: 'left',\n  top: 'down',\n  bottom: 'up'\n};\nexport function isHorizontal(anchor) {\n  return ['left', 'right'].indexOf(anchor) !== -1;\n}\nexport function getAnchor(theme, anchor) {\n  return theme.direction === 'rtl' && isHorizontal(anchor) ? oppositeDirection[anchor] : anchor;\n}\n\n/**\n * The props of the [Modal](/material-ui/api/modal/) component are available\n * when `variant=\"temporary\"` is set.\n */\nconst Drawer = /*#__PURE__*/React.forwardRef(function Drawer(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiDrawer'\n  });\n  const theme = useTheme();\n  const defaultTransitionDuration = {\n    enter: theme.transitions.duration.enteringScreen,\n    exit: theme.transitions.duration.leavingScreen\n  };\n  const {\n      anchor: anchorProp = 'left',\n      BackdropProps,\n      children,\n      className,\n      elevation = 16,\n      hideBackdrop = false,\n      ModalProps: {\n        BackdropProps: BackdropPropsProp\n      } = {},\n      onClose,\n      open = false,\n      PaperProps = {},\n      SlideProps,\n      // eslint-disable-next-line react/prop-types\n      TransitionComponent = Slide,\n      transitionDuration = defaultTransitionDuration,\n      variant = 'temporary'\n    } = props,\n    ModalProps = _objectWithoutPropertiesLoose(props.ModalProps, _excluded),\n    other = _objectWithoutPropertiesLoose(props, _excluded2);\n\n  // Let's assume that the Drawer will always be rendered on user space.\n  // We use this state is order to skip the appear transition during the\n  // initial mount of the component.\n  const mounted = React.useRef(false);\n  React.useEffect(() => {\n    mounted.current = true;\n  }, []);\n  const anchorInvariant = getAnchor(theme, anchorProp);\n  const anchor = anchorProp;\n  const ownerState = _extends({}, props, {\n    anchor,\n    elevation,\n    open,\n    variant\n  }, other);\n  const classes = useUtilityClasses(ownerState);\n  const drawer = /*#__PURE__*/_jsx(DrawerPaper, _extends({\n    elevation: variant === 'temporary' ? elevation : 0,\n    square: true\n  }, PaperProps, {\n    className: clsx(classes.paper, PaperProps.className),\n    ownerState: ownerState,\n    children: children\n  }));\n  if (variant === 'permanent') {\n    return /*#__PURE__*/_jsx(DrawerDockedRoot, _extends({\n      className: clsx(classes.root, classes.docked, className),\n      ownerState: ownerState,\n      ref: ref\n    }, other, {\n      children: drawer\n    }));\n  }\n  const slidingDrawer = /*#__PURE__*/_jsx(TransitionComponent, _extends({\n    in: open,\n    direction: oppositeDirection[anchorInvariant],\n    timeout: transitionDuration,\n    appear: mounted.current\n  }, SlideProps, {\n    children: drawer\n  }));\n  if (variant === 'persistent') {\n    return /*#__PURE__*/_jsx(DrawerDockedRoot, _extends({\n      className: clsx(classes.root, classes.docked, className),\n      ownerState: ownerState,\n      ref: ref\n    }, other, {\n      children: slidingDrawer\n    }));\n  }\n\n  // variant === temporary\n  return /*#__PURE__*/_jsx(DrawerRoot, _extends({\n    BackdropProps: _extends({}, BackdropProps, BackdropPropsProp, {\n      transitionDuration\n    }),\n    className: clsx(classes.root, classes.modal, className),\n    open: open,\n    ownerState: ownerState,\n    onClose: onClose,\n    hideBackdrop: hideBackdrop,\n    ref: ref\n  }, other, ModalProps, {\n    children: slidingDrawer\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? Drawer.propTypes /* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit the d.ts file and run \"yarn proptypes\"     |\n  // ----------------------------------------------------------------------\n  /**\n   * Side from which the drawer will appear.\n   * @default 'left'\n   */\n  anchor: PropTypes.oneOf(['bottom', 'left', 'right', 'top']),\n  /**\n   * @ignore\n   */\n  BackdropProps: PropTypes.object,\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The elevation of the drawer.\n   * @default 16\n   */\n  elevation: integerPropType,\n  /**\n   * If `true`, the backdrop is not rendered.\n   * @default false\n   */\n  hideBackdrop: PropTypes.bool,\n  /**\n   * Props applied to the [`Modal`](/material-ui/api/modal/) element.\n   * @default {}\n   */\n  ModalProps: PropTypes.object,\n  /**\n   * Callback fired when the component requests to be closed.\n   * The `reason` parameter can optionally be used to control the response to `onClose`.\n   *\n   * @param {object} event The event source of the callback.\n   * @param {string} reason Can be: `\"escapeKeyDown\"`, `\"backdropClick\"`.\n   */\n  onClose: PropTypes.func,\n  /**\n   * If `true`, the component is shown.\n   * @default false\n   */\n  open: PropTypes.bool,\n  /**\n   * Props applied to the [`Paper`](/material-ui/api/paper/) element.\n   * @default {}\n   */\n  PaperProps: PropTypes.object,\n  /**\n   * Props applied to the [`Slide`](/material-ui/api/slide/) element.\n   */\n  SlideProps: PropTypes.object,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The duration for the transition, in milliseconds.\n   * You may specify a single timeout for all transitions, or individually with an object.\n   * @default {\n   *   enter: theme.transitions.duration.enteringScreen,\n   *   exit: theme.transitions.duration.leavingScreen,\n   * }\n   */\n  transitionDuration: PropTypes.oneOfType([PropTypes.number, PropTypes.shape({\n    appear: PropTypes.number,\n    enter: PropTypes.number,\n    exit: PropTypes.number\n  })]),\n  /**\n   * The variant to use.\n   * @default 'temporary'\n   */\n  variant: PropTypes.oneOf(['permanent', 'persistent', 'temporary'])\n} : void 0;\nexport default Drawer;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "_excluded2", "React", "PropTypes", "clsx", "integerPropType", "unstable_composeClasses", "composeClasses", "Modal", "Slide", "Paper", "capitalize", "useTheme", "useThemeProps", "styled", "rootShouldForwardProp", "getDrawerUtilityClass", "jsx", "_jsx", "overridesResolver", "props", "styles", "ownerState", "root", "variant", "docked", "modal", "useUtilityClasses", "classes", "anchor", "slots", "paper", "Drawer<PERSON><PERSON>", "name", "slot", "theme", "zIndex", "vars", "drawer", "DrawerDockedRoot", "shouldForwardProp", "skipVariantsResolver", "flex", "Drawer<PERSON><PERSON>", "overflowY", "display", "flexDirection", "height", "WebkitOverflowScrolling", "position", "top", "outline", "left", "right", "maxHeight", "bottom", "borderRight", "palette", "divider", "borderBottom", "borderLeft", "borderTop", "oppositeDirection", "isHorizontal", "indexOf", "getAnchor", "direction", "Drawer", "forwardRef", "inProps", "ref", "defaultTransitionDuration", "enter", "transitions", "duration", "enteringScreen", "exit", "leavingScreen", "anchorProp", "BackdropProps", "children", "className", "elevation", "hideBackdrop", "ModalProps", "BackdropPropsProp", "onClose", "open", "PaperProps", "SlideProps", "TransitionComponent", "transitionDuration", "other", "mounted", "useRef", "useEffect", "current", "anchorInvariant", "square", "sliding<PERSON><PERSON><PERSON>", "in", "timeout", "appear", "process", "env", "NODE_ENV", "propTypes", "oneOf", "object", "node", "string", "bool", "func", "sx", "oneOfType", "arrayOf", "number", "shape"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/material/Drawer/Drawer.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"BackdropProps\"],\n  _excluded2 = [\"anchor\", \"BackdropProps\", \"children\", \"className\", \"elevation\", \"hideBackdrop\", \"ModalProps\", \"onClose\", \"open\", \"PaperProps\", \"SlideProps\", \"TransitionComponent\", \"transitionDuration\", \"variant\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { integerPropType } from '@mui/utils';\nimport { unstable_composeClasses as composeClasses } from '@mui/base/composeClasses';\nimport Modal from '../Modal';\nimport Slide from '../Slide';\nimport Paper from '../Paper';\nimport capitalize from '../utils/capitalize';\nimport useTheme from '../styles/useTheme';\nimport useThemeProps from '../styles/useThemeProps';\nimport styled, { rootShouldForwardProp } from '../styles/styled';\nimport { getDrawerUtilityClass } from './drawerClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst overridesResolver = (props, styles) => {\n  const {\n    ownerState\n  } = props;\n  return [styles.root, (ownerState.variant === 'permanent' || ownerState.variant === 'persistent') && styles.docked, styles.modal];\n};\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    anchor,\n    variant\n  } = ownerState;\n  const slots = {\n    root: ['root'],\n    docked: [(variant === 'permanent' || variant === 'persistent') && 'docked'],\n    modal: ['modal'],\n    paper: ['paper', `paperAnchor${capitalize(anchor)}`, variant !== 'temporary' && `paperAnchorDocked${capitalize(anchor)}`]\n  };\n  return composeClasses(slots, getDrawerUtilityClass, classes);\n};\nconst DrawerRoot = styled(Modal, {\n  name: 'MuiDrawer',\n  slot: 'Root',\n  overridesResolver\n})(({\n  theme\n}) => ({\n  zIndex: (theme.vars || theme).zIndex.drawer\n}));\nconst DrawerDockedRoot = styled('div', {\n  shouldForwardProp: rootShouldForwardProp,\n  name: 'MuiDrawer',\n  slot: 'Docked',\n  skipVariantsResolver: false,\n  overridesResolver\n})({\n  flex: '0 0 auto'\n});\nconst DrawerPaper = styled(Paper, {\n  name: 'MuiDrawer',\n  slot: 'Paper',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.paper, styles[`paperAnchor${capitalize(ownerState.anchor)}`], ownerState.variant !== 'temporary' && styles[`paperAnchorDocked${capitalize(ownerState.anchor)}`]];\n  }\n})(({\n  theme,\n  ownerState\n}) => _extends({\n  overflowY: 'auto',\n  display: 'flex',\n  flexDirection: 'column',\n  height: '100%',\n  flex: '1 0 auto',\n  zIndex: (theme.vars || theme).zIndex.drawer,\n  // Add iOS momentum scrolling for iOS < 13.0\n  WebkitOverflowScrolling: 'touch',\n  // temporary style\n  position: 'fixed',\n  top: 0,\n  // We disable the focus ring for mouse, touch and keyboard users.\n  // At some point, it would be better to keep it for keyboard users.\n  // :focus-ring CSS pseudo-class will help.\n  outline: 0\n}, ownerState.anchor === 'left' && {\n  left: 0\n}, ownerState.anchor === 'top' && {\n  top: 0,\n  left: 0,\n  right: 0,\n  height: 'auto',\n  maxHeight: '100%'\n}, ownerState.anchor === 'right' && {\n  right: 0\n}, ownerState.anchor === 'bottom' && {\n  top: 'auto',\n  left: 0,\n  bottom: 0,\n  right: 0,\n  height: 'auto',\n  maxHeight: '100%'\n}, ownerState.anchor === 'left' && ownerState.variant !== 'temporary' && {\n  borderRight: `1px solid ${(theme.vars || theme).palette.divider}`\n}, ownerState.anchor === 'top' && ownerState.variant !== 'temporary' && {\n  borderBottom: `1px solid ${(theme.vars || theme).palette.divider}`\n}, ownerState.anchor === 'right' && ownerState.variant !== 'temporary' && {\n  borderLeft: `1px solid ${(theme.vars || theme).palette.divider}`\n}, ownerState.anchor === 'bottom' && ownerState.variant !== 'temporary' && {\n  borderTop: `1px solid ${(theme.vars || theme).palette.divider}`\n}));\nconst oppositeDirection = {\n  left: 'right',\n  right: 'left',\n  top: 'down',\n  bottom: 'up'\n};\nexport function isHorizontal(anchor) {\n  return ['left', 'right'].indexOf(anchor) !== -1;\n}\nexport function getAnchor(theme, anchor) {\n  return theme.direction === 'rtl' && isHorizontal(anchor) ? oppositeDirection[anchor] : anchor;\n}\n\n/**\n * The props of the [Modal](/material-ui/api/modal/) component are available\n * when `variant=\"temporary\"` is set.\n */\nconst Drawer = /*#__PURE__*/React.forwardRef(function Drawer(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiDrawer'\n  });\n  const theme = useTheme();\n  const defaultTransitionDuration = {\n    enter: theme.transitions.duration.enteringScreen,\n    exit: theme.transitions.duration.leavingScreen\n  };\n  const {\n      anchor: anchorProp = 'left',\n      BackdropProps,\n      children,\n      className,\n      elevation = 16,\n      hideBackdrop = false,\n      ModalProps: {\n        BackdropProps: BackdropPropsProp\n      } = {},\n      onClose,\n      open = false,\n      PaperProps = {},\n      SlideProps,\n      // eslint-disable-next-line react/prop-types\n      TransitionComponent = Slide,\n      transitionDuration = defaultTransitionDuration,\n      variant = 'temporary'\n    } = props,\n    ModalProps = _objectWithoutPropertiesLoose(props.ModalProps, _excluded),\n    other = _objectWithoutPropertiesLoose(props, _excluded2);\n\n  // Let's assume that the Drawer will always be rendered on user space.\n  // We use this state is order to skip the appear transition during the\n  // initial mount of the component.\n  const mounted = React.useRef(false);\n  React.useEffect(() => {\n    mounted.current = true;\n  }, []);\n  const anchorInvariant = getAnchor(theme, anchorProp);\n  const anchor = anchorProp;\n  const ownerState = _extends({}, props, {\n    anchor,\n    elevation,\n    open,\n    variant\n  }, other);\n  const classes = useUtilityClasses(ownerState);\n  const drawer = /*#__PURE__*/_jsx(DrawerPaper, _extends({\n    elevation: variant === 'temporary' ? elevation : 0,\n    square: true\n  }, PaperProps, {\n    className: clsx(classes.paper, PaperProps.className),\n    ownerState: ownerState,\n    children: children\n  }));\n  if (variant === 'permanent') {\n    return /*#__PURE__*/_jsx(DrawerDockedRoot, _extends({\n      className: clsx(classes.root, classes.docked, className),\n      ownerState: ownerState,\n      ref: ref\n    }, other, {\n      children: drawer\n    }));\n  }\n  const slidingDrawer = /*#__PURE__*/_jsx(TransitionComponent, _extends({\n    in: open,\n    direction: oppositeDirection[anchorInvariant],\n    timeout: transitionDuration,\n    appear: mounted.current\n  }, SlideProps, {\n    children: drawer\n  }));\n  if (variant === 'persistent') {\n    return /*#__PURE__*/_jsx(DrawerDockedRoot, _extends({\n      className: clsx(classes.root, classes.docked, className),\n      ownerState: ownerState,\n      ref: ref\n    }, other, {\n      children: slidingDrawer\n    }));\n  }\n\n  // variant === temporary\n  return /*#__PURE__*/_jsx(DrawerRoot, _extends({\n    BackdropProps: _extends({}, BackdropProps, BackdropPropsProp, {\n      transitionDuration\n    }),\n    className: clsx(classes.root, classes.modal, className),\n    open: open,\n    ownerState: ownerState,\n    onClose: onClose,\n    hideBackdrop: hideBackdrop,\n    ref: ref\n  }, other, ModalProps, {\n    children: slidingDrawer\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? Drawer.propTypes /* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit the d.ts file and run \"yarn proptypes\"     |\n  // ----------------------------------------------------------------------\n  /**\n   * Side from which the drawer will appear.\n   * @default 'left'\n   */\n  anchor: PropTypes.oneOf(['bottom', 'left', 'right', 'top']),\n  /**\n   * @ignore\n   */\n  BackdropProps: PropTypes.object,\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The elevation of the drawer.\n   * @default 16\n   */\n  elevation: integerPropType,\n  /**\n   * If `true`, the backdrop is not rendered.\n   * @default false\n   */\n  hideBackdrop: PropTypes.bool,\n  /**\n   * Props applied to the [`Modal`](/material-ui/api/modal/) element.\n   * @default {}\n   */\n  ModalProps: PropTypes.object,\n  /**\n   * Callback fired when the component requests to be closed.\n   * The `reason` parameter can optionally be used to control the response to `onClose`.\n   *\n   * @param {object} event The event source of the callback.\n   * @param {string} reason Can be: `\"escapeKeyDown\"`, `\"backdropClick\"`.\n   */\n  onClose: PropTypes.func,\n  /**\n   * If `true`, the component is shown.\n   * @default false\n   */\n  open: PropTypes.bool,\n  /**\n   * Props applied to the [`Paper`](/material-ui/api/paper/) element.\n   * @default {}\n   */\n  PaperProps: PropTypes.object,\n  /**\n   * Props applied to the [`Slide`](/material-ui/api/slide/) element.\n   */\n  SlideProps: PropTypes.object,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The duration for the transition, in milliseconds.\n   * You may specify a single timeout for all transitions, or individually with an object.\n   * @default {\n   *   enter: theme.transitions.duration.enteringScreen,\n   *   exit: theme.transitions.duration.leavingScreen,\n   * }\n   */\n  transitionDuration: PropTypes.oneOfType([PropTypes.number, PropTypes.shape({\n    appear: PropTypes.number,\n    enter: PropTypes.number,\n    exit: PropTypes.number\n  })]),\n  /**\n   * The variant to use.\n   * @default 'temporary'\n   */\n  variant: PropTypes.oneOf(['permanent', 'persistent', 'temporary'])\n} : void 0;\nexport default Drawer;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,eAAe,CAAC;EACjCC,UAAU,GAAG,CAAC,QAAQ,EAAE,eAAe,EAAE,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,cAAc,EAAE,YAAY,EAAE,SAAS,EAAE,MAAM,EAAE,YAAY,EAAE,YAAY,EAAE,qBAAqB,EAAE,oBAAoB,EAAE,SAAS,CAAC;AACrN,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,eAAe,QAAQ,YAAY;AAC5C,SAASC,uBAAuB,IAAIC,cAAc,QAAQ,0BAA0B;AACpF,OAAOC,KAAK,MAAM,UAAU;AAC5B,OAAOC,KAAK,MAAM,UAAU;AAC5B,OAAOC,KAAK,MAAM,UAAU;AAC5B,OAAOC,UAAU,MAAM,qBAAqB;AAC5C,OAAOC,QAAQ,MAAM,oBAAoB;AACzC,OAAOC,aAAa,MAAM,yBAAyB;AACnD,OAAOC,MAAM,IAAIC,qBAAqB,QAAQ,kBAAkB;AAChE,SAASC,qBAAqB,QAAQ,iBAAiB;AACvD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGA,CAACC,KAAK,EAAEC,MAAM,KAAK;EAC3C,MAAM;IACJC;EACF,CAAC,GAAGF,KAAK;EACT,OAAO,CAACC,MAAM,CAACE,IAAI,EAAE,CAACD,UAAU,CAACE,OAAO,KAAK,WAAW,IAAIF,UAAU,CAACE,OAAO,KAAK,YAAY,KAAKH,MAAM,CAACI,MAAM,EAAEJ,MAAM,CAACK,KAAK,CAAC;AAClI,CAAC;AACD,MAAMC,iBAAiB,GAAGL,UAAU,IAAI;EACtC,MAAM;IACJM,OAAO;IACPC,MAAM;IACNL;EACF,CAAC,GAAGF,UAAU;EACd,MAAMQ,KAAK,GAAG;IACZP,IAAI,EAAE,CAAC,MAAM,CAAC;IACdE,MAAM,EAAE,CAAC,CAACD,OAAO,KAAK,WAAW,IAAIA,OAAO,KAAK,YAAY,KAAK,QAAQ,CAAC;IAC3EE,KAAK,EAAE,CAAC,OAAO,CAAC;IAChBK,KAAK,EAAE,CAAC,OAAO,EAAG,cAAapB,UAAU,CAACkB,MAAM,CAAE,EAAC,EAAEL,OAAO,KAAK,WAAW,IAAK,oBAAmBb,UAAU,CAACkB,MAAM,CAAE,EAAC;EAC1H,CAAC;EACD,OAAOtB,cAAc,CAACuB,KAAK,EAAEd,qBAAqB,EAAEY,OAAO,CAAC;AAC9D,CAAC;AACD,MAAMI,UAAU,GAAGlB,MAAM,CAACN,KAAK,EAAE;EAC/ByB,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,MAAM;EACZf;AACF,CAAC,CAAC,CAAC,CAAC;EACFgB;AACF,CAAC,MAAM;EACLC,MAAM,EAAE,CAACD,KAAK,CAACE,IAAI,IAAIF,KAAK,EAAEC,MAAM,CAACE;AACvC,CAAC,CAAC,CAAC;AACH,MAAMC,gBAAgB,GAAGzB,MAAM,CAAC,KAAK,EAAE;EACrC0B,iBAAiB,EAAEzB,qBAAqB;EACxCkB,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,QAAQ;EACdO,oBAAoB,EAAE,KAAK;EAC3BtB;AACF,CAAC,CAAC,CAAC;EACDuB,IAAI,EAAE;AACR,CAAC,CAAC;AACF,MAAMC,WAAW,GAAG7B,MAAM,CAACJ,KAAK,EAAE;EAChCuB,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,OAAO;EACbf,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJC;IACF,CAAC,GAAGF,KAAK;IACT,OAAO,CAACC,MAAM,CAACU,KAAK,EAAEV,MAAM,CAAE,cAAaV,UAAU,CAACW,UAAU,CAACO,MAAM,CAAE,EAAC,CAAC,EAAEP,UAAU,CAACE,OAAO,KAAK,WAAW,IAAIH,MAAM,CAAE,oBAAmBV,UAAU,CAACW,UAAU,CAACO,MAAM,CAAE,EAAC,CAAC,CAAC;EACjL;AACF,CAAC,CAAC,CAAC,CAAC;EACFM,KAAK;EACLb;AACF,CAAC,KAAKvB,QAAQ,CAAC;EACb6C,SAAS,EAAE,MAAM;EACjBC,OAAO,EAAE,MAAM;EACfC,aAAa,EAAE,QAAQ;EACvBC,MAAM,EAAE,MAAM;EACdL,IAAI,EAAE,UAAU;EAChBN,MAAM,EAAE,CAACD,KAAK,CAACE,IAAI,IAAIF,KAAK,EAAEC,MAAM,CAACE,MAAM;EAC3C;EACAU,uBAAuB,EAAE,OAAO;EAChC;EACAC,QAAQ,EAAE,OAAO;EACjBC,GAAG,EAAE,CAAC;EACN;EACA;EACA;EACAC,OAAO,EAAE;AACX,CAAC,EAAE7B,UAAU,CAACO,MAAM,KAAK,MAAM,IAAI;EACjCuB,IAAI,EAAE;AACR,CAAC,EAAE9B,UAAU,CAACO,MAAM,KAAK,KAAK,IAAI;EAChCqB,GAAG,EAAE,CAAC;EACNE,IAAI,EAAE,CAAC;EACPC,KAAK,EAAE,CAAC;EACRN,MAAM,EAAE,MAAM;EACdO,SAAS,EAAE;AACb,CAAC,EAAEhC,UAAU,CAACO,MAAM,KAAK,OAAO,IAAI;EAClCwB,KAAK,EAAE;AACT,CAAC,EAAE/B,UAAU,CAACO,MAAM,KAAK,QAAQ,IAAI;EACnCqB,GAAG,EAAE,MAAM;EACXE,IAAI,EAAE,CAAC;EACPG,MAAM,EAAE,CAAC;EACTF,KAAK,EAAE,CAAC;EACRN,MAAM,EAAE,MAAM;EACdO,SAAS,EAAE;AACb,CAAC,EAAEhC,UAAU,CAACO,MAAM,KAAK,MAAM,IAAIP,UAAU,CAACE,OAAO,KAAK,WAAW,IAAI;EACvEgC,WAAW,EAAG,aAAY,CAACrB,KAAK,CAACE,IAAI,IAAIF,KAAK,EAAEsB,OAAO,CAACC,OAAQ;AAClE,CAAC,EAAEpC,UAAU,CAACO,MAAM,KAAK,KAAK,IAAIP,UAAU,CAACE,OAAO,KAAK,WAAW,IAAI;EACtEmC,YAAY,EAAG,aAAY,CAACxB,KAAK,CAACE,IAAI,IAAIF,KAAK,EAAEsB,OAAO,CAACC,OAAQ;AACnE,CAAC,EAAEpC,UAAU,CAACO,MAAM,KAAK,OAAO,IAAIP,UAAU,CAACE,OAAO,KAAK,WAAW,IAAI;EACxEoC,UAAU,EAAG,aAAY,CAACzB,KAAK,CAACE,IAAI,IAAIF,KAAK,EAAEsB,OAAO,CAACC,OAAQ;AACjE,CAAC,EAAEpC,UAAU,CAACO,MAAM,KAAK,QAAQ,IAAIP,UAAU,CAACE,OAAO,KAAK,WAAW,IAAI;EACzEqC,SAAS,EAAG,aAAY,CAAC1B,KAAK,CAACE,IAAI,IAAIF,KAAK,EAAEsB,OAAO,CAACC,OAAQ;AAChE,CAAC,CAAC,CAAC;AACH,MAAMI,iBAAiB,GAAG;EACxBV,IAAI,EAAE,OAAO;EACbC,KAAK,EAAE,MAAM;EACbH,GAAG,EAAE,MAAM;EACXK,MAAM,EAAE;AACV,CAAC;AACD,OAAO,SAASQ,YAAYA,CAAClC,MAAM,EAAE;EACnC,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,CAACmC,OAAO,CAACnC,MAAM,CAAC,KAAK,CAAC,CAAC;AACjD;AACA,OAAO,SAASoC,SAASA,CAAC9B,KAAK,EAAEN,MAAM,EAAE;EACvC,OAAOM,KAAK,CAAC+B,SAAS,KAAK,KAAK,IAAIH,YAAY,CAAClC,MAAM,CAAC,GAAGiC,iBAAiB,CAACjC,MAAM,CAAC,GAAGA,MAAM;AAC/F;;AAEA;AACA;AACA;AACA;AACA,MAAMsC,MAAM,GAAG,aAAajE,KAAK,CAACkE,UAAU,CAAC,SAASD,MAAMA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACzE,MAAMlD,KAAK,GAAGP,aAAa,CAAC;IAC1BO,KAAK,EAAEiD,OAAO;IACdpC,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAME,KAAK,GAAGvB,QAAQ,CAAC,CAAC;EACxB,MAAM2D,yBAAyB,GAAG;IAChCC,KAAK,EAAErC,KAAK,CAACsC,WAAW,CAACC,QAAQ,CAACC,cAAc;IAChDC,IAAI,EAAEzC,KAAK,CAACsC,WAAW,CAACC,QAAQ,CAACG;EACnC,CAAC;EACD,MAAM;MACFhD,MAAM,EAAEiD,UAAU,GAAG,MAAM;MAC3BC,aAAa;MACbC,QAAQ;MACRC,SAAS;MACTC,SAAS,GAAG,EAAE;MACdC,YAAY,GAAG,KAAK;MACpBC,UAAU,EAAE;QACVL,aAAa,EAAEM;MACjB,CAAC,GAAG,CAAC,CAAC;MACNC,OAAO;MACPC,IAAI,GAAG,KAAK;MACZC,UAAU,GAAG,CAAC,CAAC;MACfC,UAAU;MACV;MACAC,mBAAmB,GAAGjF,KAAK;MAC3BkF,kBAAkB,GAAGpB,yBAAyB;MAC9C/C,OAAO,GAAG;IACZ,CAAC,GAAGJ,KAAK;IACTgE,UAAU,GAAGtF,6BAA6B,CAACsB,KAAK,CAACgE,UAAU,EAAEpF,SAAS,CAAC;IACvE4F,KAAK,GAAG9F,6BAA6B,CAACsB,KAAK,EAAEnB,UAAU,CAAC;;EAE1D;EACA;EACA;EACA,MAAM4F,OAAO,GAAG3F,KAAK,CAAC4F,MAAM,CAAC,KAAK,CAAC;EACnC5F,KAAK,CAAC6F,SAAS,CAAC,MAAM;IACpBF,OAAO,CAACG,OAAO,GAAG,IAAI;EACxB,CAAC,EAAE,EAAE,CAAC;EACN,MAAMC,eAAe,GAAGhC,SAAS,CAAC9B,KAAK,EAAE2C,UAAU,CAAC;EACpD,MAAMjD,MAAM,GAAGiD,UAAU;EACzB,MAAMxD,UAAU,GAAGvB,QAAQ,CAAC,CAAC,CAAC,EAAEqB,KAAK,EAAE;IACrCS,MAAM;IACNqD,SAAS;IACTK,IAAI;IACJ/D;EACF,CAAC,EAAEoE,KAAK,CAAC;EACT,MAAMhE,OAAO,GAAGD,iBAAiB,CAACL,UAAU,CAAC;EAC7C,MAAMgB,MAAM,GAAG,aAAapB,IAAI,CAACyB,WAAW,EAAE5C,QAAQ,CAAC;IACrDmF,SAAS,EAAE1D,OAAO,KAAK,WAAW,GAAG0D,SAAS,GAAG,CAAC;IAClDgB,MAAM,EAAE;EACV,CAAC,EAAEV,UAAU,EAAE;IACbP,SAAS,EAAE7E,IAAI,CAACwB,OAAO,CAACG,KAAK,EAAEyD,UAAU,CAACP,SAAS,CAAC;IACpD3D,UAAU,EAAEA,UAAU;IACtB0D,QAAQ,EAAEA;EACZ,CAAC,CAAC,CAAC;EACH,IAAIxD,OAAO,KAAK,WAAW,EAAE;IAC3B,OAAO,aAAaN,IAAI,CAACqB,gBAAgB,EAAExC,QAAQ,CAAC;MAClDkF,SAAS,EAAE7E,IAAI,CAACwB,OAAO,CAACL,IAAI,EAAEK,OAAO,CAACH,MAAM,EAAEwD,SAAS,CAAC;MACxD3D,UAAU,EAAEA,UAAU;MACtBgD,GAAG,EAAEA;IACP,CAAC,EAAEsB,KAAK,EAAE;MACRZ,QAAQ,EAAE1C;IACZ,CAAC,CAAC,CAAC;EACL;EACA,MAAM6D,aAAa,GAAG,aAAajF,IAAI,CAACwE,mBAAmB,EAAE3F,QAAQ,CAAC;IACpEqG,EAAE,EAAEb,IAAI;IACRrB,SAAS,EAAEJ,iBAAiB,CAACmC,eAAe,CAAC;IAC7CI,OAAO,EAAEV,kBAAkB;IAC3BW,MAAM,EAAET,OAAO,CAACG;EAClB,CAAC,EAAEP,UAAU,EAAE;IACbT,QAAQ,EAAE1C;EACZ,CAAC,CAAC,CAAC;EACH,IAAId,OAAO,KAAK,YAAY,EAAE;IAC5B,OAAO,aAAaN,IAAI,CAACqB,gBAAgB,EAAExC,QAAQ,CAAC;MAClDkF,SAAS,EAAE7E,IAAI,CAACwB,OAAO,CAACL,IAAI,EAAEK,OAAO,CAACH,MAAM,EAAEwD,SAAS,CAAC;MACxD3D,UAAU,EAAEA,UAAU;MACtBgD,GAAG,EAAEA;IACP,CAAC,EAAEsB,KAAK,EAAE;MACRZ,QAAQ,EAAEmB;IACZ,CAAC,CAAC,CAAC;EACL;;EAEA;EACA,OAAO,aAAajF,IAAI,CAACc,UAAU,EAAEjC,QAAQ,CAAC;IAC5CgF,aAAa,EAAEhF,QAAQ,CAAC,CAAC,CAAC,EAAEgF,aAAa,EAAEM,iBAAiB,EAAE;MAC5DM;IACF,CAAC,CAAC;IACFV,SAAS,EAAE7E,IAAI,CAACwB,OAAO,CAACL,IAAI,EAAEK,OAAO,CAACF,KAAK,EAAEuD,SAAS,CAAC;IACvDM,IAAI,EAAEA,IAAI;IACVjE,UAAU,EAAEA,UAAU;IACtBgE,OAAO,EAAEA,OAAO;IAChBH,YAAY,EAAEA,YAAY;IAC1Bb,GAAG,EAAEA;EACP,CAAC,EAAEsB,KAAK,EAAER,UAAU,EAAE;IACpBJ,QAAQ,EAAEmB;EACZ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFI,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGtC,MAAM,CAACuC,SAAS,CAAC,yBAAyB;EAChF;EACA;EACA;EACA;EACA;AACF;AACA;AACA;EACE7E,MAAM,EAAE1B,SAAS,CAACwG,KAAK,CAAC,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;EAC3D;AACF;AACA;EACE5B,aAAa,EAAE5E,SAAS,CAACyG,MAAM;EAC/B;AACF;AACA;EACE5B,QAAQ,EAAE7E,SAAS,CAAC0G,IAAI;EACxB;AACF;AACA;EACEjF,OAAO,EAAEzB,SAAS,CAACyG,MAAM;EACzB;AACF;AACA;EACE3B,SAAS,EAAE9E,SAAS,CAAC2G,MAAM;EAC3B;AACF;AACA;AACA;EACE5B,SAAS,EAAE7E,eAAe;EAC1B;AACF;AACA;AACA;EACE8E,YAAY,EAAEhF,SAAS,CAAC4G,IAAI;EAC5B;AACF;AACA;AACA;EACE3B,UAAU,EAAEjF,SAAS,CAACyG,MAAM;EAC5B;AACF;AACA;AACA;AACA;AACA;AACA;EACEtB,OAAO,EAAEnF,SAAS,CAAC6G,IAAI;EACvB;AACF;AACA;AACA;EACEzB,IAAI,EAAEpF,SAAS,CAAC4G,IAAI;EACpB;AACF;AACA;AACA;EACEvB,UAAU,EAAErF,SAAS,CAACyG,MAAM;EAC5B;AACF;AACA;EACEnB,UAAU,EAAEtF,SAAS,CAACyG,MAAM;EAC5B;AACF;AACA;EACEK,EAAE,EAAE9G,SAAS,CAAC+G,SAAS,CAAC,CAAC/G,SAAS,CAACgH,OAAO,CAAChH,SAAS,CAAC+G,SAAS,CAAC,CAAC/G,SAAS,CAAC6G,IAAI,EAAE7G,SAAS,CAACyG,MAAM,EAAEzG,SAAS,CAAC4G,IAAI,CAAC,CAAC,CAAC,EAAE5G,SAAS,CAAC6G,IAAI,EAAE7G,SAAS,CAACyG,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEjB,kBAAkB,EAAExF,SAAS,CAAC+G,SAAS,CAAC,CAAC/G,SAAS,CAACiH,MAAM,EAAEjH,SAAS,CAACkH,KAAK,CAAC;IACzEf,MAAM,EAAEnG,SAAS,CAACiH,MAAM;IACxB5C,KAAK,EAAErE,SAAS,CAACiH,MAAM;IACvBxC,IAAI,EAAEzE,SAAS,CAACiH;EAClB,CAAC,CAAC,CAAC,CAAC;EACJ;AACF;AACA;AACA;EACE5F,OAAO,EAAErB,SAAS,CAACwG,KAAK,CAAC,CAAC,WAAW,EAAE,YAAY,EAAE,WAAW,CAAC;AACnE,CAAC,GAAG,KAAK,CAAC;AACV,eAAexC,MAAM"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}