{"ast": null, "code": "import { unstable_generateUtilityClasses as generateUtilityClasses } from '@mui/utils';\nimport generateUtilityClass from '../generateUtilityClass';\nexport function getLinkUtilityClass(slot) {\n  return generateUtilityClass('MuiLink', slot);\n}\nconst linkClasses = generateUtilityClasses('MuiLink', ['root', 'underlineNone', 'underlineHover', 'underlineAlways', 'button', 'focusVisible']);\nexport default linkClasses;", "map": {"version": 3, "names": ["unstable_generateUtilityClasses", "generateUtilityClasses", "generateUtilityClass", "getLinkUtilityClass", "slot", "linkClasses"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/material/Link/linkClasses.js"], "sourcesContent": ["import { unstable_generateUtilityClasses as generateUtilityClasses } from '@mui/utils';\nimport generateUtilityClass from '../generateUtilityClass';\nexport function getLinkUtilityClass(slot) {\n  return generateUtilityClass('MuiLink', slot);\n}\nconst linkClasses = generateUtilityClasses('MuiLink', ['root', 'underlineNone', 'underlineHover', 'underlineAlways', 'button', 'focusVisible']);\nexport default linkClasses;"], "mappings": "AAAA,SAASA,+BAA+B,IAAIC,sBAAsB,QAAQ,YAAY;AACtF,OAAOC,oBAAoB,MAAM,yBAAyB;AAC1D,OAAO,SAASC,mBAAmBA,CAACC,IAAI,EAAE;EACxC,OAAOF,oBAAoB,CAAC,SAAS,EAAEE,IAAI,CAAC;AAC9C;AACA,MAAMC,WAAW,GAAGJ,sBAAsB,CAAC,SAAS,EAAE,CAAC,MAAM,EAAE,eAAe,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE,cAAc,CAAC,CAAC;AAC/I,eAAeI,WAAW"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}