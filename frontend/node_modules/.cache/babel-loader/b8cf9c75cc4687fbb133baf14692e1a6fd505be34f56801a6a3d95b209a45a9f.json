{"ast": null, "code": "import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"name\", \"slot\", \"skipVariantsResolver\", \"skipSx\", \"overridesResolver\"];\n/* eslint-disable no-underscore-dangle */\nimport styledEngineStyled, { internal_processStyles as processStyles } from '@mui/styled-engine';\nimport { getDisplayName, unstable_capitalize as capitalize, isPlainObject, deepmerge } from '@mui/utils';\nimport createTheme from './createTheme';\nimport propsToClassKey from './propsToClassKey';\nimport styleFunctionSx from './styleFunctionSx';\nfunction isEmpty(obj) {\n  return Object.keys(obj).length === 0;\n}\n\n// https://github.com/emotion-js/emotion/blob/26ded6109fcd8ca9875cc2ce4564fee678a3f3c5/packages/styled/src/utils.js#L40\nfunction isStringTag(tag) {\n  return typeof tag === 'string' &&\n  // 96 is one less than the char code\n  // for \"a\" so this is checking that\n  // it's a lowercase character\n  tag.charCodeAt(0) > 96;\n}\nconst getStyleOverrides = (name, theme) => {\n  if (theme.components && theme.components[name] && theme.components[name].styleOverrides) {\n    return theme.components[name].styleOverrides;\n  }\n  return null;\n};\nconst transformVariants = variants => {\n  const variantsStyles = {};\n  if (variants) {\n    variants.forEach(definition => {\n      const key = propsToClassKey(definition.props);\n      variantsStyles[key] = definition.style;\n    });\n  }\n  return variantsStyles;\n};\nconst getVariantStyles = (name, theme) => {\n  let variants = [];\n  if (theme && theme.components && theme.components[name] && theme.components[name].variants) {\n    variants = theme.components[name].variants;\n  }\n  return transformVariants(variants);\n};\nconst variantsResolver = (props, styles, variants) => {\n  const {\n    ownerState = {}\n  } = props;\n  const variantsStyles = [];\n  if (variants) {\n    variants.forEach(variant => {\n      let isMatch = true;\n      Object.keys(variant.props).forEach(key => {\n        if (ownerState[key] !== variant.props[key] && props[key] !== variant.props[key]) {\n          isMatch = false;\n        }\n      });\n      if (isMatch) {\n        variantsStyles.push(styles[propsToClassKey(variant.props)]);\n      }\n    });\n  }\n  return variantsStyles;\n};\nconst themeVariantsResolver = (props, styles, theme, name) => {\n  var _theme$components;\n  const themeVariants = theme == null || (_theme$components = theme.components) == null || (_theme$components = _theme$components[name]) == null ? void 0 : _theme$components.variants;\n  return variantsResolver(props, styles, themeVariants);\n};\n\n// Update /system/styled/#api in case if this changes\nexport function shouldForwardProp(prop) {\n  return prop !== 'ownerState' && prop !== 'theme' && prop !== 'sx' && prop !== 'as';\n}\nexport const systemDefaultTheme = createTheme();\nconst lowercaseFirstLetter = string => {\n  if (!string) {\n    return string;\n  }\n  return string.charAt(0).toLowerCase() + string.slice(1);\n};\nfunction resolveTheme({\n  defaultTheme,\n  theme,\n  themeId\n}) {\n  return isEmpty(theme) ? defaultTheme : theme[themeId] || theme;\n}\nfunction defaultOverridesResolver(slot) {\n  if (!slot) {\n    return null;\n  }\n  return (props, styles) => styles[slot];\n}\nconst muiStyledFunctionResolver = ({\n  styledArg,\n  props,\n  defaultTheme,\n  themeId\n}) => {\n  const resolvedStyles = styledArg(_extends({}, props, {\n    theme: resolveTheme(_extends({}, props, {\n      defaultTheme,\n      themeId\n    }))\n  }));\n  let optionalVariants;\n  if (resolvedStyles && resolvedStyles.variants) {\n    optionalVariants = resolvedStyles.variants;\n    delete resolvedStyles.variants;\n  }\n  if (optionalVariants) {\n    const variantsStyles = variantsResolver(props, transformVariants(optionalVariants), optionalVariants);\n    return [resolvedStyles, ...variantsStyles];\n  }\n  return resolvedStyles;\n};\nexport default function createStyled(input = {}) {\n  const {\n    themeId,\n    defaultTheme = systemDefaultTheme,\n    rootShouldForwardProp = shouldForwardProp,\n    slotShouldForwardProp = shouldForwardProp\n  } = input;\n  const systemSx = props => {\n    return styleFunctionSx(_extends({}, props, {\n      theme: resolveTheme(_extends({}, props, {\n        defaultTheme,\n        themeId\n      }))\n    }));\n  };\n  systemSx.__mui_systemSx = true;\n  return (tag, inputOptions = {}) => {\n    // Filter out the `sx` style function from the previous styled component to prevent unnecessary styles generated by the composite components.\n    processStyles(tag, styles => styles.filter(style => !(style != null && style.__mui_systemSx)));\n    const {\n        name: componentName,\n        slot: componentSlot,\n        skipVariantsResolver: inputSkipVariantsResolver,\n        skipSx: inputSkipSx,\n        // TODO v6: remove `lowercaseFirstLetter()` in the next major release\n        // For more details: https://github.com/mui/material-ui/pull/37908\n        overridesResolver = defaultOverridesResolver(lowercaseFirstLetter(componentSlot))\n      } = inputOptions,\n      options = _objectWithoutPropertiesLoose(inputOptions, _excluded);\n\n    // if skipVariantsResolver option is defined, take the value, otherwise, true for root and false for other slots.\n    const skipVariantsResolver = inputSkipVariantsResolver !== undefined ? inputSkipVariantsResolver :\n    // TODO v6: remove `Root` in the next major release\n    // For more details: https://github.com/mui/material-ui/pull/37908\n    componentSlot && componentSlot !== 'Root' && componentSlot !== 'root' || false;\n    const skipSx = inputSkipSx || false;\n    let label;\n    if (process.env.NODE_ENV !== 'production') {\n      if (componentName) {\n        // TODO v6: remove `lowercaseFirstLetter()` in the next major release\n        // For more details: https://github.com/mui/material-ui/pull/37908\n        label = `${componentName}-${lowercaseFirstLetter(componentSlot || 'Root')}`;\n      }\n    }\n    let shouldForwardPropOption = shouldForwardProp;\n\n    // TODO v6: remove `Root` in the next major release\n    // For more details: https://github.com/mui/material-ui/pull/37908\n    if (componentSlot === 'Root' || componentSlot === 'root') {\n      shouldForwardPropOption = rootShouldForwardProp;\n    } else if (componentSlot) {\n      // any other slot specified\n      shouldForwardPropOption = slotShouldForwardProp;\n    } else if (isStringTag(tag)) {\n      // for string (html) tag, preserve the behavior in emotion & styled-components.\n      shouldForwardPropOption = undefined;\n    }\n    const defaultStyledResolver = styledEngineStyled(tag, _extends({\n      shouldForwardProp: shouldForwardPropOption,\n      label\n    }, options));\n    const muiStyledResolver = (styleArg, ...expressions) => {\n      const expressionsWithDefaultTheme = expressions ? expressions.map(stylesArg => {\n        // On the server Emotion doesn't use React.forwardRef for creating components, so the created\n        // component stays as a function. This condition makes sure that we do not interpolate functions\n        // which are basically components used as a selectors.\n        if (typeof stylesArg === 'function' && stylesArg.__emotion_real !== stylesArg) {\n          return props => muiStyledFunctionResolver({\n            styledArg: stylesArg,\n            props,\n            defaultTheme,\n            themeId\n          });\n        }\n        if (isPlainObject(stylesArg)) {\n          let transformedStylesArg = stylesArg;\n          let styledArgVariants;\n          if (stylesArg && stylesArg.variants) {\n            styledArgVariants = stylesArg.variants;\n            delete transformedStylesArg.variants;\n            transformedStylesArg = props => {\n              let result = stylesArg;\n              const variantStyles = variantsResolver(props, transformVariants(styledArgVariants), styledArgVariants);\n              variantStyles.forEach(variantStyle => {\n                result = deepmerge(result, variantStyle);\n              });\n              return result;\n            };\n          }\n          return transformedStylesArg;\n        }\n        return stylesArg;\n      }) : [];\n      let transformedStyleArg = styleArg;\n      if (isPlainObject(styleArg)) {\n        let styledArgVariants;\n        if (styleArg && styleArg.variants) {\n          styledArgVariants = styleArg.variants;\n          delete transformedStyleArg.variants;\n          transformedStyleArg = props => {\n            let result = styleArg;\n            const variantStyles = variantsResolver(props, transformVariants(styledArgVariants), styledArgVariants);\n            variantStyles.forEach(variantStyle => {\n              result = deepmerge(result, variantStyle);\n            });\n            return result;\n          };\n        }\n      } else if (typeof styleArg === 'function' &&\n      // On the server Emotion doesn't use React.forwardRef for creating components, so the created\n      // component stays as a function. This condition makes sure that we do not interpolate functions\n      // which are basically components used as a selectors.\n      styleArg.__emotion_real !== styleArg) {\n        // If the type is function, we need to define the default theme.\n        transformedStyleArg = props => muiStyledFunctionResolver({\n          styledArg: styleArg,\n          props,\n          defaultTheme,\n          themeId\n        });\n      }\n      if (componentName && overridesResolver) {\n        expressionsWithDefaultTheme.push(props => {\n          const theme = resolveTheme(_extends({}, props, {\n            defaultTheme,\n            themeId\n          }));\n          const styleOverrides = getStyleOverrides(componentName, theme);\n          if (styleOverrides) {\n            const resolvedStyleOverrides = {};\n            Object.entries(styleOverrides).forEach(([slotKey, slotStyle]) => {\n              resolvedStyleOverrides[slotKey] = typeof slotStyle === 'function' ? slotStyle(_extends({}, props, {\n                theme\n              })) : slotStyle;\n            });\n            return overridesResolver(props, resolvedStyleOverrides);\n          }\n          return null;\n        });\n      }\n      if (componentName && !skipVariantsResolver) {\n        expressionsWithDefaultTheme.push(props => {\n          const theme = resolveTheme(_extends({}, props, {\n            defaultTheme,\n            themeId\n          }));\n          return themeVariantsResolver(props, getVariantStyles(componentName, theme), theme, componentName);\n        });\n      }\n      if (!skipSx) {\n        expressionsWithDefaultTheme.push(systemSx);\n      }\n      const numOfCustomFnsApplied = expressionsWithDefaultTheme.length - expressions.length;\n      if (Array.isArray(styleArg) && numOfCustomFnsApplied > 0) {\n        const placeholders = new Array(numOfCustomFnsApplied).fill('');\n        // If the type is array, than we need to add placeholders in the template for the overrides, variants and the sx styles.\n        transformedStyleArg = [...styleArg, ...placeholders];\n        transformedStyleArg.raw = [...styleArg.raw, ...placeholders];\n      }\n      const Component = defaultStyledResolver(transformedStyleArg, ...expressionsWithDefaultTheme);\n      if (process.env.NODE_ENV !== 'production') {\n        let displayName;\n        if (componentName) {\n          displayName = `${componentName}${capitalize(componentSlot || '')}`;\n        }\n        if (displayName === undefined) {\n          displayName = `Styled(${getDisplayName(tag)})`;\n        }\n        Component.displayName = displayName;\n      }\n      if (tag.muiName) {\n        Component.muiName = tag.muiName;\n      }\n      return Component;\n    };\n    if (defaultStyledResolver.withConfig) {\n      muiStyledResolver.withConfig = defaultStyledResolver.withConfig;\n    }\n    return muiStyledResolver;\n  };\n}", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "styledEngineStyled", "internal_processStyles", "processStyles", "getDisplayName", "unstable_capitalize", "capitalize", "isPlainObject", "deepmerge", "createTheme", "props<PERSON>o<PERSON><PERSON><PERSON><PERSON>", "styleFunctionSx", "isEmpty", "obj", "Object", "keys", "length", "isStringTag", "tag", "charCodeAt", "getStyleOverrides", "name", "theme", "components", "styleOverrides", "transformVariants", "variants", "variantsStyles", "for<PERSON>ach", "definition", "key", "props", "style", "getVariantStyles", "variantsResolver", "styles", "ownerState", "variant", "isMatch", "push", "themeVariantsResolver", "_theme$components", "themeVariants", "shouldForwardProp", "prop", "systemDefaultTheme", "lowercaseFirstLetter", "string", "char<PERSON>t", "toLowerCase", "slice", "resolveTheme", "defaultTheme", "themeId", "defaultOverridesResolver", "slot", "muiStyledFunctionResolver", "styledArg", "resolvedStyles", "optionalVariants", "createStyled", "input", "rootShouldForwardProp", "slotShouldForwardProp", "systemSx", "__mui_systemSx", "inputOptions", "filter", "componentName", "componentSlot", "skipVariantsResolver", "inputSkipVariantsResolver", "skipSx", "inputSkipSx", "overridesResolver", "options", "undefined", "label", "process", "env", "NODE_ENV", "shouldForwardPropOption", "defaultStyledResolver", "muiStyledResolver", "styleArg", "expressions", "expressionsWithDefaultTheme", "map", "stylesArg", "__emotion_real", "transformedStylesArg", "styledArgVariants", "result", "variantStyles", "variantStyle", "transformedStyleArg", "resolvedStyleOverrides", "entries", "<PERSON><PERSON><PERSON>", "slotStyle", "numOfCustomFnsApplied", "Array", "isArray", "placeholders", "fill", "raw", "Component", "displayName", "mui<PERSON><PERSON>", "withConfig"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/system/esm/createStyled.js"], "sourcesContent": ["import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"name\", \"slot\", \"skipVariantsResolver\", \"skipSx\", \"overridesResolver\"];\n/* eslint-disable no-underscore-dangle */\nimport styledEngineStyled, { internal_processStyles as processStyles } from '@mui/styled-engine';\nimport { getDisplayName, unstable_capitalize as capitalize, isPlainObject, deepmerge } from '@mui/utils';\nimport createTheme from './createTheme';\nimport propsToClassKey from './propsToClassKey';\nimport styleFunctionSx from './styleFunctionSx';\nfunction isEmpty(obj) {\n  return Object.keys(obj).length === 0;\n}\n\n// https://github.com/emotion-js/emotion/blob/26ded6109fcd8ca9875cc2ce4564fee678a3f3c5/packages/styled/src/utils.js#L40\nfunction isStringTag(tag) {\n  return typeof tag === 'string' &&\n  // 96 is one less than the char code\n  // for \"a\" so this is checking that\n  // it's a lowercase character\n  tag.charCodeAt(0) > 96;\n}\nconst getStyleOverrides = (name, theme) => {\n  if (theme.components && theme.components[name] && theme.components[name].styleOverrides) {\n    return theme.components[name].styleOverrides;\n  }\n  return null;\n};\nconst transformVariants = variants => {\n  const variantsStyles = {};\n  if (variants) {\n    variants.forEach(definition => {\n      const key = propsToClassKey(definition.props);\n      variantsStyles[key] = definition.style;\n    });\n  }\n  return variantsStyles;\n};\nconst getVariantStyles = (name, theme) => {\n  let variants = [];\n  if (theme && theme.components && theme.components[name] && theme.components[name].variants) {\n    variants = theme.components[name].variants;\n  }\n  return transformVariants(variants);\n};\nconst variantsResolver = (props, styles, variants) => {\n  const {\n    ownerState = {}\n  } = props;\n  const variantsStyles = [];\n  if (variants) {\n    variants.forEach(variant => {\n      let isMatch = true;\n      Object.keys(variant.props).forEach(key => {\n        if (ownerState[key] !== variant.props[key] && props[key] !== variant.props[key]) {\n          isMatch = false;\n        }\n      });\n      if (isMatch) {\n        variantsStyles.push(styles[propsToClassKey(variant.props)]);\n      }\n    });\n  }\n  return variantsStyles;\n};\nconst themeVariantsResolver = (props, styles, theme, name) => {\n  var _theme$components;\n  const themeVariants = theme == null || (_theme$components = theme.components) == null || (_theme$components = _theme$components[name]) == null ? void 0 : _theme$components.variants;\n  return variantsResolver(props, styles, themeVariants);\n};\n\n// Update /system/styled/#api in case if this changes\nexport function shouldForwardProp(prop) {\n  return prop !== 'ownerState' && prop !== 'theme' && prop !== 'sx' && prop !== 'as';\n}\nexport const systemDefaultTheme = createTheme();\nconst lowercaseFirstLetter = string => {\n  if (!string) {\n    return string;\n  }\n  return string.charAt(0).toLowerCase() + string.slice(1);\n};\nfunction resolveTheme({\n  defaultTheme,\n  theme,\n  themeId\n}) {\n  return isEmpty(theme) ? defaultTheme : theme[themeId] || theme;\n}\nfunction defaultOverridesResolver(slot) {\n  if (!slot) {\n    return null;\n  }\n  return (props, styles) => styles[slot];\n}\nconst muiStyledFunctionResolver = ({\n  styledArg,\n  props,\n  defaultTheme,\n  themeId\n}) => {\n  const resolvedStyles = styledArg(_extends({}, props, {\n    theme: resolveTheme(_extends({}, props, {\n      defaultTheme,\n      themeId\n    }))\n  }));\n  let optionalVariants;\n  if (resolvedStyles && resolvedStyles.variants) {\n    optionalVariants = resolvedStyles.variants;\n    delete resolvedStyles.variants;\n  }\n  if (optionalVariants) {\n    const variantsStyles = variantsResolver(props, transformVariants(optionalVariants), optionalVariants);\n    return [resolvedStyles, ...variantsStyles];\n  }\n  return resolvedStyles;\n};\nexport default function createStyled(input = {}) {\n  const {\n    themeId,\n    defaultTheme = systemDefaultTheme,\n    rootShouldForwardProp = shouldForwardProp,\n    slotShouldForwardProp = shouldForwardProp\n  } = input;\n  const systemSx = props => {\n    return styleFunctionSx(_extends({}, props, {\n      theme: resolveTheme(_extends({}, props, {\n        defaultTheme,\n        themeId\n      }))\n    }));\n  };\n  systemSx.__mui_systemSx = true;\n  return (tag, inputOptions = {}) => {\n    // Filter out the `sx` style function from the previous styled component to prevent unnecessary styles generated by the composite components.\n    processStyles(tag, styles => styles.filter(style => !(style != null && style.__mui_systemSx)));\n    const {\n        name: componentName,\n        slot: componentSlot,\n        skipVariantsResolver: inputSkipVariantsResolver,\n        skipSx: inputSkipSx,\n        // TODO v6: remove `lowercaseFirstLetter()` in the next major release\n        // For more details: https://github.com/mui/material-ui/pull/37908\n        overridesResolver = defaultOverridesResolver(lowercaseFirstLetter(componentSlot))\n      } = inputOptions,\n      options = _objectWithoutPropertiesLoose(inputOptions, _excluded);\n\n    // if skipVariantsResolver option is defined, take the value, otherwise, true for root and false for other slots.\n    const skipVariantsResolver = inputSkipVariantsResolver !== undefined ? inputSkipVariantsResolver :\n    // TODO v6: remove `Root` in the next major release\n    // For more details: https://github.com/mui/material-ui/pull/37908\n    componentSlot && componentSlot !== 'Root' && componentSlot !== 'root' || false;\n    const skipSx = inputSkipSx || false;\n    let label;\n    if (process.env.NODE_ENV !== 'production') {\n      if (componentName) {\n        // TODO v6: remove `lowercaseFirstLetter()` in the next major release\n        // For more details: https://github.com/mui/material-ui/pull/37908\n        label = `${componentName}-${lowercaseFirstLetter(componentSlot || 'Root')}`;\n      }\n    }\n    let shouldForwardPropOption = shouldForwardProp;\n\n    // TODO v6: remove `Root` in the next major release\n    // For more details: https://github.com/mui/material-ui/pull/37908\n    if (componentSlot === 'Root' || componentSlot === 'root') {\n      shouldForwardPropOption = rootShouldForwardProp;\n    } else if (componentSlot) {\n      // any other slot specified\n      shouldForwardPropOption = slotShouldForwardProp;\n    } else if (isStringTag(tag)) {\n      // for string (html) tag, preserve the behavior in emotion & styled-components.\n      shouldForwardPropOption = undefined;\n    }\n    const defaultStyledResolver = styledEngineStyled(tag, _extends({\n      shouldForwardProp: shouldForwardPropOption,\n      label\n    }, options));\n    const muiStyledResolver = (styleArg, ...expressions) => {\n      const expressionsWithDefaultTheme = expressions ? expressions.map(stylesArg => {\n        // On the server Emotion doesn't use React.forwardRef for creating components, so the created\n        // component stays as a function. This condition makes sure that we do not interpolate functions\n        // which are basically components used as a selectors.\n        if (typeof stylesArg === 'function' && stylesArg.__emotion_real !== stylesArg) {\n          return props => muiStyledFunctionResolver({\n            styledArg: stylesArg,\n            props,\n            defaultTheme,\n            themeId\n          });\n        }\n        if (isPlainObject(stylesArg)) {\n          let transformedStylesArg = stylesArg;\n          let styledArgVariants;\n          if (stylesArg && stylesArg.variants) {\n            styledArgVariants = stylesArg.variants;\n            delete transformedStylesArg.variants;\n            transformedStylesArg = props => {\n              let result = stylesArg;\n              const variantStyles = variantsResolver(props, transformVariants(styledArgVariants), styledArgVariants);\n              variantStyles.forEach(variantStyle => {\n                result = deepmerge(result, variantStyle);\n              });\n              return result;\n            };\n          }\n          return transformedStylesArg;\n        }\n        return stylesArg;\n      }) : [];\n      let transformedStyleArg = styleArg;\n      if (isPlainObject(styleArg)) {\n        let styledArgVariants;\n        if (styleArg && styleArg.variants) {\n          styledArgVariants = styleArg.variants;\n          delete transformedStyleArg.variants;\n          transformedStyleArg = props => {\n            let result = styleArg;\n            const variantStyles = variantsResolver(props, transformVariants(styledArgVariants), styledArgVariants);\n            variantStyles.forEach(variantStyle => {\n              result = deepmerge(result, variantStyle);\n            });\n            return result;\n          };\n        }\n      } else if (typeof styleArg === 'function' &&\n      // On the server Emotion doesn't use React.forwardRef for creating components, so the created\n      // component stays as a function. This condition makes sure that we do not interpolate functions\n      // which are basically components used as a selectors.\n      styleArg.__emotion_real !== styleArg) {\n        // If the type is function, we need to define the default theme.\n        transformedStyleArg = props => muiStyledFunctionResolver({\n          styledArg: styleArg,\n          props,\n          defaultTheme,\n          themeId\n        });\n      }\n      if (componentName && overridesResolver) {\n        expressionsWithDefaultTheme.push(props => {\n          const theme = resolveTheme(_extends({}, props, {\n            defaultTheme,\n            themeId\n          }));\n          const styleOverrides = getStyleOverrides(componentName, theme);\n          if (styleOverrides) {\n            const resolvedStyleOverrides = {};\n            Object.entries(styleOverrides).forEach(([slotKey, slotStyle]) => {\n              resolvedStyleOverrides[slotKey] = typeof slotStyle === 'function' ? slotStyle(_extends({}, props, {\n                theme\n              })) : slotStyle;\n            });\n            return overridesResolver(props, resolvedStyleOverrides);\n          }\n          return null;\n        });\n      }\n      if (componentName && !skipVariantsResolver) {\n        expressionsWithDefaultTheme.push(props => {\n          const theme = resolveTheme(_extends({}, props, {\n            defaultTheme,\n            themeId\n          }));\n          return themeVariantsResolver(props, getVariantStyles(componentName, theme), theme, componentName);\n        });\n      }\n      if (!skipSx) {\n        expressionsWithDefaultTheme.push(systemSx);\n      }\n      const numOfCustomFnsApplied = expressionsWithDefaultTheme.length - expressions.length;\n      if (Array.isArray(styleArg) && numOfCustomFnsApplied > 0) {\n        const placeholders = new Array(numOfCustomFnsApplied).fill('');\n        // If the type is array, than we need to add placeholders in the template for the overrides, variants and the sx styles.\n        transformedStyleArg = [...styleArg, ...placeholders];\n        transformedStyleArg.raw = [...styleArg.raw, ...placeholders];\n      }\n      const Component = defaultStyledResolver(transformedStyleArg, ...expressionsWithDefaultTheme);\n      if (process.env.NODE_ENV !== 'production') {\n        let displayName;\n        if (componentName) {\n          displayName = `${componentName}${capitalize(componentSlot || '')}`;\n        }\n        if (displayName === undefined) {\n          displayName = `Styled(${getDisplayName(tag)})`;\n        }\n        Component.displayName = displayName;\n      }\n      if (tag.muiName) {\n        Component.muiName = tag.muiName;\n      }\n      return Component;\n    };\n    if (defaultStyledResolver.withConfig) {\n      muiStyledResolver.withConfig = defaultStyledResolver.withConfig;\n    }\n    return muiStyledResolver;\n  };\n}"], "mappings": "AAAA,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,sBAAsB,EAAE,QAAQ,EAAE,mBAAmB,CAAC;AACzF;AACA,OAAOC,kBAAkB,IAAIC,sBAAsB,IAAIC,aAAa,QAAQ,oBAAoB;AAChG,SAASC,cAAc,EAAEC,mBAAmB,IAAIC,UAAU,EAAEC,aAAa,EAAEC,SAAS,QAAQ,YAAY;AACxG,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,SAASC,OAAOA,CAACC,GAAG,EAAE;EACpB,OAAOC,MAAM,CAACC,IAAI,CAACF,GAAG,CAAC,CAACG,MAAM,KAAK,CAAC;AACtC;;AAEA;AACA,SAASC,WAAWA,CAACC,GAAG,EAAE;EACxB,OAAO,OAAOA,GAAG,KAAK,QAAQ;EAC9B;EACA;EACA;EACAA,GAAG,CAACC,UAAU,CAAC,CAAC,CAAC,GAAG,EAAE;AACxB;AACA,MAAMC,iBAAiB,GAAGA,CAACC,IAAI,EAAEC,KAAK,KAAK;EACzC,IAAIA,KAAK,CAACC,UAAU,IAAID,KAAK,CAACC,UAAU,CAACF,IAAI,CAAC,IAAIC,KAAK,CAACC,UAAU,CAACF,IAAI,CAAC,CAACG,cAAc,EAAE;IACvF,OAAOF,KAAK,CAACC,UAAU,CAACF,IAAI,CAAC,CAACG,cAAc;EAC9C;EACA,OAAO,IAAI;AACb,CAAC;AACD,MAAMC,iBAAiB,GAAGC,QAAQ,IAAI;EACpC,MAAMC,cAAc,GAAG,CAAC,CAAC;EACzB,IAAID,QAAQ,EAAE;IACZA,QAAQ,CAACE,OAAO,CAACC,UAAU,IAAI;MAC7B,MAAMC,GAAG,GAAGpB,eAAe,CAACmB,UAAU,CAACE,KAAK,CAAC;MAC7CJ,cAAc,CAACG,GAAG,CAAC,GAAGD,UAAU,CAACG,KAAK;IACxC,CAAC,CAAC;EACJ;EACA,OAAOL,cAAc;AACvB,CAAC;AACD,MAAMM,gBAAgB,GAAGA,CAACZ,IAAI,EAAEC,KAAK,KAAK;EACxC,IAAII,QAAQ,GAAG,EAAE;EACjB,IAAIJ,KAAK,IAAIA,KAAK,CAACC,UAAU,IAAID,KAAK,CAACC,UAAU,CAACF,IAAI,CAAC,IAAIC,KAAK,CAACC,UAAU,CAACF,IAAI,CAAC,CAACK,QAAQ,EAAE;IAC1FA,QAAQ,GAAGJ,KAAK,CAACC,UAAU,CAACF,IAAI,CAAC,CAACK,QAAQ;EAC5C;EACA,OAAOD,iBAAiB,CAACC,QAAQ,CAAC;AACpC,CAAC;AACD,MAAMQ,gBAAgB,GAAGA,CAACH,KAAK,EAAEI,MAAM,EAAET,QAAQ,KAAK;EACpD,MAAM;IACJU,UAAU,GAAG,CAAC;EAChB,CAAC,GAAGL,KAAK;EACT,MAAMJ,cAAc,GAAG,EAAE;EACzB,IAAID,QAAQ,EAAE;IACZA,QAAQ,CAACE,OAAO,CAACS,OAAO,IAAI;MAC1B,IAAIC,OAAO,GAAG,IAAI;MAClBxB,MAAM,CAACC,IAAI,CAACsB,OAAO,CAACN,KAAK,CAAC,CAACH,OAAO,CAACE,GAAG,IAAI;QACxC,IAAIM,UAAU,CAACN,GAAG,CAAC,KAAKO,OAAO,CAACN,KAAK,CAACD,GAAG,CAAC,IAAIC,KAAK,CAACD,GAAG,CAAC,KAAKO,OAAO,CAACN,KAAK,CAACD,GAAG,CAAC,EAAE;UAC/EQ,OAAO,GAAG,KAAK;QACjB;MACF,CAAC,CAAC;MACF,IAAIA,OAAO,EAAE;QACXX,cAAc,CAACY,IAAI,CAACJ,MAAM,CAACzB,eAAe,CAAC2B,OAAO,CAACN,KAAK,CAAC,CAAC,CAAC;MAC7D;IACF,CAAC,CAAC;EACJ;EACA,OAAOJ,cAAc;AACvB,CAAC;AACD,MAAMa,qBAAqB,GAAGA,CAACT,KAAK,EAAEI,MAAM,EAAEb,KAAK,EAAED,IAAI,KAAK;EAC5D,IAAIoB,iBAAiB;EACrB,MAAMC,aAAa,GAAGpB,KAAK,IAAI,IAAI,IAAI,CAACmB,iBAAiB,GAAGnB,KAAK,CAACC,UAAU,KAAK,IAAI,IAAI,CAACkB,iBAAiB,GAAGA,iBAAiB,CAACpB,IAAI,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGoB,iBAAiB,CAACf,QAAQ;EACpL,OAAOQ,gBAAgB,CAACH,KAAK,EAAEI,MAAM,EAAEO,aAAa,CAAC;AACvD,CAAC;;AAED;AACA,OAAO,SAASC,iBAAiBA,CAACC,IAAI,EAAE;EACtC,OAAOA,IAAI,KAAK,YAAY,IAAIA,IAAI,KAAK,OAAO,IAAIA,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,IAAI;AACpF;AACA,OAAO,MAAMC,kBAAkB,GAAGpC,WAAW,CAAC,CAAC;AAC/C,MAAMqC,oBAAoB,GAAGC,MAAM,IAAI;EACrC,IAAI,CAACA,MAAM,EAAE;IACX,OAAOA,MAAM;EACf;EACA,OAAOA,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGF,MAAM,CAACG,KAAK,CAAC,CAAC,CAAC;AACzD,CAAC;AACD,SAASC,YAAYA,CAAC;EACpBC,YAAY;EACZ9B,KAAK;EACL+B;AACF,CAAC,EAAE;EACD,OAAOzC,OAAO,CAACU,KAAK,CAAC,GAAG8B,YAAY,GAAG9B,KAAK,CAAC+B,OAAO,CAAC,IAAI/B,KAAK;AAChE;AACA,SAASgC,wBAAwBA,CAACC,IAAI,EAAE;EACtC,IAAI,CAACA,IAAI,EAAE;IACT,OAAO,IAAI;EACb;EACA,OAAO,CAACxB,KAAK,EAAEI,MAAM,KAAKA,MAAM,CAACoB,IAAI,CAAC;AACxC;AACA,MAAMC,yBAAyB,GAAGA,CAAC;EACjCC,SAAS;EACT1B,KAAK;EACLqB,YAAY;EACZC;AACF,CAAC,KAAK;EACJ,MAAMK,cAAc,GAAGD,SAAS,CAAC1D,QAAQ,CAAC,CAAC,CAAC,EAAEgC,KAAK,EAAE;IACnDT,KAAK,EAAE6B,YAAY,CAACpD,QAAQ,CAAC,CAAC,CAAC,EAAEgC,KAAK,EAAE;MACtCqB,YAAY;MACZC;IACF,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC;EACH,IAAIM,gBAAgB;EACpB,IAAID,cAAc,IAAIA,cAAc,CAAChC,QAAQ,EAAE;IAC7CiC,gBAAgB,GAAGD,cAAc,CAAChC,QAAQ;IAC1C,OAAOgC,cAAc,CAAChC,QAAQ;EAChC;EACA,IAAIiC,gBAAgB,EAAE;IACpB,MAAMhC,cAAc,GAAGO,gBAAgB,CAACH,KAAK,EAAEN,iBAAiB,CAACkC,gBAAgB,CAAC,EAAEA,gBAAgB,CAAC;IACrG,OAAO,CAACD,cAAc,EAAE,GAAG/B,cAAc,CAAC;EAC5C;EACA,OAAO+B,cAAc;AACvB,CAAC;AACD,eAAe,SAASE,YAAYA,CAACC,KAAK,GAAG,CAAC,CAAC,EAAE;EAC/C,MAAM;IACJR,OAAO;IACPD,YAAY,GAAGP,kBAAkB;IACjCiB,qBAAqB,GAAGnB,iBAAiB;IACzCoB,qBAAqB,GAAGpB;EAC1B,CAAC,GAAGkB,KAAK;EACT,MAAMG,QAAQ,GAAGjC,KAAK,IAAI;IACxB,OAAOpB,eAAe,CAACZ,QAAQ,CAAC,CAAC,CAAC,EAAEgC,KAAK,EAAE;MACzCT,KAAK,EAAE6B,YAAY,CAACpD,QAAQ,CAAC,CAAC,CAAC,EAAEgC,KAAK,EAAE;QACtCqB,YAAY;QACZC;MACF,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;EACL,CAAC;EACDW,QAAQ,CAACC,cAAc,GAAG,IAAI;EAC9B,OAAO,CAAC/C,GAAG,EAAEgD,YAAY,GAAG,CAAC,CAAC,KAAK;IACjC;IACA/D,aAAa,CAACe,GAAG,EAAEiB,MAAM,IAAIA,MAAM,CAACgC,MAAM,CAACnC,KAAK,IAAI,EAAEA,KAAK,IAAI,IAAI,IAAIA,KAAK,CAACiC,cAAc,CAAC,CAAC,CAAC;IAC9F,MAAM;QACF5C,IAAI,EAAE+C,aAAa;QACnBb,IAAI,EAAEc,aAAa;QACnBC,oBAAoB,EAAEC,yBAAyB;QAC/CC,MAAM,EAAEC,WAAW;QACnB;QACA;QACAC,iBAAiB,GAAGpB,wBAAwB,CAACR,oBAAoB,CAACuB,aAAa,CAAC;MAClF,CAAC,GAAGH,YAAY;MAChBS,OAAO,GAAG7E,6BAA6B,CAACoE,YAAY,EAAElE,SAAS,CAAC;;IAElE;IACA,MAAMsE,oBAAoB,GAAGC,yBAAyB,KAAKK,SAAS,GAAGL,yBAAyB;IAChG;IACA;IACAF,aAAa,IAAIA,aAAa,KAAK,MAAM,IAAIA,aAAa,KAAK,MAAM,IAAI,KAAK;IAC9E,MAAMG,MAAM,GAAGC,WAAW,IAAI,KAAK;IACnC,IAAII,KAAK;IACT,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAIZ,aAAa,EAAE;QACjB;QACA;QACAS,KAAK,GAAI,GAAET,aAAc,IAAGtB,oBAAoB,CAACuB,aAAa,IAAI,MAAM,CAAE,EAAC;MAC7E;IACF;IACA,IAAIY,uBAAuB,GAAGtC,iBAAiB;;IAE/C;IACA;IACA,IAAI0B,aAAa,KAAK,MAAM,IAAIA,aAAa,KAAK,MAAM,EAAE;MACxDY,uBAAuB,GAAGnB,qBAAqB;IACjD,CAAC,MAAM,IAAIO,aAAa,EAAE;MACxB;MACAY,uBAAuB,GAAGlB,qBAAqB;IACjD,CAAC,MAAM,IAAI9C,WAAW,CAACC,GAAG,CAAC,EAAE;MAC3B;MACA+D,uBAAuB,GAAGL,SAAS;IACrC;IACA,MAAMM,qBAAqB,GAAGjF,kBAAkB,CAACiB,GAAG,EAAEnB,QAAQ,CAAC;MAC7D4C,iBAAiB,EAAEsC,uBAAuB;MAC1CJ;IACF,CAAC,EAAEF,OAAO,CAAC,CAAC;IACZ,MAAMQ,iBAAiB,GAAGA,CAACC,QAAQ,EAAE,GAAGC,WAAW,KAAK;MACtD,MAAMC,2BAA2B,GAAGD,WAAW,GAAGA,WAAW,CAACE,GAAG,CAACC,SAAS,IAAI;QAC7E;QACA;QACA;QACA,IAAI,OAAOA,SAAS,KAAK,UAAU,IAAIA,SAAS,CAACC,cAAc,KAAKD,SAAS,EAAE;UAC7E,OAAOzD,KAAK,IAAIyB,yBAAyB,CAAC;YACxCC,SAAS,EAAE+B,SAAS;YACpBzD,KAAK;YACLqB,YAAY;YACZC;UACF,CAAC,CAAC;QACJ;QACA,IAAI9C,aAAa,CAACiF,SAAS,CAAC,EAAE;UAC5B,IAAIE,oBAAoB,GAAGF,SAAS;UACpC,IAAIG,iBAAiB;UACrB,IAAIH,SAAS,IAAIA,SAAS,CAAC9D,QAAQ,EAAE;YACnCiE,iBAAiB,GAAGH,SAAS,CAAC9D,QAAQ;YACtC,OAAOgE,oBAAoB,CAAChE,QAAQ;YACpCgE,oBAAoB,GAAG3D,KAAK,IAAI;cAC9B,IAAI6D,MAAM,GAAGJ,SAAS;cACtB,MAAMK,aAAa,GAAG3D,gBAAgB,CAACH,KAAK,EAAEN,iBAAiB,CAACkE,iBAAiB,CAAC,EAAEA,iBAAiB,CAAC;cACtGE,aAAa,CAACjE,OAAO,CAACkE,YAAY,IAAI;gBACpCF,MAAM,GAAGpF,SAAS,CAACoF,MAAM,EAAEE,YAAY,CAAC;cAC1C,CAAC,CAAC;cACF,OAAOF,MAAM;YACf,CAAC;UACH;UACA,OAAOF,oBAAoB;QAC7B;QACA,OAAOF,SAAS;MAClB,CAAC,CAAC,GAAG,EAAE;MACP,IAAIO,mBAAmB,GAAGX,QAAQ;MAClC,IAAI7E,aAAa,CAAC6E,QAAQ,CAAC,EAAE;QAC3B,IAAIO,iBAAiB;QACrB,IAAIP,QAAQ,IAAIA,QAAQ,CAAC1D,QAAQ,EAAE;UACjCiE,iBAAiB,GAAGP,QAAQ,CAAC1D,QAAQ;UACrC,OAAOqE,mBAAmB,CAACrE,QAAQ;UACnCqE,mBAAmB,GAAGhE,KAAK,IAAI;YAC7B,IAAI6D,MAAM,GAAGR,QAAQ;YACrB,MAAMS,aAAa,GAAG3D,gBAAgB,CAACH,KAAK,EAAEN,iBAAiB,CAACkE,iBAAiB,CAAC,EAAEA,iBAAiB,CAAC;YACtGE,aAAa,CAACjE,OAAO,CAACkE,YAAY,IAAI;cACpCF,MAAM,GAAGpF,SAAS,CAACoF,MAAM,EAAEE,YAAY,CAAC;YAC1C,CAAC,CAAC;YACF,OAAOF,MAAM;UACf,CAAC;QACH;MACF,CAAC,MAAM,IAAI,OAAOR,QAAQ,KAAK,UAAU;MACzC;MACA;MACA;MACAA,QAAQ,CAACK,cAAc,KAAKL,QAAQ,EAAE;QACpC;QACAW,mBAAmB,GAAGhE,KAAK,IAAIyB,yBAAyB,CAAC;UACvDC,SAAS,EAAE2B,QAAQ;UACnBrD,KAAK;UACLqB,YAAY;UACZC;QACF,CAAC,CAAC;MACJ;MACA,IAAIe,aAAa,IAAIM,iBAAiB,EAAE;QACtCY,2BAA2B,CAAC/C,IAAI,CAACR,KAAK,IAAI;UACxC,MAAMT,KAAK,GAAG6B,YAAY,CAACpD,QAAQ,CAAC,CAAC,CAAC,EAAEgC,KAAK,EAAE;YAC7CqB,YAAY;YACZC;UACF,CAAC,CAAC,CAAC;UACH,MAAM7B,cAAc,GAAGJ,iBAAiB,CAACgD,aAAa,EAAE9C,KAAK,CAAC;UAC9D,IAAIE,cAAc,EAAE;YAClB,MAAMwE,sBAAsB,GAAG,CAAC,CAAC;YACjClF,MAAM,CAACmF,OAAO,CAACzE,cAAc,CAAC,CAACI,OAAO,CAAC,CAAC,CAACsE,OAAO,EAAEC,SAAS,CAAC,KAAK;cAC/DH,sBAAsB,CAACE,OAAO,CAAC,GAAG,OAAOC,SAAS,KAAK,UAAU,GAAGA,SAAS,CAACpG,QAAQ,CAAC,CAAC,CAAC,EAAEgC,KAAK,EAAE;gBAChGT;cACF,CAAC,CAAC,CAAC,GAAG6E,SAAS;YACjB,CAAC,CAAC;YACF,OAAOzB,iBAAiB,CAAC3C,KAAK,EAAEiE,sBAAsB,CAAC;UACzD;UACA,OAAO,IAAI;QACb,CAAC,CAAC;MACJ;MACA,IAAI5B,aAAa,IAAI,CAACE,oBAAoB,EAAE;QAC1CgB,2BAA2B,CAAC/C,IAAI,CAACR,KAAK,IAAI;UACxC,MAAMT,KAAK,GAAG6B,YAAY,CAACpD,QAAQ,CAAC,CAAC,CAAC,EAAEgC,KAAK,EAAE;YAC7CqB,YAAY;YACZC;UACF,CAAC,CAAC,CAAC;UACH,OAAOb,qBAAqB,CAACT,KAAK,EAAEE,gBAAgB,CAACmC,aAAa,EAAE9C,KAAK,CAAC,EAAEA,KAAK,EAAE8C,aAAa,CAAC;QACnG,CAAC,CAAC;MACJ;MACA,IAAI,CAACI,MAAM,EAAE;QACXc,2BAA2B,CAAC/C,IAAI,CAACyB,QAAQ,CAAC;MAC5C;MACA,MAAMoC,qBAAqB,GAAGd,2BAA2B,CAACtE,MAAM,GAAGqE,WAAW,CAACrE,MAAM;MACrF,IAAIqF,KAAK,CAACC,OAAO,CAAClB,QAAQ,CAAC,IAAIgB,qBAAqB,GAAG,CAAC,EAAE;QACxD,MAAMG,YAAY,GAAG,IAAIF,KAAK,CAACD,qBAAqB,CAAC,CAACI,IAAI,CAAC,EAAE,CAAC;QAC9D;QACAT,mBAAmB,GAAG,CAAC,GAAGX,QAAQ,EAAE,GAAGmB,YAAY,CAAC;QACpDR,mBAAmB,CAACU,GAAG,GAAG,CAAC,GAAGrB,QAAQ,CAACqB,GAAG,EAAE,GAAGF,YAAY,CAAC;MAC9D;MACA,MAAMG,SAAS,GAAGxB,qBAAqB,CAACa,mBAAmB,EAAE,GAAGT,2BAA2B,CAAC;MAC5F,IAAIR,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC,IAAI2B,WAAW;QACf,IAAIvC,aAAa,EAAE;UACjBuC,WAAW,GAAI,GAAEvC,aAAc,GAAE9D,UAAU,CAAC+D,aAAa,IAAI,EAAE,CAAE,EAAC;QACpE;QACA,IAAIsC,WAAW,KAAK/B,SAAS,EAAE;UAC7B+B,WAAW,GAAI,UAASvG,cAAc,CAACc,GAAG,CAAE,GAAE;QAChD;QACAwF,SAAS,CAACC,WAAW,GAAGA,WAAW;MACrC;MACA,IAAIzF,GAAG,CAAC0F,OAAO,EAAE;QACfF,SAAS,CAACE,OAAO,GAAG1F,GAAG,CAAC0F,OAAO;MACjC;MACA,OAAOF,SAAS;IAClB,CAAC;IACD,IAAIxB,qBAAqB,CAAC2B,UAAU,EAAE;MACpC1B,iBAAiB,CAAC0B,UAAU,GAAG3B,qBAAqB,CAAC2B,UAAU;IACjE;IACA,OAAO1B,iBAAiB;EAC1B,CAAC;AACH"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}