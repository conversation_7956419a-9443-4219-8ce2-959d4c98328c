{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"className\", \"children\", \"classes\", \"IconComponent\", \"input\", \"inputProps\", \"variant\"],\n  _excluded2 = [\"root\"];\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport PropTypes from 'prop-types';\nimport { unstable_composeClasses as composeClasses } from '@mui/base/composeClasses';\nimport NativeSelectInput from './NativeSelectInput';\nimport formControlState from '../FormControl/formControlState';\nimport useFormControl from '../FormControl/useFormControl';\nimport ArrowDropDownIcon from '../internal/svg-icons/ArrowDropDown';\nimport Input from '../Input';\nimport useThemeProps from '../styles/useThemeProps';\nimport { getNativeSelectUtilityClasses } from './nativeSelectClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getNativeSelectUtilityClasses, classes);\n};\nconst defaultInput = /*#__PURE__*/_jsx(Input, {});\n/**\n * An alternative to `<Select native />` with a much smaller bundle size footprint.\n */\nconst NativeSelect = /*#__PURE__*/React.forwardRef(function NativeSelect(inProps, ref) {\n  const props = useThemeProps({\n    name: 'MuiNativeSelect',\n    props: inProps\n  });\n  const {\n      className,\n      children,\n      classes: classesProp = {},\n      IconComponent = ArrowDropDownIcon,\n      input = defaultInput,\n      inputProps\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const muiFormControl = useFormControl();\n  const fcs = formControlState({\n    props,\n    muiFormControl,\n    states: ['variant']\n  });\n  const ownerState = _extends({}, props, {\n    classes: classesProp\n  });\n  const classes = useUtilityClasses(ownerState);\n  const otherClasses = _objectWithoutPropertiesLoose(classesProp, _excluded2);\n  return /*#__PURE__*/_jsx(React.Fragment, {\n    children: /*#__PURE__*/React.cloneElement(input, _extends({\n      // Most of the logic is implemented in `NativeSelectInput`.\n      // The `Select` component is a simple API wrapper to expose something better to play with.\n      inputComponent: NativeSelectInput,\n      inputProps: _extends({\n        children,\n        classes: otherClasses,\n        IconComponent,\n        variant: fcs.variant,\n        type: undefined\n      }, inputProps, input ? input.props.inputProps : {}),\n      ref\n    }, other, {\n      className: clsx(classes.root, input.props.className, className)\n    }))\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? NativeSelect.propTypes /* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit the d.ts file and run \"yarn proptypes\"     |\n  // ----------------------------------------------------------------------\n  /**\n   * The option elements to populate the select with.\n   * Can be some `<option>` elements.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   * @default {}\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The icon that displays the arrow.\n   * @default ArrowDropDownIcon\n   */\n  IconComponent: PropTypes.elementType,\n  /**\n   * An `Input` element; does not have to be a material-ui specific `Input`.\n   * @default <Input />\n   */\n  input: PropTypes.element,\n  /**\n   * [Attributes](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/select#attributes) applied to the `select` element.\n   */\n  inputProps: PropTypes.object,\n  /**\n   * Callback fired when a menu item is selected.\n   *\n   * @param {React.ChangeEvent<HTMLSelectElement>} event The event source of the callback.\n   * You can pull out the new value by accessing `event.target.value` (string).\n   */\n  onChange: PropTypes.func,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The `input` value. The DOM API casts this to a string.\n   */\n  value: PropTypes.any,\n  /**\n   * The variant to use.\n   */\n  variant: PropTypes.oneOf(['filled', 'outlined', 'standard'])\n} : void 0;\nNativeSelect.muiName = 'Select';\nexport default NativeSelect;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "_excluded2", "React", "clsx", "PropTypes", "unstable_composeClasses", "composeClasses", "NativeSelectInput", "formControlState", "useFormControl", "ArrowDropDownIcon", "Input", "useThemeProps", "getNativeSelectUtilityClasses", "jsx", "_jsx", "useUtilityClasses", "ownerState", "classes", "slots", "root", "defaultInput", "NativeSelect", "forwardRef", "inProps", "ref", "props", "name", "className", "children", "classesProp", "IconComponent", "input", "inputProps", "other", "muiFormControl", "fcs", "states", "otherClasses", "Fragment", "cloneElement", "inputComponent", "variant", "type", "undefined", "process", "env", "NODE_ENV", "propTypes", "node", "object", "string", "elementType", "element", "onChange", "func", "sx", "oneOfType", "arrayOf", "bool", "value", "any", "oneOf", "mui<PERSON><PERSON>"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/material/NativeSelect/NativeSelect.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"className\", \"children\", \"classes\", \"IconComponent\", \"input\", \"inputProps\", \"variant\"],\n  _excluded2 = [\"root\"];\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport PropTypes from 'prop-types';\nimport { unstable_composeClasses as composeClasses } from '@mui/base/composeClasses';\nimport NativeSelectInput from './NativeSelectInput';\nimport formControlState from '../FormControl/formControlState';\nimport useFormControl from '../FormControl/useFormControl';\nimport ArrowDropDownIcon from '../internal/svg-icons/ArrowDropDown';\nimport Input from '../Input';\nimport useThemeProps from '../styles/useThemeProps';\nimport { getNativeSelectUtilityClasses } from './nativeSelectClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getNativeSelectUtilityClasses, classes);\n};\nconst defaultInput = /*#__PURE__*/_jsx(Input, {});\n/**\n * An alternative to `<Select native />` with a much smaller bundle size footprint.\n */\nconst NativeSelect = /*#__PURE__*/React.forwardRef(function NativeSelect(inProps, ref) {\n  const props = useThemeProps({\n    name: 'MuiNativeSelect',\n    props: inProps\n  });\n  const {\n      className,\n      children,\n      classes: classesProp = {},\n      IconComponent = ArrowDropDownIcon,\n      input = defaultInput,\n      inputProps\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const muiFormControl = useFormControl();\n  const fcs = formControlState({\n    props,\n    muiFormControl,\n    states: ['variant']\n  });\n  const ownerState = _extends({}, props, {\n    classes: classesProp\n  });\n  const classes = useUtilityClasses(ownerState);\n  const otherClasses = _objectWithoutPropertiesLoose(classesProp, _excluded2);\n  return /*#__PURE__*/_jsx(React.Fragment, {\n    children: /*#__PURE__*/React.cloneElement(input, _extends({\n      // Most of the logic is implemented in `NativeSelectInput`.\n      // The `Select` component is a simple API wrapper to expose something better to play with.\n      inputComponent: NativeSelectInput,\n      inputProps: _extends({\n        children,\n        classes: otherClasses,\n        IconComponent,\n        variant: fcs.variant,\n        type: undefined\n      }, inputProps, input ? input.props.inputProps : {}),\n      ref\n    }, other, {\n      className: clsx(classes.root, input.props.className, className)\n    }))\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? NativeSelect.propTypes /* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit the d.ts file and run \"yarn proptypes\"     |\n  // ----------------------------------------------------------------------\n  /**\n   * The option elements to populate the select with.\n   * Can be some `<option>` elements.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   * @default {}\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The icon that displays the arrow.\n   * @default ArrowDropDownIcon\n   */\n  IconComponent: PropTypes.elementType,\n  /**\n   * An `Input` element; does not have to be a material-ui specific `Input`.\n   * @default <Input />\n   */\n  input: PropTypes.element,\n  /**\n   * [Attributes](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/select#attributes) applied to the `select` element.\n   */\n  inputProps: PropTypes.object,\n  /**\n   * Callback fired when a menu item is selected.\n   *\n   * @param {React.ChangeEvent<HTMLSelectElement>} event The event source of the callback.\n   * You can pull out the new value by accessing `event.target.value` (string).\n   */\n  onChange: PropTypes.func,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The `input` value. The DOM API casts this to a string.\n   */\n  value: PropTypes.any,\n  /**\n   * The variant to use.\n   */\n  variant: PropTypes.oneOf(['filled', 'outlined', 'standard'])\n} : void 0;\nNativeSelect.muiName = 'Select';\nexport default NativeSelect;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,WAAW,EAAE,UAAU,EAAE,SAAS,EAAE,eAAe,EAAE,OAAO,EAAE,YAAY,EAAE,SAAS,CAAC;EACvGC,UAAU,GAAG,CAAC,MAAM,CAAC;AACvB,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,uBAAuB,IAAIC,cAAc,QAAQ,0BAA0B;AACpF,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,OAAOC,iBAAiB,MAAM,qCAAqC;AACnE,OAAOC,KAAK,MAAM,UAAU;AAC5B,OAAOC,aAAa,MAAM,yBAAyB;AACnD,SAASC,6BAA6B,QAAQ,uBAAuB;AACrE,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC;EACF,CAAC,GAAGD,UAAU;EACd,MAAME,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM;EACf,CAAC;EACD,OAAOd,cAAc,CAACa,KAAK,EAAEN,6BAA6B,EAAEK,OAAO,CAAC;AACtE,CAAC;AACD,MAAMG,YAAY,GAAG,aAAaN,IAAI,CAACJ,KAAK,EAAE,CAAC,CAAC,CAAC;AACjD;AACA;AACA;AACA,MAAMW,YAAY,GAAG,aAAapB,KAAK,CAACqB,UAAU,CAAC,SAASD,YAAYA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACrF,MAAMC,KAAK,GAAGd,aAAa,CAAC;IAC1Be,IAAI,EAAE,iBAAiB;IACvBD,KAAK,EAAEF;EACT,CAAC,CAAC;EACF,MAAM;MACFI,SAAS;MACTC,QAAQ;MACRX,OAAO,EAAEY,WAAW,GAAG,CAAC,CAAC;MACzBC,aAAa,GAAGrB,iBAAiB;MACjCsB,KAAK,GAAGX,YAAY;MACpBY;IACF,CAAC,GAAGP,KAAK;IACTQ,KAAK,GAAGnC,6BAA6B,CAAC2B,KAAK,EAAE1B,SAAS,CAAC;EACzD,MAAMmC,cAAc,GAAG1B,cAAc,CAAC,CAAC;EACvC,MAAM2B,GAAG,GAAG5B,gBAAgB,CAAC;IAC3BkB,KAAK;IACLS,cAAc;IACdE,MAAM,EAAE,CAAC,SAAS;EACpB,CAAC,CAAC;EACF,MAAMpB,UAAU,GAAGnB,QAAQ,CAAC,CAAC,CAAC,EAAE4B,KAAK,EAAE;IACrCR,OAAO,EAAEY;EACX,CAAC,CAAC;EACF,MAAMZ,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAMqB,YAAY,GAAGvC,6BAA6B,CAAC+B,WAAW,EAAE7B,UAAU,CAAC;EAC3E,OAAO,aAAac,IAAI,CAACb,KAAK,CAACqC,QAAQ,EAAE;IACvCV,QAAQ,EAAE,aAAa3B,KAAK,CAACsC,YAAY,CAACR,KAAK,EAAElC,QAAQ,CAAC;MACxD;MACA;MACA2C,cAAc,EAAElC,iBAAiB;MACjC0B,UAAU,EAAEnC,QAAQ,CAAC;QACnB+B,QAAQ;QACRX,OAAO,EAAEoB,YAAY;QACrBP,aAAa;QACbW,OAAO,EAAEN,GAAG,CAACM,OAAO;QACpBC,IAAI,EAAEC;MACR,CAAC,EAAEX,UAAU,EAAED,KAAK,GAAGA,KAAK,CAACN,KAAK,CAACO,UAAU,GAAG,CAAC,CAAC,CAAC;MACnDR;IACF,CAAC,EAAES,KAAK,EAAE;MACRN,SAAS,EAAEzB,IAAI,CAACe,OAAO,CAACE,IAAI,EAAEY,KAAK,CAACN,KAAK,CAACE,SAAS,EAAEA,SAAS;IAChE,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC;AACFiB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGzB,YAAY,CAAC0B,SAAS,CAAC,yBAAyB;EACtF;EACA;EACA;EACA;EACA;AACF;AACA;AACA;EACEnB,QAAQ,EAAEzB,SAAS,CAAC6C,IAAI;EACxB;AACF;AACA;AACA;EACE/B,OAAO,EAAEd,SAAS,CAAC8C,MAAM;EACzB;AACF;AACA;EACEtB,SAAS,EAAExB,SAAS,CAAC+C,MAAM;EAC3B;AACF;AACA;AACA;EACEpB,aAAa,EAAE3B,SAAS,CAACgD,WAAW;EACpC;AACF;AACA;AACA;EACEpB,KAAK,EAAE5B,SAAS,CAACiD,OAAO;EACxB;AACF;AACA;EACEpB,UAAU,EAAE7B,SAAS,CAAC8C,MAAM;EAC5B;AACF;AACA;AACA;AACA;AACA;EACEI,QAAQ,EAAElD,SAAS,CAACmD,IAAI;EACxB;AACF;AACA;EACEC,EAAE,EAAEpD,SAAS,CAACqD,SAAS,CAAC,CAACrD,SAAS,CAACsD,OAAO,CAACtD,SAAS,CAACqD,SAAS,CAAC,CAACrD,SAAS,CAACmD,IAAI,EAAEnD,SAAS,CAAC8C,MAAM,EAAE9C,SAAS,CAACuD,IAAI,CAAC,CAAC,CAAC,EAAEvD,SAAS,CAACmD,IAAI,EAAEnD,SAAS,CAAC8C,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;EACEU,KAAK,EAAExD,SAAS,CAACyD,GAAG;EACpB;AACF;AACA;EACEnB,OAAO,EAAEtC,SAAS,CAAC0D,KAAK,CAAC,CAAC,QAAQ,EAAE,UAAU,EAAE,UAAU,CAAC;AAC7D,CAAC,GAAG,KAAK,CAAC;AACVxC,YAAY,CAACyC,OAAO,GAAG,QAAQ;AAC/B,eAAezC,YAAY"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}