{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { exactProp } from '@mui/utils';\nimport withWidth, { isWidthDown, isWidthUp } from './withWidth';\nimport useTheme from '../styles/useTheme';\n\n/**\n * @ignore - internal component.\n */\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction HiddenJs(props) {\n  const {\n    children,\n    only,\n    width\n  } = props;\n  const theme = useTheme();\n  let visible = true;\n\n  // `only` check is faster to get out sooner if used.\n  if (only) {\n    if (Array.isArray(only)) {\n      for (let i = 0; i < only.length; i += 1) {\n        const breakpoint = only[i];\n        if (width === breakpoint) {\n          visible = false;\n          break;\n        }\n      }\n    } else if (only && width === only) {\n      visible = false;\n    }\n  }\n\n  // Allow `only` to be combined with other props. If already hidden, no need to check others.\n  if (visible) {\n    // determine visibility based on the smallest size up\n    for (let i = 0; i < theme.breakpoints.keys.length; i += 1) {\n      const breakpoint = theme.breakpoints.keys[i];\n      const breakpointUp = props[`${breakpoint}Up`];\n      const breakpointDown = props[`${breakpoint}Down`];\n      if (breakpointUp && isWidthUp(breakpoint, width) || breakpointDown && isWidthDown(breakpoint, width)) {\n        visible = false;\n        break;\n      }\n    }\n  }\n  if (!visible) {\n    return null;\n  }\n  return /*#__PURE__*/_jsx(React.Fragment, {\n    children: children\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? HiddenJs.propTypes = {\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * If `true`, screens this size and down are hidden.\n   */\n  // eslint-disable-next-line react/no-unused-prop-types\n  lgDown: PropTypes.bool,\n  /**\n   * If `true`, screens this size and up are hidden.\n   */\n  // eslint-disable-next-line react/no-unused-prop-types\n  lgUp: PropTypes.bool,\n  /**\n   * If `true`, screens this size and down are hidden.\n   */\n  // eslint-disable-next-line react/no-unused-prop-types\n  mdDown: PropTypes.bool,\n  /**\n   * If `true`, screens this size and up are hidden.\n   */\n  // eslint-disable-next-line react/no-unused-prop-types\n  mdUp: PropTypes.bool,\n  /**\n   * Hide the given breakpoint(s).\n   */\n  only: PropTypes.oneOfType([PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl']), PropTypes.arrayOf(PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl']))]),\n  /**\n   * If `true`, screens this size and down are hidden.\n   */\n  // eslint-disable-next-line react/no-unused-prop-types\n  smDown: PropTypes.bool,\n  /**\n   * If `true`, screens this size and up are hidden.\n   */\n  // eslint-disable-next-line react/no-unused-prop-types\n  smUp: PropTypes.bool,\n  /**\n   * @ignore\n   * width prop provided by withWidth decorator.\n   */\n  width: PropTypes.string.isRequired,\n  /**\n   * If `true`, screens this size and down are hidden.\n   */\n  // eslint-disable-next-line react/no-unused-prop-types\n  xlDown: PropTypes.bool,\n  /**\n   * If `true`, screens this size and up are hidden.\n   */\n  // eslint-disable-next-line react/no-unused-prop-types\n  xlUp: PropTypes.bool,\n  /**\n   * If `true`, screens this size and down are hidden.\n   */\n  // eslint-disable-next-line react/no-unused-prop-types\n  xsDown: PropTypes.bool,\n  /**\n   * If `true`, screens this size and up are hidden.\n   */\n  // eslint-disable-next-line react/no-unused-prop-types\n  xsUp: PropTypes.bool\n} : void 0;\nif (process.env.NODE_ENV !== 'production') {\n  process.env.NODE_ENV !== \"production\" ? HiddenJs.propTypes = exactProp(HiddenJs.propTypes) : void 0;\n}\nexport default withWidth()(HiddenJs);", "map": {"version": 3, "names": ["React", "PropTypes", "exactProp", "with<PERSON><PERSON><PERSON>", "isWidthDown", "isWidthUp", "useTheme", "jsx", "_jsx", "HiddenJs", "props", "children", "only", "width", "theme", "visible", "Array", "isArray", "i", "length", "breakpoint", "breakpoints", "keys", "breakpointUp", "breakpointDown", "Fragment", "process", "env", "NODE_ENV", "propTypes", "node", "lgDown", "bool", "lgUp", "mdDown", "mdUp", "oneOfType", "oneOf", "arrayOf", "smDown", "smUp", "string", "isRequired", "xlDown", "xlUp", "xsDown", "xsUp"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/material/Hidden/HiddenJs.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { exactProp } from '@mui/utils';\nimport withWidth, { isWidthDown, isWidthUp } from './withWidth';\nimport useTheme from '../styles/useTheme';\n\n/**\n * @ignore - internal component.\n */\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction HiddenJs(props) {\n  const {\n    children,\n    only,\n    width\n  } = props;\n  const theme = useTheme();\n  let visible = true;\n\n  // `only` check is faster to get out sooner if used.\n  if (only) {\n    if (Array.isArray(only)) {\n      for (let i = 0; i < only.length; i += 1) {\n        const breakpoint = only[i];\n        if (width === breakpoint) {\n          visible = false;\n          break;\n        }\n      }\n    } else if (only && width === only) {\n      visible = false;\n    }\n  }\n\n  // Allow `only` to be combined with other props. If already hidden, no need to check others.\n  if (visible) {\n    // determine visibility based on the smallest size up\n    for (let i = 0; i < theme.breakpoints.keys.length; i += 1) {\n      const breakpoint = theme.breakpoints.keys[i];\n      const breakpointUp = props[`${breakpoint}Up`];\n      const breakpointDown = props[`${breakpoint}Down`];\n      if (breakpointUp && isWidthUp(breakpoint, width) || breakpointDown && isWidthDown(breakpoint, width)) {\n        visible = false;\n        break;\n      }\n    }\n  }\n  if (!visible) {\n    return null;\n  }\n  return /*#__PURE__*/_jsx(React.Fragment, {\n    children: children\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? HiddenJs.propTypes = {\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * If `true`, screens this size and down are hidden.\n   */\n  // eslint-disable-next-line react/no-unused-prop-types\n  lgDown: PropTypes.bool,\n  /**\n   * If `true`, screens this size and up are hidden.\n   */\n  // eslint-disable-next-line react/no-unused-prop-types\n  lgUp: PropTypes.bool,\n  /**\n   * If `true`, screens this size and down are hidden.\n   */\n  // eslint-disable-next-line react/no-unused-prop-types\n  mdDown: PropTypes.bool,\n  /**\n   * If `true`, screens this size and up are hidden.\n   */\n  // eslint-disable-next-line react/no-unused-prop-types\n  mdUp: PropTypes.bool,\n  /**\n   * Hide the given breakpoint(s).\n   */\n  only: PropTypes.oneOfType([PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl']), PropTypes.arrayOf(PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl']))]),\n  /**\n   * If `true`, screens this size and down are hidden.\n   */\n  // eslint-disable-next-line react/no-unused-prop-types\n  smDown: PropTypes.bool,\n  /**\n   * If `true`, screens this size and up are hidden.\n   */\n  // eslint-disable-next-line react/no-unused-prop-types\n  smUp: PropTypes.bool,\n  /**\n   * @ignore\n   * width prop provided by withWidth decorator.\n   */\n  width: PropTypes.string.isRequired,\n  /**\n   * If `true`, screens this size and down are hidden.\n   */\n  // eslint-disable-next-line react/no-unused-prop-types\n  xlDown: PropTypes.bool,\n  /**\n   * If `true`, screens this size and up are hidden.\n   */\n  // eslint-disable-next-line react/no-unused-prop-types\n  xlUp: PropTypes.bool,\n  /**\n   * If `true`, screens this size and down are hidden.\n   */\n  // eslint-disable-next-line react/no-unused-prop-types\n  xsDown: PropTypes.bool,\n  /**\n   * If `true`, screens this size and up are hidden.\n   */\n  // eslint-disable-next-line react/no-unused-prop-types\n  xsUp: PropTypes.bool\n} : void 0;\nif (process.env.NODE_ENV !== 'production') {\n  process.env.NODE_ENV !== \"production\" ? HiddenJs.propTypes = exactProp(HiddenJs.propTypes) : void 0;\n}\nexport default withWidth()(HiddenJs);"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,SAAS,QAAQ,YAAY;AACtC,OAAOC,SAAS,IAAIC,WAAW,EAAEC,SAAS,QAAQ,aAAa;AAC/D,OAAOC,QAAQ,MAAM,oBAAoB;;AAEzC;AACA;AACA;AACA,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,QAAQA,CAACC,KAAK,EAAE;EACvB,MAAM;IACJC,QAAQ;IACRC,IAAI;IACJC;EACF,CAAC,GAAGH,KAAK;EACT,MAAMI,KAAK,GAAGR,QAAQ,CAAC,CAAC;EACxB,IAAIS,OAAO,GAAG,IAAI;;EAElB;EACA,IAAIH,IAAI,EAAE;IACR,IAAII,KAAK,CAACC,OAAO,CAACL,IAAI,CAAC,EAAE;MACvB,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,IAAI,CAACO,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;QACvC,MAAME,UAAU,GAAGR,IAAI,CAACM,CAAC,CAAC;QAC1B,IAAIL,KAAK,KAAKO,UAAU,EAAE;UACxBL,OAAO,GAAG,KAAK;UACf;QACF;MACF;IACF,CAAC,MAAM,IAAIH,IAAI,IAAIC,KAAK,KAAKD,IAAI,EAAE;MACjCG,OAAO,GAAG,KAAK;IACjB;EACF;;EAEA;EACA,IAAIA,OAAO,EAAE;IACX;IACA,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,KAAK,CAACO,WAAW,CAACC,IAAI,CAACH,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;MACzD,MAAME,UAAU,GAAGN,KAAK,CAACO,WAAW,CAACC,IAAI,CAACJ,CAAC,CAAC;MAC5C,MAAMK,YAAY,GAAGb,KAAK,CAAE,GAAEU,UAAW,IAAG,CAAC;MAC7C,MAAMI,cAAc,GAAGd,KAAK,CAAE,GAAEU,UAAW,MAAK,CAAC;MACjD,IAAIG,YAAY,IAAIlB,SAAS,CAACe,UAAU,EAAEP,KAAK,CAAC,IAAIW,cAAc,IAAIpB,WAAW,CAACgB,UAAU,EAAEP,KAAK,CAAC,EAAE;QACpGE,OAAO,GAAG,KAAK;QACf;MACF;IACF;EACF;EACA,IAAI,CAACA,OAAO,EAAE;IACZ,OAAO,IAAI;EACb;EACA,OAAO,aAAaP,IAAI,CAACR,KAAK,CAACyB,QAAQ,EAAE;IACvCd,QAAQ,EAAEA;EACZ,CAAC,CAAC;AACJ;AACAe,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGnB,QAAQ,CAACoB,SAAS,GAAG;EAC3D;AACF;AACA;EACElB,QAAQ,EAAEV,SAAS,CAAC6B,IAAI;EACxB;AACF;AACA;EACE;EACAC,MAAM,EAAE9B,SAAS,CAAC+B,IAAI;EACtB;AACF;AACA;EACE;EACAC,IAAI,EAAEhC,SAAS,CAAC+B,IAAI;EACpB;AACF;AACA;EACE;EACAE,MAAM,EAAEjC,SAAS,CAAC+B,IAAI;EACtB;AACF;AACA;EACE;EACAG,IAAI,EAAElC,SAAS,CAAC+B,IAAI;EACpB;AACF;AACA;EACEpB,IAAI,EAAEX,SAAS,CAACmC,SAAS,CAAC,CAACnC,SAAS,CAACoC,KAAK,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,EAAEpC,SAAS,CAACqC,OAAO,CAACrC,SAAS,CAACoC,KAAK,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;EAChJ;AACF;AACA;EACE;EACAE,MAAM,EAAEtC,SAAS,CAAC+B,IAAI;EACtB;AACF;AACA;EACE;EACAQ,IAAI,EAAEvC,SAAS,CAAC+B,IAAI;EACpB;AACF;AACA;AACA;EACEnB,KAAK,EAAEZ,SAAS,CAACwC,MAAM,CAACC,UAAU;EAClC;AACF;AACA;EACE;EACAC,MAAM,EAAE1C,SAAS,CAAC+B,IAAI;EACtB;AACF;AACA;EACE;EACAY,IAAI,EAAE3C,SAAS,CAAC+B,IAAI;EACpB;AACF;AACA;EACE;EACAa,MAAM,EAAE5C,SAAS,CAAC+B,IAAI;EACtB;AACF;AACA;EACE;EACAc,IAAI,EAAE7C,SAAS,CAAC+B;AAClB,CAAC,GAAG,KAAK,CAAC;AACV,IAAIN,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCF,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGnB,QAAQ,CAACoB,SAAS,GAAG3B,SAAS,CAACO,QAAQ,CAACoB,SAAS,CAAC,GAAG,KAAK,CAAC;AACrG;AACA,eAAe1B,SAAS,CAAC,CAAC,CAACM,QAAQ,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}