{"ast": null, "code": "import { arrow as arrow$1, computePosition } from '@floating-ui/dom';\nexport { autoPlacement, autoUpdate, computePosition, detectOverflow, flip, getOverflowAncestors, hide, inline, limitShift, offset, platform, shift, size } from '@floating-ui/dom';\nimport * as React from 'react';\nimport { useLayoutEffect, useEffect } from 'react';\nimport * as ReactDOM from 'react-dom';\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * This wraps the core `arrow` middleware to allow React refs as the element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow = options => {\n  function isRef(value) {\n    return {}.hasOwnProperty.call(value, 'current');\n  }\n  return {\n    name: 'arrow',\n    options,\n    fn(state) {\n      const {\n        element,\n        padding\n      } = typeof options === 'function' ? options(state) : options;\n      if (element && isRef(element)) {\n        if (element.current != null) {\n          return arrow$1({\n            element: element.current,\n            padding\n          }).fn(state);\n        }\n        return {};\n      } else if (element) {\n        return arrow$1({\n          element,\n          padding\n        }).fn(state);\n      }\n      return {};\n    }\n  };\n};\nvar index = typeof document !== 'undefined' ? useLayoutEffect : useEffect;\n\n// Fork of `fast-deep-equal` that only does the comparisons we need and compares\n// functions\nfunction deepEqual(a, b) {\n  if (a === b) {\n    return true;\n  }\n  if (typeof a !== typeof b) {\n    return false;\n  }\n  if (typeof a === 'function' && a.toString() === b.toString()) {\n    return true;\n  }\n  let length, i, keys;\n  if (a && b && typeof a == 'object') {\n    if (Array.isArray(a)) {\n      length = a.length;\n      if (length != b.length) return false;\n      for (i = length; i-- !== 0;) {\n        if (!deepEqual(a[i], b[i])) {\n          return false;\n        }\n      }\n      return true;\n    }\n    keys = Object.keys(a);\n    length = keys.length;\n    if (length !== Object.keys(b).length) {\n      return false;\n    }\n    for (i = length; i-- !== 0;) {\n      if (!{}.hasOwnProperty.call(b, keys[i])) {\n        return false;\n      }\n    }\n    for (i = length; i-- !== 0;) {\n      const key = keys[i];\n      if (key === '_owner' && a.$$typeof) {\n        continue;\n      }\n      if (!deepEqual(a[key], b[key])) {\n        return false;\n      }\n    }\n    return true;\n  }\n  return a !== a && b !== b;\n}\nfunction getDPR(element) {\n  if (typeof window === 'undefined') {\n    return 1;\n  }\n  const win = element.ownerDocument.defaultView || window;\n  return win.devicePixelRatio || 1;\n}\nfunction roundByDPR(element, value) {\n  const dpr = getDPR(element);\n  return Math.round(value * dpr) / dpr;\n}\nfunction useLatestRef(value) {\n  const ref = React.useRef(value);\n  index(() => {\n    ref.current = value;\n  });\n  return ref;\n}\n\n/**\n * Provides data to position a floating element.\n * @see https://floating-ui.com/docs/useFloating\n */\nfunction useFloating(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    placement = 'bottom',\n    strategy = 'absolute',\n    middleware = [],\n    platform,\n    elements: {\n      reference: externalReference,\n      floating: externalFloating\n    } = {},\n    transform = true,\n    whileElementsMounted,\n    open\n  } = options;\n  const [data, setData] = React.useState({\n    x: 0,\n    y: 0,\n    strategy,\n    placement,\n    middlewareData: {},\n    isPositioned: false\n  });\n  const [latestMiddleware, setLatestMiddleware] = React.useState(middleware);\n  if (!deepEqual(latestMiddleware, middleware)) {\n    setLatestMiddleware(middleware);\n  }\n  const [_reference, _setReference] = React.useState(null);\n  const [_floating, _setFloating] = React.useState(null);\n  const setReference = React.useCallback(node => {\n    if (node != referenceRef.current) {\n      referenceRef.current = node;\n      _setReference(node);\n    }\n  }, [_setReference]);\n  const setFloating = React.useCallback(node => {\n    if (node !== floatingRef.current) {\n      floatingRef.current = node;\n      _setFloating(node);\n    }\n  }, [_setFloating]);\n  const referenceEl = externalReference || _reference;\n  const floatingEl = externalFloating || _floating;\n  const referenceRef = React.useRef(null);\n  const floatingRef = React.useRef(null);\n  const dataRef = React.useRef(data);\n  const whileElementsMountedRef = useLatestRef(whileElementsMounted);\n  const platformRef = useLatestRef(platform);\n  const update = React.useCallback(() => {\n    if (!referenceRef.current || !floatingRef.current) {\n      return;\n    }\n    const config = {\n      placement,\n      strategy,\n      middleware: latestMiddleware\n    };\n    if (platformRef.current) {\n      config.platform = platformRef.current;\n    }\n    computePosition(referenceRef.current, floatingRef.current, config).then(data => {\n      const fullData = {\n        ...data,\n        isPositioned: true\n      };\n      if (isMountedRef.current && !deepEqual(dataRef.current, fullData)) {\n        dataRef.current = fullData;\n        ReactDOM.flushSync(() => {\n          setData(fullData);\n        });\n      }\n    });\n  }, [latestMiddleware, placement, strategy, platformRef]);\n  index(() => {\n    if (open === false && dataRef.current.isPositioned) {\n      dataRef.current.isPositioned = false;\n      setData(data => ({\n        ...data,\n        isPositioned: false\n      }));\n    }\n  }, [open]);\n  const isMountedRef = React.useRef(false);\n  index(() => {\n    isMountedRef.current = true;\n    return () => {\n      isMountedRef.current = false;\n    };\n  }, []);\n  index(() => {\n    if (referenceEl) referenceRef.current = referenceEl;\n    if (floatingEl) floatingRef.current = floatingEl;\n    if (referenceEl && floatingEl) {\n      if (whileElementsMountedRef.current) {\n        return whileElementsMountedRef.current(referenceEl, floatingEl, update);\n      } else {\n        update();\n      }\n    }\n  }, [referenceEl, floatingEl, update, whileElementsMountedRef]);\n  const refs = React.useMemo(() => ({\n    reference: referenceRef,\n    floating: floatingRef,\n    setReference,\n    setFloating\n  }), [setReference, setFloating]);\n  const elements = React.useMemo(() => ({\n    reference: referenceEl,\n    floating: floatingEl\n  }), [referenceEl, floatingEl]);\n  const floatingStyles = React.useMemo(() => {\n    const initialStyles = {\n      position: strategy,\n      left: 0,\n      top: 0\n    };\n    if (!elements.floating) {\n      return initialStyles;\n    }\n    const x = roundByDPR(elements.floating, data.x);\n    const y = roundByDPR(elements.floating, data.y);\n    if (transform) {\n      return {\n        ...initialStyles,\n        transform: \"translate(\" + x + \"px, \" + y + \"px)\",\n        ...(getDPR(elements.floating) >= 1.5 && {\n          willChange: 'transform'\n        })\n      };\n    }\n    return {\n      position: strategy,\n      left: x,\n      top: y\n    };\n  }, [strategy, transform, elements.floating, data.x, data.y]);\n  return React.useMemo(() => ({\n    ...data,\n    update,\n    refs,\n    elements,\n    floatingStyles\n  }), [data, update, refs, elements, floatingStyles]);\n}\nexport { arrow, useFloating };", "map": {"version": 3, "names": ["arrow", "arrow$1", "computePosition", "autoPlacement", "autoUpdate", "detectOverflow", "flip", "getOverflowAncestors", "hide", "inline", "limitShift", "offset", "platform", "shift", "size", "React", "useLayoutEffect", "useEffect", "ReactDOM", "options", "isRef", "value", "hasOwnProperty", "call", "name", "fn", "state", "element", "padding", "current", "index", "document", "deepEqual", "a", "b", "toString", "length", "i", "keys", "Array", "isArray", "Object", "key", "$$typeof", "getDPR", "window", "win", "ownerDocument", "defaultView", "devicePixelRatio", "roundByDPR", "dpr", "Math", "round", "useLatestRef", "ref", "useRef", "useFloating", "placement", "strategy", "middleware", "elements", "reference", "externalReference", "floating", "externalFloating", "transform", "whileElementsMounted", "open", "data", "setData", "useState", "x", "y", "middlewareData", "isPositioned", "latestMiddleware", "setLatestMiddleware", "_reference", "_setReference", "_floating", "_setFloating", "setReference", "useCallback", "node", "referenceRef", "setFloating", "floatingRef", "referenceEl", "floatingEl", "dataRef", "whileElementsMountedRef", "platformRef", "update", "config", "then", "fullData", "isMountedRef", "flushSync", "refs", "useMemo", "floatingStyles", "initialStyles", "position", "left", "top", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@floating-ui/react-dom/dist/floating-ui.react-dom.mjs"], "sourcesContent": ["import { arrow as arrow$1, computePosition } from '@floating-ui/dom';\nexport { autoPlacement, autoUpdate, computePosition, detectOverflow, flip, getOverflowAncestors, hide, inline, limitShift, offset, platform, shift, size } from '@floating-ui/dom';\nimport * as React from 'react';\nimport { useLayoutEffect, useEffect } from 'react';\nimport * as ReactDOM from 'react-dom';\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * This wraps the core `arrow` middleware to allow React refs as the element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow = options => {\n  function isRef(value) {\n    return {}.hasOwnProperty.call(value, 'current');\n  }\n  return {\n    name: 'arrow',\n    options,\n    fn(state) {\n      const {\n        element,\n        padding\n      } = typeof options === 'function' ? options(state) : options;\n      if (element && isRef(element)) {\n        if (element.current != null) {\n          return arrow$1({\n            element: element.current,\n            padding\n          }).fn(state);\n        }\n        return {};\n      } else if (element) {\n        return arrow$1({\n          element,\n          padding\n        }).fn(state);\n      }\n      return {};\n    }\n  };\n};\n\nvar index = typeof document !== 'undefined' ? useLayoutEffect : useEffect;\n\n// Fork of `fast-deep-equal` that only does the comparisons we need and compares\n// functions\nfunction deepEqual(a, b) {\n  if (a === b) {\n    return true;\n  }\n  if (typeof a !== typeof b) {\n    return false;\n  }\n  if (typeof a === 'function' && a.toString() === b.toString()) {\n    return true;\n  }\n  let length, i, keys;\n  if (a && b && typeof a == 'object') {\n    if (Array.isArray(a)) {\n      length = a.length;\n      if (length != b.length) return false;\n      for (i = length; i-- !== 0;) {\n        if (!deepEqual(a[i], b[i])) {\n          return false;\n        }\n      }\n      return true;\n    }\n    keys = Object.keys(a);\n    length = keys.length;\n    if (length !== Object.keys(b).length) {\n      return false;\n    }\n    for (i = length; i-- !== 0;) {\n      if (!{}.hasOwnProperty.call(b, keys[i])) {\n        return false;\n      }\n    }\n    for (i = length; i-- !== 0;) {\n      const key = keys[i];\n      if (key === '_owner' && a.$$typeof) {\n        continue;\n      }\n      if (!deepEqual(a[key], b[key])) {\n        return false;\n      }\n    }\n    return true;\n  }\n  return a !== a && b !== b;\n}\n\nfunction getDPR(element) {\n  if (typeof window === 'undefined') {\n    return 1;\n  }\n  const win = element.ownerDocument.defaultView || window;\n  return win.devicePixelRatio || 1;\n}\n\nfunction roundByDPR(element, value) {\n  const dpr = getDPR(element);\n  return Math.round(value * dpr) / dpr;\n}\n\nfunction useLatestRef(value) {\n  const ref = React.useRef(value);\n  index(() => {\n    ref.current = value;\n  });\n  return ref;\n}\n\n/**\n * Provides data to position a floating element.\n * @see https://floating-ui.com/docs/useFloating\n */\nfunction useFloating(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    placement = 'bottom',\n    strategy = 'absolute',\n    middleware = [],\n    platform,\n    elements: {\n      reference: externalReference,\n      floating: externalFloating\n    } = {},\n    transform = true,\n    whileElementsMounted,\n    open\n  } = options;\n  const [data, setData] = React.useState({\n    x: 0,\n    y: 0,\n    strategy,\n    placement,\n    middlewareData: {},\n    isPositioned: false\n  });\n  const [latestMiddleware, setLatestMiddleware] = React.useState(middleware);\n  if (!deepEqual(latestMiddleware, middleware)) {\n    setLatestMiddleware(middleware);\n  }\n  const [_reference, _setReference] = React.useState(null);\n  const [_floating, _setFloating] = React.useState(null);\n  const setReference = React.useCallback(node => {\n    if (node != referenceRef.current) {\n      referenceRef.current = node;\n      _setReference(node);\n    }\n  }, [_setReference]);\n  const setFloating = React.useCallback(node => {\n    if (node !== floatingRef.current) {\n      floatingRef.current = node;\n      _setFloating(node);\n    }\n  }, [_setFloating]);\n  const referenceEl = externalReference || _reference;\n  const floatingEl = externalFloating || _floating;\n  const referenceRef = React.useRef(null);\n  const floatingRef = React.useRef(null);\n  const dataRef = React.useRef(data);\n  const whileElementsMountedRef = useLatestRef(whileElementsMounted);\n  const platformRef = useLatestRef(platform);\n  const update = React.useCallback(() => {\n    if (!referenceRef.current || !floatingRef.current) {\n      return;\n    }\n    const config = {\n      placement,\n      strategy,\n      middleware: latestMiddleware\n    };\n    if (platformRef.current) {\n      config.platform = platformRef.current;\n    }\n    computePosition(referenceRef.current, floatingRef.current, config).then(data => {\n      const fullData = {\n        ...data,\n        isPositioned: true\n      };\n      if (isMountedRef.current && !deepEqual(dataRef.current, fullData)) {\n        dataRef.current = fullData;\n        ReactDOM.flushSync(() => {\n          setData(fullData);\n        });\n      }\n    });\n  }, [latestMiddleware, placement, strategy, platformRef]);\n  index(() => {\n    if (open === false && dataRef.current.isPositioned) {\n      dataRef.current.isPositioned = false;\n      setData(data => ({\n        ...data,\n        isPositioned: false\n      }));\n    }\n  }, [open]);\n  const isMountedRef = React.useRef(false);\n  index(() => {\n    isMountedRef.current = true;\n    return () => {\n      isMountedRef.current = false;\n    };\n  }, []);\n  index(() => {\n    if (referenceEl) referenceRef.current = referenceEl;\n    if (floatingEl) floatingRef.current = floatingEl;\n    if (referenceEl && floatingEl) {\n      if (whileElementsMountedRef.current) {\n        return whileElementsMountedRef.current(referenceEl, floatingEl, update);\n      } else {\n        update();\n      }\n    }\n  }, [referenceEl, floatingEl, update, whileElementsMountedRef]);\n  const refs = React.useMemo(() => ({\n    reference: referenceRef,\n    floating: floatingRef,\n    setReference,\n    setFloating\n  }), [setReference, setFloating]);\n  const elements = React.useMemo(() => ({\n    reference: referenceEl,\n    floating: floatingEl\n  }), [referenceEl, floatingEl]);\n  const floatingStyles = React.useMemo(() => {\n    const initialStyles = {\n      position: strategy,\n      left: 0,\n      top: 0\n    };\n    if (!elements.floating) {\n      return initialStyles;\n    }\n    const x = roundByDPR(elements.floating, data.x);\n    const y = roundByDPR(elements.floating, data.y);\n    if (transform) {\n      return {\n        ...initialStyles,\n        transform: \"translate(\" + x + \"px, \" + y + \"px)\",\n        ...(getDPR(elements.floating) >= 1.5 && {\n          willChange: 'transform'\n        })\n      };\n    }\n    return {\n      position: strategy,\n      left: x,\n      top: y\n    };\n  }, [strategy, transform, elements.floating, data.x, data.y]);\n  return React.useMemo(() => ({\n    ...data,\n    update,\n    refs,\n    elements,\n    floatingStyles\n  }), [data, update, refs, elements, floatingStyles]);\n}\n\nexport { arrow, useFloating };\n"], "mappings": "AAAA,SAASA,KAAK,IAAIC,OAAO,EAAEC,eAAe,QAAQ,kBAAkB;AACpE,SAASC,aAAa,EAAEC,UAAU,EAAEF,eAAe,EAAEG,cAAc,EAAEC,IAAI,EAAEC,oBAAoB,EAAEC,IAAI,EAAEC,MAAM,EAAEC,UAAU,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,IAAI,QAAQ,kBAAkB;AAClL,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,eAAe,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,KAAKC,QAAQ,MAAM,WAAW;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA,MAAMlB,KAAK,GAAGmB,OAAO,IAAI;EACvB,SAASC,KAAKA,CAACC,KAAK,EAAE;IACpB,OAAO,CAAC,CAAC,CAACC,cAAc,CAACC,IAAI,CAACF,KAAK,EAAE,SAAS,CAAC;EACjD;EACA,OAAO;IACLG,IAAI,EAAE,OAAO;IACbL,OAAO;IACPM,EAAEA,CAACC,KAAK,EAAE;MACR,MAAM;QACJC,OAAO;QACPC;MACF,CAAC,GAAG,OAAOT,OAAO,KAAK,UAAU,GAAGA,OAAO,CAACO,KAAK,CAAC,GAAGP,OAAO;MAC5D,IAAIQ,OAAO,IAAIP,KAAK,CAACO,OAAO,CAAC,EAAE;QAC7B,IAAIA,OAAO,CAACE,OAAO,IAAI,IAAI,EAAE;UAC3B,OAAO5B,OAAO,CAAC;YACb0B,OAAO,EAAEA,OAAO,CAACE,OAAO;YACxBD;UACF,CAAC,CAAC,CAACH,EAAE,CAACC,KAAK,CAAC;QACd;QACA,OAAO,CAAC,CAAC;MACX,CAAC,MAAM,IAAIC,OAAO,EAAE;QAClB,OAAO1B,OAAO,CAAC;UACb0B,OAAO;UACPC;QACF,CAAC,CAAC,CAACH,EAAE,CAACC,KAAK,CAAC;MACd;MACA,OAAO,CAAC,CAAC;IACX;EACF,CAAC;AACH,CAAC;AAED,IAAII,KAAK,GAAG,OAAOC,QAAQ,KAAK,WAAW,GAAGf,eAAe,GAAGC,SAAS;;AAEzE;AACA;AACA,SAASe,SAASA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACvB,IAAID,CAAC,KAAKC,CAAC,EAAE;IACX,OAAO,IAAI;EACb;EACA,IAAI,OAAOD,CAAC,KAAK,OAAOC,CAAC,EAAE;IACzB,OAAO,KAAK;EACd;EACA,IAAI,OAAOD,CAAC,KAAK,UAAU,IAAIA,CAAC,CAACE,QAAQ,CAAC,CAAC,KAAKD,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE;IAC5D,OAAO,IAAI;EACb;EACA,IAAIC,MAAM,EAAEC,CAAC,EAAEC,IAAI;EACnB,IAAIL,CAAC,IAAIC,CAAC,IAAI,OAAOD,CAAC,IAAI,QAAQ,EAAE;IAClC,IAAIM,KAAK,CAACC,OAAO,CAACP,CAAC,CAAC,EAAE;MACpBG,MAAM,GAAGH,CAAC,CAACG,MAAM;MACjB,IAAIA,MAAM,IAAIF,CAAC,CAACE,MAAM,EAAE,OAAO,KAAK;MACpC,KAAKC,CAAC,GAAGD,MAAM,EAAEC,CAAC,EAAE,KAAK,CAAC,GAAG;QAC3B,IAAI,CAACL,SAAS,CAACC,CAAC,CAACI,CAAC,CAAC,EAAEH,CAAC,CAACG,CAAC,CAAC,CAAC,EAAE;UAC1B,OAAO,KAAK;QACd;MACF;MACA,OAAO,IAAI;IACb;IACAC,IAAI,GAAGG,MAAM,CAACH,IAAI,CAACL,CAAC,CAAC;IACrBG,MAAM,GAAGE,IAAI,CAACF,MAAM;IACpB,IAAIA,MAAM,KAAKK,MAAM,CAACH,IAAI,CAACJ,CAAC,CAAC,CAACE,MAAM,EAAE;MACpC,OAAO,KAAK;IACd;IACA,KAAKC,CAAC,GAAGD,MAAM,EAAEC,CAAC,EAAE,KAAK,CAAC,GAAG;MAC3B,IAAI,CAAC,CAAC,CAAC,CAACf,cAAc,CAACC,IAAI,CAACW,CAAC,EAAEI,IAAI,CAACD,CAAC,CAAC,CAAC,EAAE;QACvC,OAAO,KAAK;MACd;IACF;IACA,KAAKA,CAAC,GAAGD,MAAM,EAAEC,CAAC,EAAE,KAAK,CAAC,GAAG;MAC3B,MAAMK,GAAG,GAAGJ,IAAI,CAACD,CAAC,CAAC;MACnB,IAAIK,GAAG,KAAK,QAAQ,IAAIT,CAAC,CAACU,QAAQ,EAAE;QAClC;MACF;MACA,IAAI,CAACX,SAAS,CAACC,CAAC,CAACS,GAAG,CAAC,EAAER,CAAC,CAACQ,GAAG,CAAC,CAAC,EAAE;QAC9B,OAAO,KAAK;MACd;IACF;IACA,OAAO,IAAI;EACb;EACA,OAAOT,CAAC,KAAKA,CAAC,IAAIC,CAAC,KAAKA,CAAC;AAC3B;AAEA,SAASU,MAAMA,CAACjB,OAAO,EAAE;EACvB,IAAI,OAAOkB,MAAM,KAAK,WAAW,EAAE;IACjC,OAAO,CAAC;EACV;EACA,MAAMC,GAAG,GAAGnB,OAAO,CAACoB,aAAa,CAACC,WAAW,IAAIH,MAAM;EACvD,OAAOC,GAAG,CAACG,gBAAgB,IAAI,CAAC;AAClC;AAEA,SAASC,UAAUA,CAACvB,OAAO,EAAEN,KAAK,EAAE;EAClC,MAAM8B,GAAG,GAAGP,MAAM,CAACjB,OAAO,CAAC;EAC3B,OAAOyB,IAAI,CAACC,KAAK,CAAChC,KAAK,GAAG8B,GAAG,CAAC,GAAGA,GAAG;AACtC;AAEA,SAASG,YAAYA,CAACjC,KAAK,EAAE;EAC3B,MAAMkC,GAAG,GAAGxC,KAAK,CAACyC,MAAM,CAACnC,KAAK,CAAC;EAC/BS,KAAK,CAAC,MAAM;IACVyB,GAAG,CAAC1B,OAAO,GAAGR,KAAK;EACrB,CAAC,CAAC;EACF,OAAOkC,GAAG;AACZ;;AAEA;AACA;AACA;AACA;AACA,SAASE,WAAWA,CAACtC,OAAO,EAAE;EAC5B,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,CAAC,CAAC;EACd;EACA,MAAM;IACJuC,SAAS,GAAG,QAAQ;IACpBC,QAAQ,GAAG,UAAU;IACrBC,UAAU,GAAG,EAAE;IACfhD,QAAQ;IACRiD,QAAQ,EAAE;MACRC,SAAS,EAAEC,iBAAiB;MAC5BC,QAAQ,EAAEC;IACZ,CAAC,GAAG,CAAC,CAAC;IACNC,SAAS,GAAG,IAAI;IAChBC,oBAAoB;IACpBC;EACF,CAAC,GAAGjD,OAAO;EACX,MAAM,CAACkD,IAAI,EAAEC,OAAO,CAAC,GAAGvD,KAAK,CAACwD,QAAQ,CAAC;IACrCC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJd,QAAQ;IACRD,SAAS;IACTgB,cAAc,EAAE,CAAC,CAAC;IAClBC,YAAY,EAAE;EAChB,CAAC,CAAC;EACF,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG9D,KAAK,CAACwD,QAAQ,CAACX,UAAU,CAAC;EAC1E,IAAI,CAAC5B,SAAS,CAAC4C,gBAAgB,EAAEhB,UAAU,CAAC,EAAE;IAC5CiB,mBAAmB,CAACjB,UAAU,CAAC;EACjC;EACA,MAAM,CAACkB,UAAU,EAAEC,aAAa,CAAC,GAAGhE,KAAK,CAACwD,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACS,SAAS,EAAEC,YAAY,CAAC,GAAGlE,KAAK,CAACwD,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAMW,YAAY,GAAGnE,KAAK,CAACoE,WAAW,CAACC,IAAI,IAAI;IAC7C,IAAIA,IAAI,IAAIC,YAAY,CAACxD,OAAO,EAAE;MAChCwD,YAAY,CAACxD,OAAO,GAAGuD,IAAI;MAC3BL,aAAa,CAACK,IAAI,CAAC;IACrB;EACF,CAAC,EAAE,CAACL,aAAa,CAAC,CAAC;EACnB,MAAMO,WAAW,GAAGvE,KAAK,CAACoE,WAAW,CAACC,IAAI,IAAI;IAC5C,IAAIA,IAAI,KAAKG,WAAW,CAAC1D,OAAO,EAAE;MAChC0D,WAAW,CAAC1D,OAAO,GAAGuD,IAAI;MAC1BH,YAAY,CAACG,IAAI,CAAC;IACpB;EACF,CAAC,EAAE,CAACH,YAAY,CAAC,CAAC;EAClB,MAAMO,WAAW,GAAGzB,iBAAiB,IAAIe,UAAU;EACnD,MAAMW,UAAU,GAAGxB,gBAAgB,IAAIe,SAAS;EAChD,MAAMK,YAAY,GAAGtE,KAAK,CAACyC,MAAM,CAAC,IAAI,CAAC;EACvC,MAAM+B,WAAW,GAAGxE,KAAK,CAACyC,MAAM,CAAC,IAAI,CAAC;EACtC,MAAMkC,OAAO,GAAG3E,KAAK,CAACyC,MAAM,CAACa,IAAI,CAAC;EAClC,MAAMsB,uBAAuB,GAAGrC,YAAY,CAACa,oBAAoB,CAAC;EAClE,MAAMyB,WAAW,GAAGtC,YAAY,CAAC1C,QAAQ,CAAC;EAC1C,MAAMiF,MAAM,GAAG9E,KAAK,CAACoE,WAAW,CAAC,MAAM;IACrC,IAAI,CAACE,YAAY,CAACxD,OAAO,IAAI,CAAC0D,WAAW,CAAC1D,OAAO,EAAE;MACjD;IACF;IACA,MAAMiE,MAAM,GAAG;MACbpC,SAAS;MACTC,QAAQ;MACRC,UAAU,EAAEgB;IACd,CAAC;IACD,IAAIgB,WAAW,CAAC/D,OAAO,EAAE;MACvBiE,MAAM,CAAClF,QAAQ,GAAGgF,WAAW,CAAC/D,OAAO;IACvC;IACA3B,eAAe,CAACmF,YAAY,CAACxD,OAAO,EAAE0D,WAAW,CAAC1D,OAAO,EAAEiE,MAAM,CAAC,CAACC,IAAI,CAAC1B,IAAI,IAAI;MAC9E,MAAM2B,QAAQ,GAAG;QACf,GAAG3B,IAAI;QACPM,YAAY,EAAE;MAChB,CAAC;MACD,IAAIsB,YAAY,CAACpE,OAAO,IAAI,CAACG,SAAS,CAAC0D,OAAO,CAAC7D,OAAO,EAAEmE,QAAQ,CAAC,EAAE;QACjEN,OAAO,CAAC7D,OAAO,GAAGmE,QAAQ;QAC1B9E,QAAQ,CAACgF,SAAS,CAAC,MAAM;UACvB5B,OAAO,CAAC0B,QAAQ,CAAC;QACnB,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACpB,gBAAgB,EAAElB,SAAS,EAAEC,QAAQ,EAAEiC,WAAW,CAAC,CAAC;EACxD9D,KAAK,CAAC,MAAM;IACV,IAAIsC,IAAI,KAAK,KAAK,IAAIsB,OAAO,CAAC7D,OAAO,CAAC8C,YAAY,EAAE;MAClDe,OAAO,CAAC7D,OAAO,CAAC8C,YAAY,GAAG,KAAK;MACpCL,OAAO,CAACD,IAAI,KAAK;QACf,GAAGA,IAAI;QACPM,YAAY,EAAE;MAChB,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EAAE,CAACP,IAAI,CAAC,CAAC;EACV,MAAM6B,YAAY,GAAGlF,KAAK,CAACyC,MAAM,CAAC,KAAK,CAAC;EACxC1B,KAAK,CAAC,MAAM;IACVmE,YAAY,CAACpE,OAAO,GAAG,IAAI;IAC3B,OAAO,MAAM;MACXoE,YAAY,CAACpE,OAAO,GAAG,KAAK;IAC9B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACNC,KAAK,CAAC,MAAM;IACV,IAAI0D,WAAW,EAAEH,YAAY,CAACxD,OAAO,GAAG2D,WAAW;IACnD,IAAIC,UAAU,EAAEF,WAAW,CAAC1D,OAAO,GAAG4D,UAAU;IAChD,IAAID,WAAW,IAAIC,UAAU,EAAE;MAC7B,IAAIE,uBAAuB,CAAC9D,OAAO,EAAE;QACnC,OAAO8D,uBAAuB,CAAC9D,OAAO,CAAC2D,WAAW,EAAEC,UAAU,EAAEI,MAAM,CAAC;MACzE,CAAC,MAAM;QACLA,MAAM,CAAC,CAAC;MACV;IACF;EACF,CAAC,EAAE,CAACL,WAAW,EAAEC,UAAU,EAAEI,MAAM,EAAEF,uBAAuB,CAAC,CAAC;EAC9D,MAAMQ,IAAI,GAAGpF,KAAK,CAACqF,OAAO,CAAC,OAAO;IAChCtC,SAAS,EAAEuB,YAAY;IACvBrB,QAAQ,EAAEuB,WAAW;IACrBL,YAAY;IACZI;EACF,CAAC,CAAC,EAAE,CAACJ,YAAY,EAAEI,WAAW,CAAC,CAAC;EAChC,MAAMzB,QAAQ,GAAG9C,KAAK,CAACqF,OAAO,CAAC,OAAO;IACpCtC,SAAS,EAAE0B,WAAW;IACtBxB,QAAQ,EAAEyB;EACZ,CAAC,CAAC,EAAE,CAACD,WAAW,EAAEC,UAAU,CAAC,CAAC;EAC9B,MAAMY,cAAc,GAAGtF,KAAK,CAACqF,OAAO,CAAC,MAAM;IACzC,MAAME,aAAa,GAAG;MACpBC,QAAQ,EAAE5C,QAAQ;MAClB6C,IAAI,EAAE,CAAC;MACPC,GAAG,EAAE;IACP,CAAC;IACD,IAAI,CAAC5C,QAAQ,CAACG,QAAQ,EAAE;MACtB,OAAOsC,aAAa;IACtB;IACA,MAAM9B,CAAC,GAAGtB,UAAU,CAACW,QAAQ,CAACG,QAAQ,EAAEK,IAAI,CAACG,CAAC,CAAC;IAC/C,MAAMC,CAAC,GAAGvB,UAAU,CAACW,QAAQ,CAACG,QAAQ,EAAEK,IAAI,CAACI,CAAC,CAAC;IAC/C,IAAIP,SAAS,EAAE;MACb,OAAO;QACL,GAAGoC,aAAa;QAChBpC,SAAS,EAAE,YAAY,GAAGM,CAAC,GAAG,MAAM,GAAGC,CAAC,GAAG,KAAK;QAChD,IAAI7B,MAAM,CAACiB,QAAQ,CAACG,QAAQ,CAAC,IAAI,GAAG,IAAI;UACtC0C,UAAU,EAAE;QACd,CAAC;MACH,CAAC;IACH;IACA,OAAO;MACLH,QAAQ,EAAE5C,QAAQ;MAClB6C,IAAI,EAAEhC,CAAC;MACPiC,GAAG,EAAEhC;IACP,CAAC;EACH,CAAC,EAAE,CAACd,QAAQ,EAAEO,SAAS,EAAEL,QAAQ,CAACG,QAAQ,EAAEK,IAAI,CAACG,CAAC,EAAEH,IAAI,CAACI,CAAC,CAAC,CAAC;EAC5D,OAAO1D,KAAK,CAACqF,OAAO,CAAC,OAAO;IAC1B,GAAG/B,IAAI;IACPwB,MAAM;IACNM,IAAI;IACJtC,QAAQ;IACRwC;EACF,CAAC,CAAC,EAAE,CAAChC,IAAI,EAAEwB,MAAM,EAAEM,IAAI,EAAEtC,QAAQ,EAAEwC,cAAc,CAAC,CAAC;AACrD;AAEA,SAASrG,KAAK,EAAEyD,WAAW"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}