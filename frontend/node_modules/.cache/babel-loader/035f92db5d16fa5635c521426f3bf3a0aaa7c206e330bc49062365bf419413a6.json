{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"checked\", \"defaultChecked\", \"disabled\", \"onBlur\", \"onChange\", \"onFocus\", \"onFocusVisible\", \"readOnly\", \"required\", \"slotProps\", \"slots\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { unstable_composeClasses as composeClasses } from '../composeClasses';\nimport { useSwitch } from '../useSwitch';\nimport { useSlotProps } from '../utils';\nimport { useClassNamesOverride } from '../utils/ClassNameConfigurator';\nimport { getSwitchUtilityClass } from './switchClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    checked,\n    disabled,\n    focusVisible,\n    readOnly\n  } = ownerState;\n  const slots = {\n    root: ['root', checked && 'checked', disabled && 'disabled', focusVisible && 'focusVisible', readOnly && 'readOnly'],\n    thumb: ['thumb'],\n    input: ['input'],\n    track: ['track']\n  };\n  return composeClasses(slots, useClassNamesOverride(getSwitchUtilityClass));\n};\n\n/**\n * The foundation for building custom-styled switches.\n *\n * Demos:\n *\n * - [Switch](https://mui.com/base-ui/react-switch/)\n *\n * API:\n *\n * - [Switch API](https://mui.com/base-ui/react-switch/components-api/#switch)\n */\nconst Switch = /*#__PURE__*/React.forwardRef(function Switch(props, forwardedRef) {\n  var _slots$root, _slots$thumb, _slots$input, _slots$track;\n  const {\n      slotProps = {},\n      slots = {}\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    getInputProps,\n    checked,\n    disabled,\n    focusVisible,\n    readOnly\n  } = useSwitch(props);\n  const ownerState = _extends({}, props, {\n    checked,\n    disabled,\n    focusVisible,\n    readOnly\n  });\n  const classes = useUtilityClasses(ownerState);\n  const Root = (_slots$root = slots.root) != null ? _slots$root : 'span';\n  const rootProps = useSlotProps({\n    elementType: Root,\n    externalSlotProps: slotProps.root,\n    externalForwardedProps: other,\n    additionalProps: {\n      ref: forwardedRef\n    },\n    ownerState,\n    className: classes.root\n  });\n  const Thumb = (_slots$thumb = slots.thumb) != null ? _slots$thumb : 'span';\n  const thumbProps = useSlotProps({\n    elementType: Thumb,\n    externalSlotProps: slotProps.thumb,\n    ownerState,\n    className: classes.thumb\n  });\n  const Input = (_slots$input = slots.input) != null ? _slots$input : 'input';\n  const inputProps = useSlotProps({\n    elementType: Input,\n    getSlotProps: getInputProps,\n    externalSlotProps: slotProps.input,\n    ownerState,\n    className: classes.input\n  });\n  const Track = slots.track === null ? () => null : (_slots$track = slots.track) != null ? _slots$track : 'span';\n  const trackProps = useSlotProps({\n    elementType: Track,\n    externalSlotProps: slotProps.track,\n    ownerState,\n    className: classes.track\n  });\n  return /*#__PURE__*/_jsxs(Root, _extends({}, rootProps, {\n    children: [/*#__PURE__*/_jsx(Track, _extends({}, trackProps)), /*#__PURE__*/_jsx(Thumb, _extends({}, thumbProps)), /*#__PURE__*/_jsx(Input, _extends({}, inputProps))]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? Switch.propTypes /* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * If `true`, the component is checked.\n   */\n  checked: PropTypes.bool,\n  /**\n   * Class name applied to the root element.\n   */\n  className: PropTypes.string,\n  /**\n   * The default checked state. Use when the component is not controlled.\n   */\n  defaultChecked: PropTypes.bool,\n  /**\n   * If `true`, the component is disabled.\n   */\n  disabled: PropTypes.bool,\n  /**\n   * @ignore\n   */\n  onBlur: PropTypes.func,\n  /**\n   * Callback fired when the state is changed.\n   *\n   * @param {React.ChangeEvent<HTMLInputElement>} event The event source of the callback.\n   * You can pull out the new value by accessing `event.target.value` (string).\n   * You can pull out the new checked state by accessing `event.target.checked` (boolean).\n   */\n  onChange: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onFocus: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onFocusVisible: PropTypes.func,\n  /**\n   * If `true`, the component is read only.\n   */\n  readOnly: PropTypes.bool,\n  /**\n   * If `true`, the `input` element is required.\n   */\n  required: PropTypes.bool,\n  /**\n   * The props used for each slot inside the Switch.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    input: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    thumb: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    track: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside the Switch.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  slots: PropTypes /* @typescript-to-proptypes-ignore */.shape({\n    input: PropTypes.elementType,\n    root: PropTypes.elementType,\n    thumb: PropTypes.elementType,\n    track: PropTypes.oneOfType([PropTypes.elementType, PropTypes.oneOf([null])])\n  })\n} : void 0;\nexport { Switch };", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "PropTypes", "unstable_composeClasses", "composeClasses", "useSwitch", "useSlotProps", "useClassNamesOverride", "getSwitchUtilityClass", "jsx", "_jsx", "jsxs", "_jsxs", "useUtilityClasses", "ownerState", "checked", "disabled", "focusVisible", "readOnly", "slots", "root", "thumb", "input", "track", "Switch", "forwardRef", "props", "forwardedRef", "_slots$root", "_slots$thumb", "_slots$input", "_slots$track", "slotProps", "other", "getInputProps", "classes", "Root", "rootProps", "elementType", "externalSlotProps", "externalForwardedProps", "additionalProps", "ref", "className", "Thumb", "thumbProps", "Input", "inputProps", "getSlotProps", "Track", "trackProps", "children", "process", "env", "NODE_ENV", "propTypes", "bool", "string", "defaultChecked", "onBlur", "func", "onChange", "onFocus", "onFocusVisible", "required", "shape", "oneOfType", "object", "oneOf"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/base/Switch/Switch.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"checked\", \"defaultChecked\", \"disabled\", \"onBlur\", \"onChange\", \"onFocus\", \"onFocusVisible\", \"readOnly\", \"required\", \"slotProps\", \"slots\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { unstable_composeClasses as composeClasses } from '../composeClasses';\nimport { useSwitch } from '../useSwitch';\nimport { useSlotProps } from '../utils';\nimport { useClassNamesOverride } from '../utils/ClassNameConfigurator';\nimport { getSwitchUtilityClass } from './switchClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    checked,\n    disabled,\n    focusVisible,\n    readOnly\n  } = ownerState;\n  const slots = {\n    root: ['root', checked && 'checked', disabled && 'disabled', focusVisible && 'focusVisible', readOnly && 'readOnly'],\n    thumb: ['thumb'],\n    input: ['input'],\n    track: ['track']\n  };\n  return composeClasses(slots, useClassNamesOverride(getSwitchUtilityClass));\n};\n\n/**\n * The foundation for building custom-styled switches.\n *\n * Demos:\n *\n * - [Switch](https://mui.com/base-ui/react-switch/)\n *\n * API:\n *\n * - [Switch API](https://mui.com/base-ui/react-switch/components-api/#switch)\n */\nconst Switch = /*#__PURE__*/React.forwardRef(function Switch(props, forwardedRef) {\n  var _slots$root, _slots$thumb, _slots$input, _slots$track;\n  const {\n      slotProps = {},\n      slots = {}\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    getInputProps,\n    checked,\n    disabled,\n    focusVisible,\n    readOnly\n  } = useSwitch(props);\n  const ownerState = _extends({}, props, {\n    checked,\n    disabled,\n    focusVisible,\n    readOnly\n  });\n  const classes = useUtilityClasses(ownerState);\n  const Root = (_slots$root = slots.root) != null ? _slots$root : 'span';\n  const rootProps = useSlotProps({\n    elementType: Root,\n    externalSlotProps: slotProps.root,\n    externalForwardedProps: other,\n    additionalProps: {\n      ref: forwardedRef\n    },\n    ownerState,\n    className: classes.root\n  });\n  const Thumb = (_slots$thumb = slots.thumb) != null ? _slots$thumb : 'span';\n  const thumbProps = useSlotProps({\n    elementType: Thumb,\n    externalSlotProps: slotProps.thumb,\n    ownerState,\n    className: classes.thumb\n  });\n  const Input = (_slots$input = slots.input) != null ? _slots$input : 'input';\n  const inputProps = useSlotProps({\n    elementType: Input,\n    getSlotProps: getInputProps,\n    externalSlotProps: slotProps.input,\n    ownerState,\n    className: classes.input\n  });\n  const Track = slots.track === null ? () => null : (_slots$track = slots.track) != null ? _slots$track : 'span';\n  const trackProps = useSlotProps({\n    elementType: Track,\n    externalSlotProps: slotProps.track,\n    ownerState,\n    className: classes.track\n  });\n  return /*#__PURE__*/_jsxs(Root, _extends({}, rootProps, {\n    children: [/*#__PURE__*/_jsx(Track, _extends({}, trackProps)), /*#__PURE__*/_jsx(Thumb, _extends({}, thumbProps)), /*#__PURE__*/_jsx(Input, _extends({}, inputProps))]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? Switch.propTypes /* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * If `true`, the component is checked.\n   */\n  checked: PropTypes.bool,\n  /**\n   * Class name applied to the root element.\n   */\n  className: PropTypes.string,\n  /**\n   * The default checked state. Use when the component is not controlled.\n   */\n  defaultChecked: PropTypes.bool,\n  /**\n   * If `true`, the component is disabled.\n   */\n  disabled: PropTypes.bool,\n  /**\n   * @ignore\n   */\n  onBlur: PropTypes.func,\n  /**\n   * Callback fired when the state is changed.\n   *\n   * @param {React.ChangeEvent<HTMLInputElement>} event The event source of the callback.\n   * You can pull out the new value by accessing `event.target.value` (string).\n   * You can pull out the new checked state by accessing `event.target.checked` (boolean).\n   */\n  onChange: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onFocus: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onFocusVisible: PropTypes.func,\n  /**\n   * If `true`, the component is read only.\n   */\n  readOnly: PropTypes.bool,\n  /**\n   * If `true`, the `input` element is required.\n   */\n  required: PropTypes.bool,\n  /**\n   * The props used for each slot inside the Switch.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    input: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    thumb: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    track: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside the Switch.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  slots: PropTypes /* @typescript-to-proptypes-ignore */.shape({\n    input: PropTypes.elementType,\n    root: PropTypes.elementType,\n    thumb: PropTypes.elementType,\n    track: PropTypes.oneOfType([PropTypes.elementType, PropTypes.oneOf([null])])\n  })\n} : void 0;\nexport { Switch };"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,SAAS,EAAE,gBAAgB,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,EAAE,SAAS,EAAE,gBAAgB,EAAE,UAAU,EAAE,UAAU,EAAE,WAAW,EAAE,OAAO,CAAC;AAC5J,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,uBAAuB,IAAIC,cAAc,QAAQ,mBAAmB;AAC7E,SAASC,SAAS,QAAQ,cAAc;AACxC,SAASC,YAAY,QAAQ,UAAU;AACvC,SAASC,qBAAqB,QAAQ,gCAAgC;AACtE,SAASC,qBAAqB,QAAQ,iBAAiB;AACvD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,OAAO;IACPC,QAAQ;IACRC,YAAY;IACZC;EACF,CAAC,GAAGJ,UAAU;EACd,MAAMK,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEL,OAAO,IAAI,SAAS,EAAEC,QAAQ,IAAI,UAAU,EAAEC,YAAY,IAAI,cAAc,EAAEC,QAAQ,IAAI,UAAU,CAAC;IACpHG,KAAK,EAAE,CAAC,OAAO,CAAC;IAChBC,KAAK,EAAE,CAAC,OAAO,CAAC;IAChBC,KAAK,EAAE,CAAC,OAAO;EACjB,CAAC;EACD,OAAOnB,cAAc,CAACe,KAAK,EAAEZ,qBAAqB,CAACC,qBAAqB,CAAC,CAAC;AAC5E,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMgB,MAAM,GAAG,aAAavB,KAAK,CAACwB,UAAU,CAAC,SAASD,MAAMA,CAACE,KAAK,EAAEC,YAAY,EAAE;EAChF,IAAIC,WAAW,EAAEC,YAAY,EAAEC,YAAY,EAAEC,YAAY;EACzD,MAAM;MACFC,SAAS,GAAG,CAAC,CAAC;MACdb,KAAK,GAAG,CAAC;IACX,CAAC,GAAGO,KAAK;IACTO,KAAK,GAAGlC,6BAA6B,CAAC2B,KAAK,EAAE1B,SAAS,CAAC;EACzD,MAAM;IACJkC,aAAa;IACbnB,OAAO;IACPC,QAAQ;IACRC,YAAY;IACZC;EACF,CAAC,GAAGb,SAAS,CAACqB,KAAK,CAAC;EACpB,MAAMZ,UAAU,GAAGhB,QAAQ,CAAC,CAAC,CAAC,EAAE4B,KAAK,EAAE;IACrCX,OAAO;IACPC,QAAQ;IACRC,YAAY;IACZC;EACF,CAAC,CAAC;EACF,MAAMiB,OAAO,GAAGtB,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAMsB,IAAI,GAAG,CAACR,WAAW,GAAGT,KAAK,CAACC,IAAI,KAAK,IAAI,GAAGQ,WAAW,GAAG,MAAM;EACtE,MAAMS,SAAS,GAAG/B,YAAY,CAAC;IAC7BgC,WAAW,EAAEF,IAAI;IACjBG,iBAAiB,EAAEP,SAAS,CAACZ,IAAI;IACjCoB,sBAAsB,EAAEP,KAAK;IAC7BQ,eAAe,EAAE;MACfC,GAAG,EAAEf;IACP,CAAC;IACDb,UAAU;IACV6B,SAAS,EAAER,OAAO,CAACf;EACrB,CAAC,CAAC;EACF,MAAMwB,KAAK,GAAG,CAACf,YAAY,GAAGV,KAAK,CAACE,KAAK,KAAK,IAAI,GAAGQ,YAAY,GAAG,MAAM;EAC1E,MAAMgB,UAAU,GAAGvC,YAAY,CAAC;IAC9BgC,WAAW,EAAEM,KAAK;IAClBL,iBAAiB,EAAEP,SAAS,CAACX,KAAK;IAClCP,UAAU;IACV6B,SAAS,EAAER,OAAO,CAACd;EACrB,CAAC,CAAC;EACF,MAAMyB,KAAK,GAAG,CAAChB,YAAY,GAAGX,KAAK,CAACG,KAAK,KAAK,IAAI,GAAGQ,YAAY,GAAG,OAAO;EAC3E,MAAMiB,UAAU,GAAGzC,YAAY,CAAC;IAC9BgC,WAAW,EAAEQ,KAAK;IAClBE,YAAY,EAAEd,aAAa;IAC3BK,iBAAiB,EAAEP,SAAS,CAACV,KAAK;IAClCR,UAAU;IACV6B,SAAS,EAAER,OAAO,CAACb;EACrB,CAAC,CAAC;EACF,MAAM2B,KAAK,GAAG9B,KAAK,CAACI,KAAK,KAAK,IAAI,GAAG,MAAM,IAAI,GAAG,CAACQ,YAAY,GAAGZ,KAAK,CAACI,KAAK,KAAK,IAAI,GAAGQ,YAAY,GAAG,MAAM;EAC9G,MAAMmB,UAAU,GAAG5C,YAAY,CAAC;IAC9BgC,WAAW,EAAEW,KAAK;IAClBV,iBAAiB,EAAEP,SAAS,CAACT,KAAK;IAClCT,UAAU;IACV6B,SAAS,EAAER,OAAO,CAACZ;EACrB,CAAC,CAAC;EACF,OAAO,aAAaX,KAAK,CAACwB,IAAI,EAAEtC,QAAQ,CAAC,CAAC,CAAC,EAAEuC,SAAS,EAAE;IACtDc,QAAQ,EAAE,CAAC,aAAazC,IAAI,CAACuC,KAAK,EAAEnD,QAAQ,CAAC,CAAC,CAAC,EAAEoD,UAAU,CAAC,CAAC,EAAE,aAAaxC,IAAI,CAACkC,KAAK,EAAE9C,QAAQ,CAAC,CAAC,CAAC,EAAE+C,UAAU,CAAC,CAAC,EAAE,aAAanC,IAAI,CAACoC,KAAK,EAAEhD,QAAQ,CAAC,CAAC,CAAC,EAAEiD,UAAU,CAAC,CAAC;EACvK,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFK,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG9B,MAAM,CAAC+B,SAAS,CAAC,yBAAyB;EAChF;EACA;EACA;EACA;EACA;AACF;AACA;EACExC,OAAO,EAAEb,SAAS,CAACsD,IAAI;EACvB;AACF;AACA;EACEb,SAAS,EAAEzC,SAAS,CAACuD,MAAM;EAC3B;AACF;AACA;EACEC,cAAc,EAAExD,SAAS,CAACsD,IAAI;EAC9B;AACF;AACA;EACExC,QAAQ,EAAEd,SAAS,CAACsD,IAAI;EACxB;AACF;AACA;EACEG,MAAM,EAAEzD,SAAS,CAAC0D,IAAI;EACtB;AACF;AACA;AACA;AACA;AACA;AACA;EACEC,QAAQ,EAAE3D,SAAS,CAAC0D,IAAI;EACxB;AACF;AACA;EACEE,OAAO,EAAE5D,SAAS,CAAC0D,IAAI;EACvB;AACF;AACA;EACEG,cAAc,EAAE7D,SAAS,CAAC0D,IAAI;EAC9B;AACF;AACA;EACE1C,QAAQ,EAAEhB,SAAS,CAACsD,IAAI;EACxB;AACF;AACA;EACEQ,QAAQ,EAAE9D,SAAS,CAACsD,IAAI;EACxB;AACF;AACA;AACA;EACExB,SAAS,EAAE9B,SAAS,CAAC+D,KAAK,CAAC;IACzB3C,KAAK,EAAEpB,SAAS,CAACgE,SAAS,CAAC,CAAChE,SAAS,CAAC0D,IAAI,EAAE1D,SAAS,CAACiE,MAAM,CAAC,CAAC;IAC9D/C,IAAI,EAAElB,SAAS,CAACgE,SAAS,CAAC,CAAChE,SAAS,CAAC0D,IAAI,EAAE1D,SAAS,CAACiE,MAAM,CAAC,CAAC;IAC7D9C,KAAK,EAAEnB,SAAS,CAACgE,SAAS,CAAC,CAAChE,SAAS,CAAC0D,IAAI,EAAE1D,SAAS,CAACiE,MAAM,CAAC,CAAC;IAC9D5C,KAAK,EAAErB,SAAS,CAACgE,SAAS,CAAC,CAAChE,SAAS,CAAC0D,IAAI,EAAE1D,SAAS,CAACiE,MAAM,CAAC;EAC/D,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;EACEhD,KAAK,EAAEjB,SAAS,CAAC,sCAAsC+D,KAAK,CAAC;IAC3D3C,KAAK,EAAEpB,SAAS,CAACoC,WAAW;IAC5BlB,IAAI,EAAElB,SAAS,CAACoC,WAAW;IAC3BjB,KAAK,EAAEnB,SAAS,CAACoC,WAAW;IAC5Bf,KAAK,EAAErB,SAAS,CAACgE,SAAS,CAAC,CAAChE,SAAS,CAACoC,WAAW,EAAEpC,SAAS,CAACkE,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;EAC7E,CAAC;AACH,CAAC,GAAG,KAAK,CAAC;AACV,SAAS5C,MAAM"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}