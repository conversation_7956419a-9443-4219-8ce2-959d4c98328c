{"ast": null, "code": "import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"variant\"];\nimport { unstable_capitalize as capitalize } from '@mui/utils';\nfunction isEmpty(string) {\n  return string.length === 0;\n}\n\n/**\n * Generates string classKey based on the properties provided. It starts with the\n * variant if defined, and then it appends all other properties in alphabetical order.\n * @param {object} props - the properties for which the classKey should be created.\n */\nexport default function propsToClassKey(props) {\n  const {\n      variant\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  let classKey = variant || '';\n  Object.keys(other).sort().forEach(key => {\n    if (key === 'color') {\n      classKey += isEmpty(classKey) ? props[key] : capitalize(props[key]);\n    } else {\n      classKey += `${isEmpty(classKey) ? key : capitalize(key)}${capitalize(props[key].toString())}`;\n    }\n  });\n  return classKey;\n}", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_excluded", "unstable_capitalize", "capitalize", "isEmpty", "string", "length", "props<PERSON>o<PERSON><PERSON><PERSON><PERSON>", "props", "variant", "other", "classKey", "Object", "keys", "sort", "for<PERSON>ach", "key", "toString"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/system/esm/propsToClassKey.js"], "sourcesContent": ["import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"variant\"];\nimport { unstable_capitalize as capitalize } from '@mui/utils';\nfunction isEmpty(string) {\n  return string.length === 0;\n}\n\n/**\n * Generates string classKey based on the properties provided. It starts with the\n * variant if defined, and then it appends all other properties in alphabetical order.\n * @param {object} props - the properties for which the classKey should be created.\n */\nexport default function propsToClassKey(props) {\n  const {\n      variant\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  let classKey = variant || '';\n  Object.keys(other).sort().forEach(key => {\n    if (key === 'color') {\n      classKey += isEmpty(classKey) ? props[key] : capitalize(props[key]);\n    } else {\n      classKey += `${isEmpty(classKey) ? key : capitalize(key)}${capitalize(props[key].toString())}`;\n    }\n  });\n  return classKey;\n}"], "mappings": "AAAA,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,SAAS,CAAC;AAC7B,SAASC,mBAAmB,IAAIC,UAAU,QAAQ,YAAY;AAC9D,SAASC,OAAOA,CAACC,MAAM,EAAE;EACvB,OAAOA,MAAM,CAACC,MAAM,KAAK,CAAC;AAC5B;;AAEA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,eAAeA,CAACC,KAAK,EAAE;EAC7C,MAAM;MACFC;IACF,CAAC,GAAGD,KAAK;IACTE,KAAK,GAAGV,6BAA6B,CAACQ,KAAK,EAAEP,SAAS,CAAC;EACzD,IAAIU,QAAQ,GAAGF,OAAO,IAAI,EAAE;EAC5BG,MAAM,CAACC,IAAI,CAACH,KAAK,CAAC,CAACI,IAAI,CAAC,CAAC,CAACC,OAAO,CAACC,GAAG,IAAI;IACvC,IAAIA,GAAG,KAAK,OAAO,EAAE;MACnBL,QAAQ,IAAIP,OAAO,CAACO,QAAQ,CAAC,GAAGH,KAAK,CAACQ,GAAG,CAAC,GAAGb,UAAU,CAACK,KAAK,CAACQ,GAAG,CAAC,CAAC;IACrE,CAAC,MAAM;MACLL,QAAQ,IAAK,GAAEP,OAAO,CAACO,QAAQ,CAAC,GAAGK,GAAG,GAAGb,UAAU,CAACa,GAAG,CAAE,GAAEb,UAAU,CAACK,KAAK,CAACQ,GAAG,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,EAAC;IAChG;EACF,CAAC,CAAC;EACF,OAAON,QAAQ;AACjB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}