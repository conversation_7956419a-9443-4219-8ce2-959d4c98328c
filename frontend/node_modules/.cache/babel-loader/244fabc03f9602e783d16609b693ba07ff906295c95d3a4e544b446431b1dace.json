{"ast": null, "code": "'use client';\n\nexport { TextareaAutosize } from './TextareaAutosize';\nexport * from './TextareaAutosize.types';", "map": {"version": 3, "names": ["TextareaAutosize"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/base/TextareaAutosize/index.js"], "sourcesContent": ["'use client';\n\nexport { TextareaAutosize } from './TextareaAutosize';\nexport * from './TextareaAutosize.types';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,gBAAgB,QAAQ,oBAAoB;AACrD,cAAc,0BAA0B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}