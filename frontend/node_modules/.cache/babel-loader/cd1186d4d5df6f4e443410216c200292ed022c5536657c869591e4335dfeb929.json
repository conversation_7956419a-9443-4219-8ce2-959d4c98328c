{"ast": null, "code": "'use client';\n\nexport { Snackbar } from './Snackbar';\nexport * from './Snackbar.types';\nexport * from './snackbarClasses';", "map": {"version": 3, "names": ["Snackbar"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/base/Snackbar/index.js"], "sourcesContent": ["'use client';\n\nexport { Snackbar } from './Snackbar';\nexport * from './Snackbar.types';\nexport * from './snackbarClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,QAAQ,QAAQ,YAAY;AACrC,cAAc,kBAAkB;AAChC,cAAc,mBAAmB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}