{"ast": null, "code": "import { unstable_setRef as setRef } from '@mui/utils';\nexport default setRef;", "map": {"version": 3, "names": ["unstable_setRef", "setRef"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/material/utils/setRef.js"], "sourcesContent": ["import { unstable_setRef as setRef } from '@mui/utils';\nexport default setRef;"], "mappings": "AAAA,SAASA,eAAe,IAAIC,MAAM,QAAQ,YAAY;AACtD,eAAeA,MAAM"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}