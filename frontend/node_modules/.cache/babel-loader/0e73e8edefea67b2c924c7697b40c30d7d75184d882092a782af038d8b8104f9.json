{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"children\", \"className\", \"component\", \"dense\", \"disablePadding\", \"subheader\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { unstable_composeClasses as composeClasses } from '@mui/base/composeClasses';\nimport styled from '../styles/styled';\nimport useThemeProps from '../styles/useThemeProps';\nimport ListContext from './ListContext';\nimport { getListUtilityClass } from './listClasses';\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    disablePadding,\n    dense,\n    subheader\n  } = ownerState;\n  const slots = {\n    root: ['root', !disablePadding && 'padding', dense && 'dense', subheader && 'subheader']\n  };\n  return composeClasses(slots, getListUtilityClass, classes);\n};\nconst ListRoot = styled('ul', {\n  name: 'MuiList',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, !ownerState.disablePadding && styles.padding, ownerState.dense && styles.dense, ownerState.subheader && styles.subheader];\n  }\n})(({\n  ownerState\n}) => _extends({\n  listStyle: 'none',\n  margin: 0,\n  padding: 0,\n  position: 'relative'\n}, !ownerState.disablePadding && {\n  paddingTop: 8,\n  paddingBottom: 8\n}, ownerState.subheader && {\n  paddingTop: 0\n}));\nconst List = /*#__PURE__*/React.forwardRef(function List(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiList'\n  });\n  const {\n      children,\n      className,\n      component = 'ul',\n      dense = false,\n      disablePadding = false,\n      subheader\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const context = React.useMemo(() => ({\n    dense\n  }), [dense]);\n  const ownerState = _extends({}, props, {\n    component,\n    dense,\n    disablePadding\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(ListContext.Provider, {\n    value: context,\n    children: /*#__PURE__*/_jsxs(ListRoot, _extends({\n      as: component,\n      className: clsx(classes.root, className),\n      ref: ref,\n      ownerState: ownerState\n    }, other, {\n      children: [subheader, children]\n    }))\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? List.propTypes /* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit the d.ts file and run \"yarn proptypes\"     |\n  // ----------------------------------------------------------------------\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * If `true`, compact vertical padding designed for keyboard and mouse input is used for\n   * the list and list items.\n   * The prop is available to descendant components as the `dense` context.\n   * @default false\n   */\n  dense: PropTypes.bool,\n  /**\n   * If `true`, vertical padding is removed from the list.\n   * @default false\n   */\n  disablePadding: PropTypes.bool,\n  /**\n   * The content of the subheader, normally `ListSubheader`.\n   */\n  subheader: PropTypes.node,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default List;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "React", "PropTypes", "clsx", "unstable_composeClasses", "composeClasses", "styled", "useThemeProps", "ListContext", "getListUtilityClass", "jsxs", "_jsxs", "jsx", "_jsx", "useUtilityClasses", "ownerState", "classes", "disablePadding", "dense", "subheader", "slots", "root", "ListRoot", "name", "slot", "overridesResolver", "props", "styles", "padding", "listStyle", "margin", "position", "paddingTop", "paddingBottom", "List", "forwardRef", "inProps", "ref", "children", "className", "component", "other", "context", "useMemo", "Provider", "value", "as", "process", "env", "NODE_ENV", "propTypes", "node", "object", "string", "elementType", "bool", "sx", "oneOfType", "arrayOf", "func"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/material/List/List.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"children\", \"className\", \"component\", \"dense\", \"disablePadding\", \"subheader\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { unstable_composeClasses as composeClasses } from '@mui/base/composeClasses';\nimport styled from '../styles/styled';\nimport useThemeProps from '../styles/useThemeProps';\nimport ListContext from './ListContext';\nimport { getListUtilityClass } from './listClasses';\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    disablePadding,\n    dense,\n    subheader\n  } = ownerState;\n  const slots = {\n    root: ['root', !disablePadding && 'padding', dense && 'dense', subheader && 'subheader']\n  };\n  return composeClasses(slots, getListUtilityClass, classes);\n};\nconst ListRoot = styled('ul', {\n  name: 'MuiList',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, !ownerState.disablePadding && styles.padding, ownerState.dense && styles.dense, ownerState.subheader && styles.subheader];\n  }\n})(({\n  ownerState\n}) => _extends({\n  listStyle: 'none',\n  margin: 0,\n  padding: 0,\n  position: 'relative'\n}, !ownerState.disablePadding && {\n  paddingTop: 8,\n  paddingBottom: 8\n}, ownerState.subheader && {\n  paddingTop: 0\n}));\nconst List = /*#__PURE__*/React.forwardRef(function List(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiList'\n  });\n  const {\n      children,\n      className,\n      component = 'ul',\n      dense = false,\n      disablePadding = false,\n      subheader\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const context = React.useMemo(() => ({\n    dense\n  }), [dense]);\n  const ownerState = _extends({}, props, {\n    component,\n    dense,\n    disablePadding\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(ListContext.Provider, {\n    value: context,\n    children: /*#__PURE__*/_jsxs(ListRoot, _extends({\n      as: component,\n      className: clsx(classes.root, className),\n      ref: ref,\n      ownerState: ownerState\n    }, other, {\n      children: [subheader, children]\n    }))\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? List.propTypes /* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit the d.ts file and run \"yarn proptypes\"     |\n  // ----------------------------------------------------------------------\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * If `true`, compact vertical padding designed for keyboard and mouse input is used for\n   * the list and list items.\n   * The prop is available to descendant components as the `dense` context.\n   * @default false\n   */\n  dense: PropTypes.bool,\n  /**\n   * If `true`, vertical padding is removed from the list.\n   * @default false\n   */\n  disablePadding: PropTypes.bool,\n  /**\n   * The content of the subheader, normally `ListSubheader`.\n   */\n  subheader: PropTypes.node,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default List;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,gBAAgB,EAAE,WAAW,CAAC;AAChG,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,uBAAuB,IAAIC,cAAc,QAAQ,0BAA0B;AACpF,OAAOC,MAAM,MAAM,kBAAkB;AACrC,OAAOC,aAAa,MAAM,yBAAyB;AACnD,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,mBAAmB,QAAQ,eAAe;AACnD,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,OAAO;IACPC,cAAc;IACdC,KAAK;IACLC;EACF,CAAC,GAAGJ,UAAU;EACd,MAAMK,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAE,CAACJ,cAAc,IAAI,SAAS,EAAEC,KAAK,IAAI,OAAO,EAAEC,SAAS,IAAI,WAAW;EACzF,CAAC;EACD,OAAOd,cAAc,CAACe,KAAK,EAAEX,mBAAmB,EAAEO,OAAO,CAAC;AAC5D,CAAC;AACD,MAAMM,QAAQ,GAAGhB,MAAM,CAAC,IAAI,EAAE;EAC5BiB,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJZ;IACF,CAAC,GAAGW,KAAK;IACT,OAAO,CAACC,MAAM,CAACN,IAAI,EAAE,CAACN,UAAU,CAACE,cAAc,IAAIU,MAAM,CAACC,OAAO,EAAEb,UAAU,CAACG,KAAK,IAAIS,MAAM,CAACT,KAAK,EAAEH,UAAU,CAACI,SAAS,IAAIQ,MAAM,CAACR,SAAS,CAAC;EAChJ;AACF,CAAC,CAAC,CAAC,CAAC;EACFJ;AACF,CAAC,KAAKhB,QAAQ,CAAC;EACb8B,SAAS,EAAE,MAAM;EACjBC,MAAM,EAAE,CAAC;EACTF,OAAO,EAAE,CAAC;EACVG,QAAQ,EAAE;AACZ,CAAC,EAAE,CAAChB,UAAU,CAACE,cAAc,IAAI;EAC/Be,UAAU,EAAE,CAAC;EACbC,aAAa,EAAE;AACjB,CAAC,EAAElB,UAAU,CAACI,SAAS,IAAI;EACzBa,UAAU,EAAE;AACd,CAAC,CAAC,CAAC;AACH,MAAME,IAAI,GAAG,aAAajC,KAAK,CAACkC,UAAU,CAAC,SAASD,IAAIA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACrE,MAAMX,KAAK,GAAGnB,aAAa,CAAC;IAC1BmB,KAAK,EAAEU,OAAO;IACdb,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFe,QAAQ;MACRC,SAAS;MACTC,SAAS,GAAG,IAAI;MAChBtB,KAAK,GAAG,KAAK;MACbD,cAAc,GAAG,KAAK;MACtBE;IACF,CAAC,GAAGO,KAAK;IACTe,KAAK,GAAG3C,6BAA6B,CAAC4B,KAAK,EAAE1B,SAAS,CAAC;EACzD,MAAM0C,OAAO,GAAGzC,KAAK,CAAC0C,OAAO,CAAC,OAAO;IACnCzB;EACF,CAAC,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC;EACZ,MAAMH,UAAU,GAAGhB,QAAQ,CAAC,CAAC,CAAC,EAAE2B,KAAK,EAAE;IACrCc,SAAS;IACTtB,KAAK;IACLD;EACF,CAAC,CAAC;EACF,MAAMD,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,OAAO,aAAaF,IAAI,CAACL,WAAW,CAACoC,QAAQ,EAAE;IAC7CC,KAAK,EAAEH,OAAO;IACdJ,QAAQ,EAAE,aAAa3B,KAAK,CAACW,QAAQ,EAAEvB,QAAQ,CAAC;MAC9C+C,EAAE,EAAEN,SAAS;MACbD,SAAS,EAAEpC,IAAI,CAACa,OAAO,CAACK,IAAI,EAAEkB,SAAS,CAAC;MACxCF,GAAG,EAAEA,GAAG;MACRtB,UAAU,EAAEA;IACd,CAAC,EAAE0B,KAAK,EAAE;MACRH,QAAQ,EAAE,CAACnB,SAAS,EAAEmB,QAAQ;IAChC,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC;AACFS,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGf,IAAI,CAACgB,SAAS,CAAC,yBAAyB;EAC9E;EACA;EACA;EACA;EACA;AACF;AACA;EACEZ,QAAQ,EAAEpC,SAAS,CAACiD,IAAI;EACxB;AACF;AACA;EACEnC,OAAO,EAAEd,SAAS,CAACkD,MAAM;EACzB;AACF;AACA;EACEb,SAAS,EAAErC,SAAS,CAACmD,MAAM;EAC3B;AACF;AACA;AACA;EACEb,SAAS,EAAEtC,SAAS,CAACoD,WAAW;EAChC;AACF;AACA;AACA;AACA;AACA;EACEpC,KAAK,EAAEhB,SAAS,CAACqD,IAAI;EACrB;AACF;AACA;AACA;EACEtC,cAAc,EAAEf,SAAS,CAACqD,IAAI;EAC9B;AACF;AACA;EACEpC,SAAS,EAAEjB,SAAS,CAACiD,IAAI;EACzB;AACF;AACA;EACEK,EAAE,EAAEtD,SAAS,CAACuD,SAAS,CAAC,CAACvD,SAAS,CAACwD,OAAO,CAACxD,SAAS,CAACuD,SAAS,CAAC,CAACvD,SAAS,CAACyD,IAAI,EAAEzD,SAAS,CAACkD,MAAM,EAAElD,SAAS,CAACqD,IAAI,CAAC,CAAC,CAAC,EAAErD,SAAS,CAACyD,IAAI,EAAEzD,SAAS,CAACkD,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAelB,IAAI"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}