{"ast": null, "code": "'use client';\n\nexport { default } from './useForkRef';", "map": {"version": 3, "names": ["default"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/utils/esm/useForkRef/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './useForkRef';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,cAAc"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}