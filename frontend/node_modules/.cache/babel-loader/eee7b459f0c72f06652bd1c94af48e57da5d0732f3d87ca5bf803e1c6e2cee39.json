{"ast": null, "code": "'use client';\n\nexport { default } from './GlobalStyles';\nexport * from './GlobalStyles';", "map": {"version": 3, "names": ["default"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/system/esm/GlobalStyles/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './GlobalStyles';\nexport * from './GlobalStyles';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,gBAAgB;AACxC,cAAc,gBAAgB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}