{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"children\", \"className\", \"icon\", \"optional\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { unstable_composeClasses as composeClasses } from '@mui/base/composeClasses';\nimport styled from '../styles/styled';\nimport useThemeProps from '../styles/useThemeProps';\nimport ButtonBase from '../ButtonBase';\nimport StepLabel from '../StepLabel';\nimport isMuiElement from '../utils/isMuiElement';\nimport StepperContext from '../Stepper/StepperContext';\nimport StepContext from '../Step/StepContext';\nimport stepButtonClasses, { getStepButtonUtilityClass } from './stepButtonClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    orientation\n  } = ownerState;\n  const slots = {\n    root: ['root', orientation],\n    touchRipple: ['touchRipple']\n  };\n  return composeClasses(slots, getStepButtonUtilityClass, classes);\n};\nconst StepButtonRoot = styled(ButtonBase, {\n  name: 'MuiStepButton',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [{\n      [`& .${stepButtonClasses.touchRipple}`]: styles.touchRipple\n    }, styles.root, styles[ownerState.orientation]];\n  }\n})(({\n  ownerState\n}) => _extends({\n  width: '100%',\n  padding: '24px 16px',\n  margin: '-24px -16px',\n  boxSizing: 'content-box'\n}, ownerState.orientation === 'vertical' && {\n  justifyContent: 'flex-start',\n  padding: '8px',\n  margin: '-8px'\n}, {\n  [`& .${stepButtonClasses.touchRipple}`]: {\n    color: 'rgba(0, 0, 0, 0.3)'\n  }\n}));\nconst StepButton = /*#__PURE__*/React.forwardRef(function StepButton(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiStepButton'\n  });\n  const {\n      children,\n      className,\n      icon,\n      optional\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    disabled,\n    active\n  } = React.useContext(StepContext);\n  const {\n    orientation\n  } = React.useContext(StepperContext);\n  const ownerState = _extends({}, props, {\n    orientation\n  });\n  const classes = useUtilityClasses(ownerState);\n  const childProps = {\n    icon,\n    optional\n  };\n  const child = isMuiElement(children, ['StepLabel']) ? ( /*#__PURE__*/React.cloneElement(children, childProps)) : /*#__PURE__*/_jsx(StepLabel, _extends({}, childProps, {\n    children: children\n  }));\n  return /*#__PURE__*/_jsx(StepButtonRoot, _extends({\n    focusRipple: true,\n    disabled: disabled,\n    TouchRippleProps: {\n      className: classes.touchRipple\n    },\n    className: clsx(classes.root, className),\n    ref: ref,\n    ownerState: ownerState,\n    \"aria-current\": active ? 'step' : undefined\n  }, other, {\n    children: child\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? StepButton.propTypes /* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit the d.ts file and run \"yarn proptypes\"     |\n  // ----------------------------------------------------------------------\n  /**\n   * Can be a `StepLabel` or a node to place inside `StepLabel` as children.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The icon displayed by the step label.\n   */\n  icon: PropTypes.node,\n  /**\n   * The optional node to display.\n   */\n  optional: PropTypes.node,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default StepButton;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "React", "PropTypes", "clsx", "unstable_composeClasses", "composeClasses", "styled", "useThemeProps", "ButtonBase", "<PERSON><PERSON><PERSON><PERSON>", "isMuiElement", "StepperContext", "StepContext", "stepButtonClasses", "getStepButtonUtilityClass", "jsx", "_jsx", "useUtilityClasses", "ownerState", "classes", "orientation", "slots", "root", "touchRipple", "StepButtonRoot", "name", "slot", "overridesResolver", "props", "styles", "width", "padding", "margin", "boxSizing", "justifyContent", "color", "StepButton", "forwardRef", "inProps", "ref", "children", "className", "icon", "optional", "other", "disabled", "active", "useContext", "childProps", "child", "cloneElement", "focusRipple", "TouchRippleProps", "undefined", "process", "env", "NODE_ENV", "propTypes", "node", "object", "string", "sx", "oneOfType", "arrayOf", "func", "bool"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/material/StepButton/StepButton.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"children\", \"className\", \"icon\", \"optional\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { unstable_composeClasses as composeClasses } from '@mui/base/composeClasses';\nimport styled from '../styles/styled';\nimport useThemeProps from '../styles/useThemeProps';\nimport ButtonBase from '../ButtonBase';\nimport StepLabel from '../StepLabel';\nimport isMuiElement from '../utils/isMuiElement';\nimport StepperContext from '../Stepper/StepperContext';\nimport StepContext from '../Step/StepContext';\nimport stepButtonClasses, { getStepButtonUtilityClass } from './stepButtonClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    orientation\n  } = ownerState;\n  const slots = {\n    root: ['root', orientation],\n    touchRipple: ['touchRipple']\n  };\n  return composeClasses(slots, getStepButtonUtilityClass, classes);\n};\nconst StepButtonRoot = styled(ButtonBase, {\n  name: 'MuiStepButton',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [{\n      [`& .${stepButtonClasses.touchRipple}`]: styles.touchRipple\n    }, styles.root, styles[ownerState.orientation]];\n  }\n})(({\n  ownerState\n}) => _extends({\n  width: '100%',\n  padding: '24px 16px',\n  margin: '-24px -16px',\n  boxSizing: 'content-box'\n}, ownerState.orientation === 'vertical' && {\n  justifyContent: 'flex-start',\n  padding: '8px',\n  margin: '-8px'\n}, {\n  [`& .${stepButtonClasses.touchRipple}`]: {\n    color: 'rgba(0, 0, 0, 0.3)'\n  }\n}));\nconst StepButton = /*#__PURE__*/React.forwardRef(function StepButton(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiStepButton'\n  });\n  const {\n      children,\n      className,\n      icon,\n      optional\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    disabled,\n    active\n  } = React.useContext(StepContext);\n  const {\n    orientation\n  } = React.useContext(StepperContext);\n  const ownerState = _extends({}, props, {\n    orientation\n  });\n  const classes = useUtilityClasses(ownerState);\n  const childProps = {\n    icon,\n    optional\n  };\n  const child = isMuiElement(children, ['StepLabel']) ? ( /*#__PURE__*/React.cloneElement(children, childProps)) : /*#__PURE__*/_jsx(StepLabel, _extends({}, childProps, {\n    children: children\n  }));\n  return /*#__PURE__*/_jsx(StepButtonRoot, _extends({\n    focusRipple: true,\n    disabled: disabled,\n    TouchRippleProps: {\n      className: classes.touchRipple\n    },\n    className: clsx(classes.root, className),\n    ref: ref,\n    ownerState: ownerState,\n    \"aria-current\": active ? 'step' : undefined\n  }, other, {\n    children: child\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? StepButton.propTypes /* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit the d.ts file and run \"yarn proptypes\"     |\n  // ----------------------------------------------------------------------\n  /**\n   * Can be a `StepLabel` or a node to place inside `StepLabel` as children.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The icon displayed by the step label.\n   */\n  icon: PropTypes.node,\n  /**\n   * The optional node to display.\n   */\n  optional: PropTypes.node,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default StepButton;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,UAAU,CAAC;AAC/D,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,uBAAuB,IAAIC,cAAc,QAAQ,0BAA0B;AACpF,OAAOC,MAAM,MAAM,kBAAkB;AACrC,OAAOC,aAAa,MAAM,yBAAyB;AACnD,OAAOC,UAAU,MAAM,eAAe;AACtC,OAAOC,SAAS,MAAM,cAAc;AACpC,OAAOC,YAAY,MAAM,uBAAuB;AAChD,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,iBAAiB,IAAIC,yBAAyB,QAAQ,qBAAqB;AAClF,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,OAAO;IACPC;EACF,CAAC,GAAGF,UAAU;EACd,MAAMG,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEF,WAAW,CAAC;IAC3BG,WAAW,EAAE,CAAC,aAAa;EAC7B,CAAC;EACD,OAAOlB,cAAc,CAACgB,KAAK,EAAEP,yBAAyB,EAAEK,OAAO,CAAC;AAClE,CAAC;AACD,MAAMK,cAAc,GAAGlB,MAAM,CAACE,UAAU,EAAE;EACxCiB,IAAI,EAAE,eAAe;EACrBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJX;IACF,CAAC,GAAGU,KAAK;IACT,OAAO,CAAC;MACN,CAAE,MAAKf,iBAAiB,CAACU,WAAY,EAAC,GAAGM,MAAM,CAACN;IAClD,CAAC,EAAEM,MAAM,CAACP,IAAI,EAAEO,MAAM,CAACX,UAAU,CAACE,WAAW,CAAC,CAAC;EACjD;AACF,CAAC,CAAC,CAAC,CAAC;EACFF;AACF,CAAC,KAAKnB,QAAQ,CAAC;EACb+B,KAAK,EAAE,MAAM;EACbC,OAAO,EAAE,WAAW;EACpBC,MAAM,EAAE,aAAa;EACrBC,SAAS,EAAE;AACb,CAAC,EAAEf,UAAU,CAACE,WAAW,KAAK,UAAU,IAAI;EAC1Cc,cAAc,EAAE,YAAY;EAC5BH,OAAO,EAAE,KAAK;EACdC,MAAM,EAAE;AACV,CAAC,EAAE;EACD,CAAE,MAAKnB,iBAAiB,CAACU,WAAY,EAAC,GAAG;IACvCY,KAAK,EAAE;EACT;AACF,CAAC,CAAC,CAAC;AACH,MAAMC,UAAU,GAAG,aAAanC,KAAK,CAACoC,UAAU,CAAC,SAASD,UAAUA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACjF,MAAMX,KAAK,GAAGrB,aAAa,CAAC;IAC1BqB,KAAK,EAAEU,OAAO;IACdb,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFe,QAAQ;MACRC,SAAS;MACTC,IAAI;MACJC;IACF,CAAC,GAAGf,KAAK;IACTgB,KAAK,GAAG9C,6BAA6B,CAAC8B,KAAK,EAAE5B,SAAS,CAAC;EACzD,MAAM;IACJ6C,QAAQ;IACRC;EACF,CAAC,GAAG7C,KAAK,CAAC8C,UAAU,CAACnC,WAAW,CAAC;EACjC,MAAM;IACJQ;EACF,CAAC,GAAGnB,KAAK,CAAC8C,UAAU,CAACpC,cAAc,CAAC;EACpC,MAAMO,UAAU,GAAGnB,QAAQ,CAAC,CAAC,CAAC,EAAE6B,KAAK,EAAE;IACrCR;EACF,CAAC,CAAC;EACF,MAAMD,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAM8B,UAAU,GAAG;IACjBN,IAAI;IACJC;EACF,CAAC;EACD,MAAMM,KAAK,GAAGvC,YAAY,CAAC8B,QAAQ,EAAE,CAAC,WAAW,CAAC,CAAC,KAAK,aAAavC,KAAK,CAACiD,YAAY,CAACV,QAAQ,EAAEQ,UAAU,CAAC,IAAI,aAAahC,IAAI,CAACP,SAAS,EAAEV,QAAQ,CAAC,CAAC,CAAC,EAAEiD,UAAU,EAAE;IACrKR,QAAQ,EAAEA;EACZ,CAAC,CAAC,CAAC;EACH,OAAO,aAAaxB,IAAI,CAACQ,cAAc,EAAEzB,QAAQ,CAAC;IAChDoD,WAAW,EAAE,IAAI;IACjBN,QAAQ,EAAEA,QAAQ;IAClBO,gBAAgB,EAAE;MAChBX,SAAS,EAAEtB,OAAO,CAACI;IACrB,CAAC;IACDkB,SAAS,EAAEtC,IAAI,CAACgB,OAAO,CAACG,IAAI,EAAEmB,SAAS,CAAC;IACxCF,GAAG,EAAEA,GAAG;IACRrB,UAAU,EAAEA,UAAU;IACtB,cAAc,EAAE4B,MAAM,GAAG,MAAM,GAAGO;EACpC,CAAC,EAAET,KAAK,EAAE;IACRJ,QAAQ,EAAES;EACZ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFK,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGpB,UAAU,CAACqB,SAAS,CAAC,yBAAyB;EACpF;EACA;EACA;EACA;EACA;AACF;AACA;EACEjB,QAAQ,EAAEtC,SAAS,CAACwD,IAAI;EACxB;AACF;AACA;EACEvC,OAAO,EAAEjB,SAAS,CAACyD,MAAM;EACzB;AACF;AACA;EACElB,SAAS,EAAEvC,SAAS,CAAC0D,MAAM;EAC3B;AACF;AACA;EACElB,IAAI,EAAExC,SAAS,CAACwD,IAAI;EACpB;AACF;AACA;EACEf,QAAQ,EAAEzC,SAAS,CAACwD,IAAI;EACxB;AACF;AACA;EACEG,EAAE,EAAE3D,SAAS,CAAC4D,SAAS,CAAC,CAAC5D,SAAS,CAAC6D,OAAO,CAAC7D,SAAS,CAAC4D,SAAS,CAAC,CAAC5D,SAAS,CAAC8D,IAAI,EAAE9D,SAAS,CAACyD,MAAM,EAAEzD,SAAS,CAAC+D,IAAI,CAAC,CAAC,CAAC,EAAE/D,SAAS,CAAC8D,IAAI,EAAE9D,SAAS,CAACyD,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAevB,UAAU"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}