{"ast": null, "code": "import { unstable_generateUtilityClasses as generateUtilityClasses } from '@mui/utils';\nimport generateUtilityClass from '../generateUtilityClass';\nexport function getBottomNavigationActionUtilityClass(slot) {\n  return generateUtilityClass('MuiBottomNavigationAction', slot);\n}\nconst bottomNavigationActionClasses = generateUtilityClasses('MuiBottomNavigationAction', ['root', 'iconOnly', 'selected', 'label']);\nexport default bottomNavigationActionClasses;", "map": {"version": 3, "names": ["unstable_generateUtilityClasses", "generateUtilityClasses", "generateUtilityClass", "getBottomNavigationActionUtilityClass", "slot", "bottomNavigationActionClasses"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/material/BottomNavigationAction/bottomNavigationActionClasses.js"], "sourcesContent": ["import { unstable_generateUtilityClasses as generateUtilityClasses } from '@mui/utils';\nimport generateUtilityClass from '../generateUtilityClass';\nexport function getBottomNavigationActionUtilityClass(slot) {\n  return generateUtilityClass('MuiBottomNavigationAction', slot);\n}\nconst bottomNavigationActionClasses = generateUtilityClasses('MuiBottomNavigationAction', ['root', 'iconOnly', 'selected', 'label']);\nexport default bottomNavigationActionClasses;"], "mappings": "AAAA,SAASA,+BAA+B,IAAIC,sBAAsB,QAAQ,YAAY;AACtF,OAAOC,oBAAoB,MAAM,yBAAyB;AAC1D,OAAO,SAASC,qCAAqCA,CAACC,IAAI,EAAE;EAC1D,OAAOF,oBAAoB,CAAC,2BAA2B,EAAEE,IAAI,CAAC;AAChE;AACA,MAAMC,6BAA6B,GAAGJ,sBAAsB,CAAC,2BAA2B,EAAE,CAAC,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;AACpI,eAAeI,6BAA6B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}