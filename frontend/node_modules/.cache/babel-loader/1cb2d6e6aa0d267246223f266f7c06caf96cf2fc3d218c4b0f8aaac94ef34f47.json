{"ast": null, "code": "'use client';\n\nimport { unstable_useControlled as useControlled } from '@mui/utils';\nexport default useControlled;", "map": {"version": 3, "names": ["unstable_useControlled", "useControlled"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/material/utils/useControlled.js"], "sourcesContent": ["'use client';\n\nimport { unstable_useControlled as useControlled } from '@mui/utils';\nexport default useControlled;"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,sBAAsB,IAAIC,aAAa,QAAQ,YAAY;AACpE,eAAeA,aAAa"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}