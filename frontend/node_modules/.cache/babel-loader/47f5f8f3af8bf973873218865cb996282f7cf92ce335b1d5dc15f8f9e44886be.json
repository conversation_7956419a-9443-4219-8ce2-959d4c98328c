{"ast": null, "code": "import * as React from 'react';\nimport { useContext, forwardRef } from 'react';\nimport createCache from '@emotion/cache';\nimport _extends from '@babel/runtime/helpers/esm/extends';\nimport weakMemoize from '@emotion/weak-memoize';\nimport hoistNonReactStatics from '../_isolated-hnrs/dist/emotion-react-_isolated-hnrs.browser.esm.js';\nimport { getRegisteredStyles, registerStyles, insertStyles } from '@emotion/utils';\nimport { serializeStyles } from '@emotion/serialize';\nimport { useInsertionEffectAlwaysWithSyncFallback } from '@emotion/use-insertion-effect-with-fallbacks';\nvar isBrowser = \"object\" !== 'undefined';\nvar hasOwnProperty = {}.hasOwnProperty;\nvar EmotionCacheContext = /* #__PURE__ */React.createContext(\n// we're doing this to avoid preconstruct's dead code elimination in this one case\n// because this module is primarily intended for the browser and node\n// but it's also required in react native and similar environments sometimes\n// and we could have a special build just for that\n// but this is much easier and the native packages\n// might use a different theme context in the future anyway\ntypeof HTMLElement !== 'undefined' ? /* #__PURE__ */createCache({\n  key: 'css'\n}) : null);\nif (process.env.NODE_ENV !== 'production') {\n  EmotionCacheContext.displayName = 'EmotionCacheContext';\n}\nvar CacheProvider = EmotionCacheContext.Provider;\nvar __unsafe_useEmotionCache = function useEmotionCache() {\n  return useContext(EmotionCacheContext);\n};\nvar withEmotionCache = function withEmotionCache(func) {\n  // $FlowFixMe\n  return /*#__PURE__*/forwardRef(function (props, ref) {\n    // the cache will never be null in the browser\n    var cache = useContext(EmotionCacheContext);\n    return func(props, cache, ref);\n  });\n};\nif (!isBrowser) {\n  withEmotionCache = function withEmotionCache(func) {\n    return function (props) {\n      var cache = useContext(EmotionCacheContext);\n      if (cache === null) {\n        // yes, we're potentially creating this on every render\n        // it doesn't actually matter though since it's only on the server\n        // so there will only every be a single render\n        // that could change in the future because of suspense and etc. but for now,\n        // this works and i don't want to optimise for a future thing that we aren't sure about\n        cache = createCache({\n          key: 'css'\n        });\n        return /*#__PURE__*/React.createElement(EmotionCacheContext.Provider, {\n          value: cache\n        }, func(props, cache));\n      } else {\n        return func(props, cache);\n      }\n    };\n  };\n}\nvar ThemeContext = /* #__PURE__ */React.createContext({});\nif (process.env.NODE_ENV !== 'production') {\n  ThemeContext.displayName = 'EmotionThemeContext';\n}\nvar useTheme = function useTheme() {\n  return React.useContext(ThemeContext);\n};\nvar getTheme = function getTheme(outerTheme, theme) {\n  if (typeof theme === 'function') {\n    var mergedTheme = theme(outerTheme);\n    if (process.env.NODE_ENV !== 'production' && (mergedTheme == null || typeof mergedTheme !== 'object' || Array.isArray(mergedTheme))) {\n      throw new Error('[ThemeProvider] Please return an object from your theme function, i.e. theme={() => ({})}!');\n    }\n    return mergedTheme;\n  }\n  if (process.env.NODE_ENV !== 'production' && (theme == null || typeof theme !== 'object' || Array.isArray(theme))) {\n    throw new Error('[ThemeProvider] Please make your theme prop a plain object');\n  }\n  return _extends({}, outerTheme, theme);\n};\nvar createCacheWithTheme = /* #__PURE__ */weakMemoize(function (outerTheme) {\n  return weakMemoize(function (theme) {\n    return getTheme(outerTheme, theme);\n  });\n});\nvar ThemeProvider = function ThemeProvider(props) {\n  var theme = React.useContext(ThemeContext);\n  if (props.theme !== theme) {\n    theme = createCacheWithTheme(theme)(props.theme);\n  }\n  return /*#__PURE__*/React.createElement(ThemeContext.Provider, {\n    value: theme\n  }, props.children);\n};\nfunction withTheme(Component) {\n  var componentName = Component.displayName || Component.name || 'Component';\n  var render = function render(props, ref) {\n    var theme = React.useContext(ThemeContext);\n    return /*#__PURE__*/React.createElement(Component, _extends({\n      theme: theme,\n      ref: ref\n    }, props));\n  }; // $FlowFixMe\n\n  var WithTheme = /*#__PURE__*/React.forwardRef(render);\n  WithTheme.displayName = \"WithTheme(\" + componentName + \")\";\n  return hoistNonReactStatics(WithTheme, Component);\n}\nvar getLastPart = function getLastPart(functionName) {\n  // The match may be something like 'Object.createEmotionProps' or\n  // 'Loader.prototype.render'\n  var parts = functionName.split('.');\n  return parts[parts.length - 1];\n};\nvar getFunctionNameFromStackTraceLine = function getFunctionNameFromStackTraceLine(line) {\n  // V8\n  var match = /^\\s+at\\s+([A-Za-z0-9$.]+)\\s/.exec(line);\n  if (match) return getLastPart(match[1]); // Safari / Firefox\n\n  match = /^([A-Za-z0-9$.]+)@/.exec(line);\n  if (match) return getLastPart(match[1]);\n  return undefined;\n};\nvar internalReactFunctionNames = /* #__PURE__ */new Set(['renderWithHooks', 'processChild', 'finishClassComponent', 'renderToString']); // These identifiers come from error stacks, so they have to be valid JS\n// identifiers, thus we only need to replace what is a valid character for JS,\n// but not for CSS.\n\nvar sanitizeIdentifier = function sanitizeIdentifier(identifier) {\n  return identifier.replace(/\\$/g, '-');\n};\nvar getLabelFromStackTrace = function getLabelFromStackTrace(stackTrace) {\n  if (!stackTrace) return undefined;\n  var lines = stackTrace.split('\\n');\n  for (var i = 0; i < lines.length; i++) {\n    var functionName = getFunctionNameFromStackTraceLine(lines[i]); // The first line of V8 stack traces is just \"Error\"\n\n    if (!functionName) continue; // If we reach one of these, we have gone too far and should quit\n\n    if (internalReactFunctionNames.has(functionName)) break; // The component name is the first function in the stack that starts with an\n    // uppercase letter\n\n    if (/^[A-Z]/.test(functionName)) return sanitizeIdentifier(functionName);\n  }\n  return undefined;\n};\nvar typePropName = '__EMOTION_TYPE_PLEASE_DO_NOT_USE__';\nvar labelPropName = '__EMOTION_LABEL_PLEASE_DO_NOT_USE__';\nvar createEmotionProps = function createEmotionProps(type, props) {\n  if (process.env.NODE_ENV !== 'production' && typeof props.css === 'string' &&\n  // check if there is a css declaration\n  props.css.indexOf(':') !== -1) {\n    throw new Error(\"Strings are not allowed as css prop values, please wrap it in a css template literal from '@emotion/react' like this: css`\" + props.css + \"`\");\n  }\n  var newProps = {};\n  for (var key in props) {\n    if (hasOwnProperty.call(props, key)) {\n      newProps[key] = props[key];\n    }\n  }\n  newProps[typePropName] = type; // For performance, only call getLabelFromStackTrace in development and when\n  // the label hasn't already been computed\n\n  if (process.env.NODE_ENV !== 'production' && !!props.css && (typeof props.css !== 'object' || typeof props.css.name !== 'string' || props.css.name.indexOf('-') === -1)) {\n    var label = getLabelFromStackTrace(new Error().stack);\n    if (label) newProps[labelPropName] = label;\n  }\n  return newProps;\n};\nvar Insertion = function Insertion(_ref) {\n  var cache = _ref.cache,\n    serialized = _ref.serialized,\n    isStringTag = _ref.isStringTag;\n  registerStyles(cache, serialized, isStringTag);\n  useInsertionEffectAlwaysWithSyncFallback(function () {\n    return insertStyles(cache, serialized, isStringTag);\n  });\n  return null;\n};\nvar Emotion = /* #__PURE__ */withEmotionCache(function (props, cache, ref) {\n  var cssProp = props.css; // so that using `css` from `emotion` and passing the result to the css prop works\n  // not passing the registered cache to serializeStyles because it would\n  // make certain babel optimisations not possible\n\n  if (typeof cssProp === 'string' && cache.registered[cssProp] !== undefined) {\n    cssProp = cache.registered[cssProp];\n  }\n  var WrappedComponent = props[typePropName];\n  var registeredStyles = [cssProp];\n  var className = '';\n  if (typeof props.className === 'string') {\n    className = getRegisteredStyles(cache.registered, registeredStyles, props.className);\n  } else if (props.className != null) {\n    className = props.className + \" \";\n  }\n  var serialized = serializeStyles(registeredStyles, undefined, React.useContext(ThemeContext));\n  if (process.env.NODE_ENV !== 'production' && serialized.name.indexOf('-') === -1) {\n    var labelFromStack = props[labelPropName];\n    if (labelFromStack) {\n      serialized = serializeStyles([serialized, 'label:' + labelFromStack + ';']);\n    }\n  }\n  className += cache.key + \"-\" + serialized.name;\n  var newProps = {};\n  for (var key in props) {\n    if (hasOwnProperty.call(props, key) && key !== 'css' && key !== typePropName && (process.env.NODE_ENV === 'production' || key !== labelPropName)) {\n      newProps[key] = props[key];\n    }\n  }\n  newProps.ref = ref;\n  newProps.className = className;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(Insertion, {\n    cache: cache,\n    serialized: serialized,\n    isStringTag: typeof WrappedComponent === 'string'\n  }), /*#__PURE__*/React.createElement(WrappedComponent, newProps));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Emotion.displayName = 'EmotionCssPropInternal';\n}\nvar Emotion$1 = Emotion;\nexport { CacheProvider as C, Emotion$1 as E, ThemeContext as T, __unsafe_useEmotionCache as _, ThemeProvider as a, withTheme as b, createEmotionProps as c, hasOwnProperty as h, isBrowser as i, useTheme as u, withEmotionCache as w };", "map": {"version": 3, "names": ["React", "useContext", "forwardRef", "createCache", "_extends", "weakMemoize", "hoistNonReactStatics", "getRegisteredStyles", "registerStyles", "insertStyles", "serializeStyles", "useInsertionEffectAlwaysWithSyncFallback", "<PERSON><PERSON><PERSON><PERSON>", "hasOwnProperty", "EmotionCacheContext", "createContext", "HTMLElement", "key", "process", "env", "NODE_ENV", "displayName", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Provider", "__unsafe_useEmotionCache", "useEmotionCache", "withEmotionCache", "func", "props", "ref", "cache", "createElement", "value", "ThemeContext", "useTheme", "getTheme", "outerTheme", "theme", "mergedTheme", "Array", "isArray", "Error", "createCacheWithTheme", "ThemeProvider", "children", "withTheme", "Component", "componentName", "name", "render", "WithTheme", "getLastPart", "functionName", "parts", "split", "length", "getFunctionNameFromStackTraceLine", "line", "match", "exec", "undefined", "internalReactFunctionNames", "Set", "sanitizeIdentifier", "identifier", "replace", "getLabelFromStackTrace", "stackTrace", "lines", "i", "has", "test", "typePropName", "labelPropName", "createEmotionProps", "type", "css", "indexOf", "newProps", "call", "label", "stack", "Insertion", "_ref", "serialized", "isStringTag", "Emotion", "cssProp", "registered", "WrappedComponent", "registeredStyles", "className", "labelFromStack", "Fragment", "Emotion$1", "C", "E", "T", "_", "a", "b", "c", "h", "u", "w"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@emotion/react/dist/emotion-element-c39617d8.browser.esm.js"], "sourcesContent": ["import * as React from 'react';\nimport { useContext, forwardRef } from 'react';\nimport createCache from '@emotion/cache';\nimport _extends from '@babel/runtime/helpers/esm/extends';\nimport weakMemoize from '@emotion/weak-memoize';\nimport hoistNonReactStatics from '../_isolated-hnrs/dist/emotion-react-_isolated-hnrs.browser.esm.js';\nimport { getRegisteredStyles, registerStyles, insertStyles } from '@emotion/utils';\nimport { serializeStyles } from '@emotion/serialize';\nimport { useInsertionEffectAlwaysWithSyncFallback } from '@emotion/use-insertion-effect-with-fallbacks';\n\nvar isBrowser = \"object\" !== 'undefined';\nvar hasOwnProperty = {}.hasOwnProperty;\n\nvar EmotionCacheContext = /* #__PURE__ */React.createContext( // we're doing this to avoid preconstruct's dead code elimination in this one case\n// because this module is primarily intended for the browser and node\n// but it's also required in react native and similar environments sometimes\n// and we could have a special build just for that\n// but this is much easier and the native packages\n// might use a different theme context in the future anyway\ntypeof HTMLElement !== 'undefined' ? /* #__PURE__ */createCache({\n  key: 'css'\n}) : null);\n\nif (process.env.NODE_ENV !== 'production') {\n  EmotionCacheContext.displayName = 'EmotionCacheContext';\n}\n\nvar CacheProvider = EmotionCacheContext.Provider;\nvar __unsafe_useEmotionCache = function useEmotionCache() {\n  return useContext(EmotionCacheContext);\n};\n\nvar withEmotionCache = function withEmotionCache(func) {\n  // $FlowFixMe\n  return /*#__PURE__*/forwardRef(function (props, ref) {\n    // the cache will never be null in the browser\n    var cache = useContext(EmotionCacheContext);\n    return func(props, cache, ref);\n  });\n};\n\nif (!isBrowser) {\n  withEmotionCache = function withEmotionCache(func) {\n    return function (props) {\n      var cache = useContext(EmotionCacheContext);\n\n      if (cache === null) {\n        // yes, we're potentially creating this on every render\n        // it doesn't actually matter though since it's only on the server\n        // so there will only every be a single render\n        // that could change in the future because of suspense and etc. but for now,\n        // this works and i don't want to optimise for a future thing that we aren't sure about\n        cache = createCache({\n          key: 'css'\n        });\n        return /*#__PURE__*/React.createElement(EmotionCacheContext.Provider, {\n          value: cache\n        }, func(props, cache));\n      } else {\n        return func(props, cache);\n      }\n    };\n  };\n}\n\nvar ThemeContext = /* #__PURE__ */React.createContext({});\n\nif (process.env.NODE_ENV !== 'production') {\n  ThemeContext.displayName = 'EmotionThemeContext';\n}\n\nvar useTheme = function useTheme() {\n  return React.useContext(ThemeContext);\n};\n\nvar getTheme = function getTheme(outerTheme, theme) {\n  if (typeof theme === 'function') {\n    var mergedTheme = theme(outerTheme);\n\n    if (process.env.NODE_ENV !== 'production' && (mergedTheme == null || typeof mergedTheme !== 'object' || Array.isArray(mergedTheme))) {\n      throw new Error('[ThemeProvider] Please return an object from your theme function, i.e. theme={() => ({})}!');\n    }\n\n    return mergedTheme;\n  }\n\n  if (process.env.NODE_ENV !== 'production' && (theme == null || typeof theme !== 'object' || Array.isArray(theme))) {\n    throw new Error('[ThemeProvider] Please make your theme prop a plain object');\n  }\n\n  return _extends({}, outerTheme, theme);\n};\n\nvar createCacheWithTheme = /* #__PURE__ */weakMemoize(function (outerTheme) {\n  return weakMemoize(function (theme) {\n    return getTheme(outerTheme, theme);\n  });\n});\nvar ThemeProvider = function ThemeProvider(props) {\n  var theme = React.useContext(ThemeContext);\n\n  if (props.theme !== theme) {\n    theme = createCacheWithTheme(theme)(props.theme);\n  }\n\n  return /*#__PURE__*/React.createElement(ThemeContext.Provider, {\n    value: theme\n  }, props.children);\n};\nfunction withTheme(Component) {\n  var componentName = Component.displayName || Component.name || 'Component';\n\n  var render = function render(props, ref) {\n    var theme = React.useContext(ThemeContext);\n    return /*#__PURE__*/React.createElement(Component, _extends({\n      theme: theme,\n      ref: ref\n    }, props));\n  }; // $FlowFixMe\n\n\n  var WithTheme = /*#__PURE__*/React.forwardRef(render);\n  WithTheme.displayName = \"WithTheme(\" + componentName + \")\";\n  return hoistNonReactStatics(WithTheme, Component);\n}\n\nvar getLastPart = function getLastPart(functionName) {\n  // The match may be something like 'Object.createEmotionProps' or\n  // 'Loader.prototype.render'\n  var parts = functionName.split('.');\n  return parts[parts.length - 1];\n};\n\nvar getFunctionNameFromStackTraceLine = function getFunctionNameFromStackTraceLine(line) {\n  // V8\n  var match = /^\\s+at\\s+([A-Za-z0-9$.]+)\\s/.exec(line);\n  if (match) return getLastPart(match[1]); // Safari / Firefox\n\n  match = /^([A-Za-z0-9$.]+)@/.exec(line);\n  if (match) return getLastPart(match[1]);\n  return undefined;\n};\n\nvar internalReactFunctionNames = /* #__PURE__ */new Set(['renderWithHooks', 'processChild', 'finishClassComponent', 'renderToString']); // These identifiers come from error stacks, so they have to be valid JS\n// identifiers, thus we only need to replace what is a valid character for JS,\n// but not for CSS.\n\nvar sanitizeIdentifier = function sanitizeIdentifier(identifier) {\n  return identifier.replace(/\\$/g, '-');\n};\n\nvar getLabelFromStackTrace = function getLabelFromStackTrace(stackTrace) {\n  if (!stackTrace) return undefined;\n  var lines = stackTrace.split('\\n');\n\n  for (var i = 0; i < lines.length; i++) {\n    var functionName = getFunctionNameFromStackTraceLine(lines[i]); // The first line of V8 stack traces is just \"Error\"\n\n    if (!functionName) continue; // If we reach one of these, we have gone too far and should quit\n\n    if (internalReactFunctionNames.has(functionName)) break; // The component name is the first function in the stack that starts with an\n    // uppercase letter\n\n    if (/^[A-Z]/.test(functionName)) return sanitizeIdentifier(functionName);\n  }\n\n  return undefined;\n};\n\nvar typePropName = '__EMOTION_TYPE_PLEASE_DO_NOT_USE__';\nvar labelPropName = '__EMOTION_LABEL_PLEASE_DO_NOT_USE__';\nvar createEmotionProps = function createEmotionProps(type, props) {\n  if (process.env.NODE_ENV !== 'production' && typeof props.css === 'string' && // check if there is a css declaration\n  props.css.indexOf(':') !== -1) {\n    throw new Error(\"Strings are not allowed as css prop values, please wrap it in a css template literal from '@emotion/react' like this: css`\" + props.css + \"`\");\n  }\n\n  var newProps = {};\n\n  for (var key in props) {\n    if (hasOwnProperty.call(props, key)) {\n      newProps[key] = props[key];\n    }\n  }\n\n  newProps[typePropName] = type; // For performance, only call getLabelFromStackTrace in development and when\n  // the label hasn't already been computed\n\n  if (process.env.NODE_ENV !== 'production' && !!props.css && (typeof props.css !== 'object' || typeof props.css.name !== 'string' || props.css.name.indexOf('-') === -1)) {\n    var label = getLabelFromStackTrace(new Error().stack);\n    if (label) newProps[labelPropName] = label;\n  }\n\n  return newProps;\n};\n\nvar Insertion = function Insertion(_ref) {\n  var cache = _ref.cache,\n      serialized = _ref.serialized,\n      isStringTag = _ref.isStringTag;\n  registerStyles(cache, serialized, isStringTag);\n  useInsertionEffectAlwaysWithSyncFallback(function () {\n    return insertStyles(cache, serialized, isStringTag);\n  });\n\n  return null;\n};\n\nvar Emotion = /* #__PURE__ */withEmotionCache(function (props, cache, ref) {\n  var cssProp = props.css; // so that using `css` from `emotion` and passing the result to the css prop works\n  // not passing the registered cache to serializeStyles because it would\n  // make certain babel optimisations not possible\n\n  if (typeof cssProp === 'string' && cache.registered[cssProp] !== undefined) {\n    cssProp = cache.registered[cssProp];\n  }\n\n  var WrappedComponent = props[typePropName];\n  var registeredStyles = [cssProp];\n  var className = '';\n\n  if (typeof props.className === 'string') {\n    className = getRegisteredStyles(cache.registered, registeredStyles, props.className);\n  } else if (props.className != null) {\n    className = props.className + \" \";\n  }\n\n  var serialized = serializeStyles(registeredStyles, undefined, React.useContext(ThemeContext));\n\n  if (process.env.NODE_ENV !== 'production' && serialized.name.indexOf('-') === -1) {\n    var labelFromStack = props[labelPropName];\n\n    if (labelFromStack) {\n      serialized = serializeStyles([serialized, 'label:' + labelFromStack + ';']);\n    }\n  }\n\n  className += cache.key + \"-\" + serialized.name;\n  var newProps = {};\n\n  for (var key in props) {\n    if (hasOwnProperty.call(props, key) && key !== 'css' && key !== typePropName && (process.env.NODE_ENV === 'production' || key !== labelPropName)) {\n      newProps[key] = props[key];\n    }\n  }\n\n  newProps.ref = ref;\n  newProps.className = className;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(Insertion, {\n    cache: cache,\n    serialized: serialized,\n    isStringTag: typeof WrappedComponent === 'string'\n  }), /*#__PURE__*/React.createElement(WrappedComponent, newProps));\n});\n\nif (process.env.NODE_ENV !== 'production') {\n  Emotion.displayName = 'EmotionCssPropInternal';\n}\n\nvar Emotion$1 = Emotion;\n\nexport { CacheProvider as C, Emotion$1 as E, ThemeContext as T, __unsafe_useEmotionCache as _, ThemeProvider as a, withTheme as b, createEmotionProps as c, hasOwnProperty as h, isBrowser as i, useTheme as u, withEmotionCache as w };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,EAAEC,UAAU,QAAQ,OAAO;AAC9C,OAAOC,WAAW,MAAM,gBAAgB;AACxC,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,WAAW,MAAM,uBAAuB;AAC/C,OAAOC,oBAAoB,MAAM,oEAAoE;AACrG,SAASC,mBAAmB,EAAEC,cAAc,EAAEC,YAAY,QAAQ,gBAAgB;AAClF,SAASC,eAAe,QAAQ,oBAAoB;AACpD,SAASC,wCAAwC,QAAQ,8CAA8C;AAEvG,IAAIC,SAAS,GAAG,QAAQ,KAAK,WAAW;AACxC,IAAIC,cAAc,GAAG,CAAC,CAAC,CAACA,cAAc;AAEtC,IAAIC,mBAAmB,GAAG,eAAed,KAAK,CAACe,aAAa;AAAE;AAC9D;AACA;AACA;AACA;AACA;AACA,OAAOC,WAAW,KAAK,WAAW,GAAG,eAAeb,WAAW,CAAC;EAC9Dc,GAAG,EAAE;AACP,CAAC,CAAC,GAAG,IAAI,CAAC;AAEV,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCN,mBAAmB,CAACO,WAAW,GAAG,qBAAqB;AACzD;AAEA,IAAIC,aAAa,GAAGR,mBAAmB,CAACS,QAAQ;AAChD,IAAIC,wBAAwB,GAAG,SAASC,eAAeA,CAAA,EAAG;EACxD,OAAOxB,UAAU,CAACa,mBAAmB,CAAC;AACxC,CAAC;AAED,IAAIY,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,IAAI,EAAE;EACrD;EACA,OAAO,aAAazB,UAAU,CAAC,UAAU0B,KAAK,EAAEC,GAAG,EAAE;IACnD;IACA,IAAIC,KAAK,GAAG7B,UAAU,CAACa,mBAAmB,CAAC;IAC3C,OAAOa,IAAI,CAACC,KAAK,EAAEE,KAAK,EAAED,GAAG,CAAC;EAChC,CAAC,CAAC;AACJ,CAAC;AAED,IAAI,CAACjB,SAAS,EAAE;EACdc,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,IAAI,EAAE;IACjD,OAAO,UAAUC,KAAK,EAAE;MACtB,IAAIE,KAAK,GAAG7B,UAAU,CAACa,mBAAmB,CAAC;MAE3C,IAAIgB,KAAK,KAAK,IAAI,EAAE;QAClB;QACA;QACA;QACA;QACA;QACAA,KAAK,GAAG3B,WAAW,CAAC;UAClBc,GAAG,EAAE;QACP,CAAC,CAAC;QACF,OAAO,aAAajB,KAAK,CAAC+B,aAAa,CAACjB,mBAAmB,CAACS,QAAQ,EAAE;UACpES,KAAK,EAAEF;QACT,CAAC,EAAEH,IAAI,CAACC,KAAK,EAAEE,KAAK,CAAC,CAAC;MACxB,CAAC,MAAM;QACL,OAAOH,IAAI,CAACC,KAAK,EAAEE,KAAK,CAAC;MAC3B;IACF,CAAC;EACH,CAAC;AACH;AAEA,IAAIG,YAAY,GAAG,eAAejC,KAAK,CAACe,aAAa,CAAC,CAAC,CAAC,CAAC;AAEzD,IAAIG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCa,YAAY,CAACZ,WAAW,GAAG,qBAAqB;AAClD;AAEA,IAAIa,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAG;EACjC,OAAOlC,KAAK,CAACC,UAAU,CAACgC,YAAY,CAAC;AACvC,CAAC;AAED,IAAIE,QAAQ,GAAG,SAASA,QAAQA,CAACC,UAAU,EAAEC,KAAK,EAAE;EAClD,IAAI,OAAOA,KAAK,KAAK,UAAU,EAAE;IAC/B,IAAIC,WAAW,GAAGD,KAAK,CAACD,UAAU,CAAC;IAEnC,IAAIlB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,KAAKkB,WAAW,IAAI,IAAI,IAAI,OAAOA,WAAW,KAAK,QAAQ,IAAIC,KAAK,CAACC,OAAO,CAACF,WAAW,CAAC,CAAC,EAAE;MACnI,MAAM,IAAIG,KAAK,CAAC,4FAA4F,CAAC;IAC/G;IAEA,OAAOH,WAAW;EACpB;EAEA,IAAIpB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,KAAKiB,KAAK,IAAI,IAAI,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIE,KAAK,CAACC,OAAO,CAACH,KAAK,CAAC,CAAC,EAAE;IACjH,MAAM,IAAII,KAAK,CAAC,4DAA4D,CAAC;EAC/E;EAEA,OAAOrC,QAAQ,CAAC,CAAC,CAAC,EAAEgC,UAAU,EAAEC,KAAK,CAAC;AACxC,CAAC;AAED,IAAIK,oBAAoB,GAAG,eAAerC,WAAW,CAAC,UAAU+B,UAAU,EAAE;EAC1E,OAAO/B,WAAW,CAAC,UAAUgC,KAAK,EAAE;IAClC,OAAOF,QAAQ,CAACC,UAAU,EAAEC,KAAK,CAAC;EACpC,CAAC,CAAC;AACJ,CAAC,CAAC;AACF,IAAIM,aAAa,GAAG,SAASA,aAAaA,CAACf,KAAK,EAAE;EAChD,IAAIS,KAAK,GAAGrC,KAAK,CAACC,UAAU,CAACgC,YAAY,CAAC;EAE1C,IAAIL,KAAK,CAACS,KAAK,KAAKA,KAAK,EAAE;IACzBA,KAAK,GAAGK,oBAAoB,CAACL,KAAK,CAAC,CAACT,KAAK,CAACS,KAAK,CAAC;EAClD;EAEA,OAAO,aAAarC,KAAK,CAAC+B,aAAa,CAACE,YAAY,CAACV,QAAQ,EAAE;IAC7DS,KAAK,EAAEK;EACT,CAAC,EAAET,KAAK,CAACgB,QAAQ,CAAC;AACpB,CAAC;AACD,SAASC,SAASA,CAACC,SAAS,EAAE;EAC5B,IAAIC,aAAa,GAAGD,SAAS,CAACzB,WAAW,IAAIyB,SAAS,CAACE,IAAI,IAAI,WAAW;EAE1E,IAAIC,MAAM,GAAG,SAASA,MAAMA,CAACrB,KAAK,EAAEC,GAAG,EAAE;IACvC,IAAIQ,KAAK,GAAGrC,KAAK,CAACC,UAAU,CAACgC,YAAY,CAAC;IAC1C,OAAO,aAAajC,KAAK,CAAC+B,aAAa,CAACe,SAAS,EAAE1C,QAAQ,CAAC;MAC1DiC,KAAK,EAAEA,KAAK;MACZR,GAAG,EAAEA;IACP,CAAC,EAAED,KAAK,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC;;EAGH,IAAIsB,SAAS,GAAG,aAAalD,KAAK,CAACE,UAAU,CAAC+C,MAAM,CAAC;EACrDC,SAAS,CAAC7B,WAAW,GAAG,YAAY,GAAG0B,aAAa,GAAG,GAAG;EAC1D,OAAOzC,oBAAoB,CAAC4C,SAAS,EAAEJ,SAAS,CAAC;AACnD;AAEA,IAAIK,WAAW,GAAG,SAASA,WAAWA,CAACC,YAAY,EAAE;EACnD;EACA;EACA,IAAIC,KAAK,GAAGD,YAAY,CAACE,KAAK,CAAC,GAAG,CAAC;EACnC,OAAOD,KAAK,CAACA,KAAK,CAACE,MAAM,GAAG,CAAC,CAAC;AAChC,CAAC;AAED,IAAIC,iCAAiC,GAAG,SAASA,iCAAiCA,CAACC,IAAI,EAAE;EACvF;EACA,IAAIC,KAAK,GAAG,6BAA6B,CAACC,IAAI,CAACF,IAAI,CAAC;EACpD,IAAIC,KAAK,EAAE,OAAOP,WAAW,CAACO,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEzCA,KAAK,GAAG,oBAAoB,CAACC,IAAI,CAACF,IAAI,CAAC;EACvC,IAAIC,KAAK,EAAE,OAAOP,WAAW,CAACO,KAAK,CAAC,CAAC,CAAC,CAAC;EACvC,OAAOE,SAAS;AAClB,CAAC;AAED,IAAIC,0BAA0B,GAAG,eAAe,IAAIC,GAAG,CAAC,CAAC,iBAAiB,EAAE,cAAc,EAAE,sBAAsB,EAAE,gBAAgB,CAAC,CAAC,CAAC,CAAC;AACxI;AACA;;AAEA,IAAIC,kBAAkB,GAAG,SAASA,kBAAkBA,CAACC,UAAU,EAAE;EAC/D,OAAOA,UAAU,CAACC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;AACvC,CAAC;AAED,IAAIC,sBAAsB,GAAG,SAASA,sBAAsBA,CAACC,UAAU,EAAE;EACvE,IAAI,CAACA,UAAU,EAAE,OAAOP,SAAS;EACjC,IAAIQ,KAAK,GAAGD,UAAU,CAACb,KAAK,CAAC,IAAI,CAAC;EAElC,KAAK,IAAIe,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACb,MAAM,EAAEc,CAAC,EAAE,EAAE;IACrC,IAAIjB,YAAY,GAAGI,iCAAiC,CAACY,KAAK,CAACC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEhE,IAAI,CAACjB,YAAY,EAAE,SAAS,CAAC;;IAE7B,IAAIS,0BAA0B,CAACS,GAAG,CAAClB,YAAY,CAAC,EAAE,MAAM,CAAC;IACzD;;IAEA,IAAI,QAAQ,CAACmB,IAAI,CAACnB,YAAY,CAAC,EAAE,OAAOW,kBAAkB,CAACX,YAAY,CAAC;EAC1E;EAEA,OAAOQ,SAAS;AAClB,CAAC;AAED,IAAIY,YAAY,GAAG,oCAAoC;AACvD,IAAIC,aAAa,GAAG,qCAAqC;AACzD,IAAIC,kBAAkB,GAAG,SAASA,kBAAkBA,CAACC,IAAI,EAAE/C,KAAK,EAAE;EAChE,IAAIV,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAI,OAAOQ,KAAK,CAACgD,GAAG,KAAK,QAAQ;EAAI;EAC9EhD,KAAK,CAACgD,GAAG,CAACC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;IAC7B,MAAM,IAAIpC,KAAK,CAAC,4HAA4H,GAAGb,KAAK,CAACgD,GAAG,GAAG,GAAG,CAAC;EACjK;EAEA,IAAIE,QAAQ,GAAG,CAAC,CAAC;EAEjB,KAAK,IAAI7D,GAAG,IAAIW,KAAK,EAAE;IACrB,IAAIf,cAAc,CAACkE,IAAI,CAACnD,KAAK,EAAEX,GAAG,CAAC,EAAE;MACnC6D,QAAQ,CAAC7D,GAAG,CAAC,GAAGW,KAAK,CAACX,GAAG,CAAC;IAC5B;EACF;EAEA6D,QAAQ,CAACN,YAAY,CAAC,GAAGG,IAAI,CAAC,CAAC;EAC/B;;EAEA,IAAIzD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAI,CAAC,CAACQ,KAAK,CAACgD,GAAG,KAAK,OAAOhD,KAAK,CAACgD,GAAG,KAAK,QAAQ,IAAI,OAAOhD,KAAK,CAACgD,GAAG,CAAC5B,IAAI,KAAK,QAAQ,IAAIpB,KAAK,CAACgD,GAAG,CAAC5B,IAAI,CAAC6B,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;IACvK,IAAIG,KAAK,GAAGd,sBAAsB,CAAC,IAAIzB,KAAK,CAAC,CAAC,CAACwC,KAAK,CAAC;IACrD,IAAID,KAAK,EAAEF,QAAQ,CAACL,aAAa,CAAC,GAAGO,KAAK;EAC5C;EAEA,OAAOF,QAAQ;AACjB,CAAC;AAED,IAAII,SAAS,GAAG,SAASA,SAASA,CAACC,IAAI,EAAE;EACvC,IAAIrD,KAAK,GAAGqD,IAAI,CAACrD,KAAK;IAClBsD,UAAU,GAAGD,IAAI,CAACC,UAAU;IAC5BC,WAAW,GAAGF,IAAI,CAACE,WAAW;EAClC7E,cAAc,CAACsB,KAAK,EAAEsD,UAAU,EAAEC,WAAW,CAAC;EAC9C1E,wCAAwC,CAAC,YAAY;IACnD,OAAOF,YAAY,CAACqB,KAAK,EAAEsD,UAAU,EAAEC,WAAW,CAAC;EACrD,CAAC,CAAC;EAEF,OAAO,IAAI;AACb,CAAC;AAED,IAAIC,OAAO,GAAG,eAAe5D,gBAAgB,CAAC,UAAUE,KAAK,EAAEE,KAAK,EAAED,GAAG,EAAE;EACzE,IAAI0D,OAAO,GAAG3D,KAAK,CAACgD,GAAG,CAAC,CAAC;EACzB;EACA;;EAEA,IAAI,OAAOW,OAAO,KAAK,QAAQ,IAAIzD,KAAK,CAAC0D,UAAU,CAACD,OAAO,CAAC,KAAK3B,SAAS,EAAE;IAC1E2B,OAAO,GAAGzD,KAAK,CAAC0D,UAAU,CAACD,OAAO,CAAC;EACrC;EAEA,IAAIE,gBAAgB,GAAG7D,KAAK,CAAC4C,YAAY,CAAC;EAC1C,IAAIkB,gBAAgB,GAAG,CAACH,OAAO,CAAC;EAChC,IAAII,SAAS,GAAG,EAAE;EAElB,IAAI,OAAO/D,KAAK,CAAC+D,SAAS,KAAK,QAAQ,EAAE;IACvCA,SAAS,GAAGpF,mBAAmB,CAACuB,KAAK,CAAC0D,UAAU,EAAEE,gBAAgB,EAAE9D,KAAK,CAAC+D,SAAS,CAAC;EACtF,CAAC,MAAM,IAAI/D,KAAK,CAAC+D,SAAS,IAAI,IAAI,EAAE;IAClCA,SAAS,GAAG/D,KAAK,CAAC+D,SAAS,GAAG,GAAG;EACnC;EAEA,IAAIP,UAAU,GAAG1E,eAAe,CAACgF,gBAAgB,EAAE9B,SAAS,EAAE5D,KAAK,CAACC,UAAU,CAACgC,YAAY,CAAC,CAAC;EAE7F,IAAIf,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAIgE,UAAU,CAACpC,IAAI,CAAC6B,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;IAChF,IAAIe,cAAc,GAAGhE,KAAK,CAAC6C,aAAa,CAAC;IAEzC,IAAImB,cAAc,EAAE;MAClBR,UAAU,GAAG1E,eAAe,CAAC,CAAC0E,UAAU,EAAE,QAAQ,GAAGQ,cAAc,GAAG,GAAG,CAAC,CAAC;IAC7E;EACF;EAEAD,SAAS,IAAI7D,KAAK,CAACb,GAAG,GAAG,GAAG,GAAGmE,UAAU,CAACpC,IAAI;EAC9C,IAAI8B,QAAQ,GAAG,CAAC,CAAC;EAEjB,KAAK,IAAI7D,GAAG,IAAIW,KAAK,EAAE;IACrB,IAAIf,cAAc,CAACkE,IAAI,CAACnD,KAAK,EAAEX,GAAG,CAAC,IAAIA,GAAG,KAAK,KAAK,IAAIA,GAAG,KAAKuD,YAAY,KAAKtD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAIH,GAAG,KAAKwD,aAAa,CAAC,EAAE;MAChJK,QAAQ,CAAC7D,GAAG,CAAC,GAAGW,KAAK,CAACX,GAAG,CAAC;IAC5B;EACF;EAEA6D,QAAQ,CAACjD,GAAG,GAAGA,GAAG;EAClBiD,QAAQ,CAACa,SAAS,GAAGA,SAAS;EAC9B,OAAO,aAAa3F,KAAK,CAAC+B,aAAa,CAAC/B,KAAK,CAAC6F,QAAQ,EAAE,IAAI,EAAE,aAAa7F,KAAK,CAAC+B,aAAa,CAACmD,SAAS,EAAE;IACxGpD,KAAK,EAAEA,KAAK;IACZsD,UAAU,EAAEA,UAAU;IACtBC,WAAW,EAAE,OAAOI,gBAAgB,KAAK;EAC3C,CAAC,CAAC,EAAE,aAAazF,KAAK,CAAC+B,aAAa,CAAC0D,gBAAgB,EAAEX,QAAQ,CAAC,CAAC;AACnE,CAAC,CAAC;AAEF,IAAI5D,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCkE,OAAO,CAACjE,WAAW,GAAG,wBAAwB;AAChD;AAEA,IAAIyE,SAAS,GAAGR,OAAO;AAEvB,SAAShE,aAAa,IAAIyE,CAAC,EAAED,SAAS,IAAIE,CAAC,EAAE/D,YAAY,IAAIgE,CAAC,EAAEzE,wBAAwB,IAAI0E,CAAC,EAAEvD,aAAa,IAAIwD,CAAC,EAAEtD,SAAS,IAAIuD,CAAC,EAAE1B,kBAAkB,IAAI2B,CAAC,EAAExF,cAAc,IAAIyF,CAAC,EAAE1F,SAAS,IAAIyD,CAAC,EAAEnC,QAAQ,IAAIqE,CAAC,EAAE7E,gBAAgB,IAAI8E,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}