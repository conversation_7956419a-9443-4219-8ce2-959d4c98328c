{"ast": null, "code": "import { unstable_generateUtilityClasses as generateUtilityClasses } from '@mui/utils';\nimport generateUtilityClass from '../generateUtilityClass';\nexport function getTableHeadUtilityClass(slot) {\n  return generateUtilityClass('MuiTableHead', slot);\n}\nconst tableHeadClasses = generateUtilityClasses('MuiTableHead', ['root']);\nexport default tableHeadClasses;", "map": {"version": 3, "names": ["unstable_generateUtilityClasses", "generateUtilityClasses", "generateUtilityClass", "getTableHeadUtilityClass", "slot", "tableHeadClasses"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/material/TableHead/tableHeadClasses.js"], "sourcesContent": ["import { unstable_generateUtilityClasses as generateUtilityClasses } from '@mui/utils';\nimport generateUtilityClass from '../generateUtilityClass';\nexport function getTableHeadUtilityClass(slot) {\n  return generateUtilityClass('MuiTableHead', slot);\n}\nconst tableHeadClasses = generateUtilityClasses('MuiTableHead', ['root']);\nexport default tableHeadClasses;"], "mappings": "AAAA,SAASA,+BAA+B,IAAIC,sBAAsB,QAAQ,YAAY;AACtF,OAAOC,oBAAoB,MAAM,yBAAyB;AAC1D,OAAO,SAASC,wBAAwBA,CAACC,IAAI,EAAE;EAC7C,OAAOF,oBAAoB,CAAC,cAAc,EAAEE,IAAI,CAAC;AACnD;AACA,MAAMC,gBAAgB,GAAGJ,sBAAsB,CAAC,cAAc,EAAE,CAAC,MAAM,CAAC,CAAC;AACzE,eAAeI,gBAAgB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}