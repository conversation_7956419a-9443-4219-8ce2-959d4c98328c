{"ast": null, "code": "'use client';\n\n// @inheritedComponent IconButton\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"className\", \"color\", \"edge\", \"size\", \"sx\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { refType } from '@mui/utils';\nimport { unstable_composeClasses as composeClasses } from '@mui/base/composeClasses';\nimport { alpha, darken, lighten } from '@mui/system';\nimport capitalize from '../utils/capitalize';\nimport SwitchBase from '../internal/SwitchBase';\nimport useThemeProps from '../styles/useThemeProps';\nimport styled from '../styles/styled';\nimport switchClasses, { getSwitchUtilityClass } from './switchClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    edge,\n    size,\n    color,\n    checked,\n    disabled\n  } = ownerState;\n  const slots = {\n    root: ['root', edge && `edge${capitalize(edge)}`, `size${capitalize(size)}`],\n    switchBase: ['switchBase', `color${capitalize(color)}`, checked && 'checked', disabled && 'disabled'],\n    thumb: ['thumb'],\n    track: ['track'],\n    input: ['input']\n  };\n  const composedClasses = composeClasses(slots, getSwitchUtilityClass, classes);\n  return _extends({}, classes, composedClasses);\n};\nconst SwitchRoot = styled('span', {\n  name: 'MuiSwitch',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.edge && styles[`edge${capitalize(ownerState.edge)}`], styles[`size${capitalize(ownerState.size)}`]];\n  }\n})(({\n  ownerState\n}) => _extends({\n  display: 'inline-flex',\n  width: 34 + 12 * 2,\n  height: 14 + 12 * 2,\n  overflow: 'hidden',\n  padding: 12,\n  boxSizing: 'border-box',\n  position: 'relative',\n  flexShrink: 0,\n  zIndex: 0,\n  // Reset the stacking context.\n  verticalAlign: 'middle',\n  // For correct alignment with the text.\n  '@media print': {\n    colorAdjust: 'exact'\n  }\n}, ownerState.edge === 'start' && {\n  marginLeft: -8\n}, ownerState.edge === 'end' && {\n  marginRight: -8\n}, ownerState.size === 'small' && {\n  width: 40,\n  height: 24,\n  padding: 7,\n  [`& .${switchClasses.thumb}`]: {\n    width: 16,\n    height: 16\n  },\n  [`& .${switchClasses.switchBase}`]: {\n    padding: 4,\n    [`&.${switchClasses.checked}`]: {\n      transform: 'translateX(16px)'\n    }\n  }\n}));\nconst SwitchSwitchBase = styled(SwitchBase, {\n  name: 'MuiSwitch',\n  slot: 'SwitchBase',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.switchBase, {\n      [`& .${switchClasses.input}`]: styles.input\n    }, ownerState.color !== 'default' && styles[`color${capitalize(ownerState.color)}`]];\n  }\n})(({\n  theme\n}) => ({\n  position: 'absolute',\n  top: 0,\n  left: 0,\n  zIndex: 1,\n  // Render above the focus ripple.\n  color: theme.vars ? theme.vars.palette.Switch.defaultColor : `${theme.palette.mode === 'light' ? theme.palette.common.white : theme.palette.grey[300]}`,\n  transition: theme.transitions.create(['left', 'transform'], {\n    duration: theme.transitions.duration.shortest\n  }),\n  [`&.${switchClasses.checked}`]: {\n    transform: 'translateX(20px)'\n  },\n  [`&.${switchClasses.disabled}`]: {\n    color: theme.vars ? theme.vars.palette.Switch.defaultDisabledColor : `${theme.palette.mode === 'light' ? theme.palette.grey[100] : theme.palette.grey[600]}`\n  },\n  [`&.${switchClasses.checked} + .${switchClasses.track}`]: {\n    opacity: 0.5\n  },\n  [`&.${switchClasses.disabled} + .${switchClasses.track}`]: {\n    opacity: theme.vars ? theme.vars.opacity.switchTrackDisabled : `${theme.palette.mode === 'light' ? 0.12 : 0.2}`\n  },\n  [`& .${switchClasses.input}`]: {\n    left: '-100%',\n    width: '300%'\n  }\n}), ({\n  theme,\n  ownerState\n}) => _extends({\n  '&:hover': {\n    backgroundColor: theme.vars ? `rgba(${theme.vars.palette.action.activeChannel} / ${theme.vars.palette.action.hoverOpacity})` : alpha(theme.palette.action.active, theme.palette.action.hoverOpacity),\n    // Reset on touch devices, it doesn't add specificity\n    '@media (hover: none)': {\n      backgroundColor: 'transparent'\n    }\n  }\n}, ownerState.color !== 'default' && {\n  [`&.${switchClasses.checked}`]: {\n    color: (theme.vars || theme).palette[ownerState.color].main,\n    '&:hover': {\n      backgroundColor: theme.vars ? `rgba(${theme.vars.palette[ownerState.color].mainChannel} / ${theme.vars.palette.action.hoverOpacity})` : alpha(theme.palette[ownerState.color].main, theme.palette.action.hoverOpacity),\n      '@media (hover: none)': {\n        backgroundColor: 'transparent'\n      }\n    },\n    [`&.${switchClasses.disabled}`]: {\n      color: theme.vars ? theme.vars.palette.Switch[`${ownerState.color}DisabledColor`] : `${theme.palette.mode === 'light' ? lighten(theme.palette[ownerState.color].main, 0.62) : darken(theme.palette[ownerState.color].main, 0.55)}`\n    }\n  },\n  [`&.${switchClasses.checked} + .${switchClasses.track}`]: {\n    backgroundColor: (theme.vars || theme).palette[ownerState.color].main\n  }\n}));\nconst SwitchTrack = styled('span', {\n  name: 'MuiSwitch',\n  slot: 'Track',\n  overridesResolver: (props, styles) => styles.track\n})(({\n  theme\n}) => ({\n  height: '100%',\n  width: '100%',\n  borderRadius: 14 / 2,\n  zIndex: -1,\n  transition: theme.transitions.create(['opacity', 'background-color'], {\n    duration: theme.transitions.duration.shortest\n  }),\n  backgroundColor: theme.vars ? theme.vars.palette.common.onBackground : `${theme.palette.mode === 'light' ? theme.palette.common.black : theme.palette.common.white}`,\n  opacity: theme.vars ? theme.vars.opacity.switchTrack : `${theme.palette.mode === 'light' ? 0.38 : 0.3}`\n}));\nconst SwitchThumb = styled('span', {\n  name: 'MuiSwitch',\n  slot: 'Thumb',\n  overridesResolver: (props, styles) => styles.thumb\n})(({\n  theme\n}) => ({\n  boxShadow: (theme.vars || theme).shadows[1],\n  backgroundColor: 'currentColor',\n  width: 20,\n  height: 20,\n  borderRadius: '50%'\n}));\nconst Switch = /*#__PURE__*/React.forwardRef(function Switch(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiSwitch'\n  });\n  const {\n      className,\n      color = 'primary',\n      edge = false,\n      size = 'medium',\n      sx\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    color,\n    edge,\n    size\n  });\n  const classes = useUtilityClasses(ownerState);\n  const icon = /*#__PURE__*/_jsx(SwitchThumb, {\n    className: classes.thumb,\n    ownerState: ownerState\n  });\n  return /*#__PURE__*/_jsxs(SwitchRoot, {\n    className: clsx(classes.root, className),\n    sx: sx,\n    ownerState: ownerState,\n    children: [/*#__PURE__*/_jsx(SwitchSwitchBase, _extends({\n      type: \"checkbox\",\n      icon: icon,\n      checkedIcon: icon,\n      ref: ref,\n      ownerState: ownerState\n    }, other, {\n      classes: _extends({}, classes, {\n        root: classes.switchBase\n      })\n    })), /*#__PURE__*/_jsx(SwitchTrack, {\n      className: classes.track,\n      ownerState: ownerState\n    })]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Switch.propTypes /* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit the d.ts file and run \"yarn proptypes\"     |\n  // ----------------------------------------------------------------------\n  /**\n   * If `true`, the component is checked.\n   */\n  checked: PropTypes.bool,\n  /**\n   * The icon to display when the component is checked.\n   */\n  checkedIcon: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The color of the component.\n   * It supports both default and custom theme colors, which can be added as shown in the\n   * [palette customization guide](https://mui.com/material-ui/customization/palette/#custom-colors).\n   * @default 'primary'\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['default', 'primary', 'secondary', 'error', 'info', 'success', 'warning']), PropTypes.string]),\n  /**\n   * The default checked state. Use when the component is not controlled.\n   */\n  defaultChecked: PropTypes.bool,\n  /**\n   * If `true`, the component is disabled.\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, the ripple effect is disabled.\n   * @default false\n   */\n  disableRipple: PropTypes.bool,\n  /**\n   * If given, uses a negative margin to counteract the padding on one\n   * side (this is often helpful for aligning the left or right\n   * side of the icon with content above or below, without ruining the border\n   * size and shape).\n   * @default false\n   */\n  edge: PropTypes.oneOf(['end', 'start', false]),\n  /**\n   * The icon to display when the component is unchecked.\n   */\n  icon: PropTypes.node,\n  /**\n   * The id of the `input` element.\n   */\n  id: PropTypes.string,\n  /**\n   * [Attributes](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input#Attributes) applied to the `input` element.\n   */\n  inputProps: PropTypes.object,\n  /**\n   * Pass a ref to the `input` element.\n   */\n  inputRef: refType,\n  /**\n   * Callback fired when the state is changed.\n   *\n   * @param {React.ChangeEvent<HTMLInputElement>} event The event source of the callback.\n   * You can pull out the new value by accessing `event.target.value` (string).\n   * You can pull out the new checked state by accessing `event.target.checked` (boolean).\n   */\n  onChange: PropTypes.func,\n  /**\n   * If `true`, the `input` element is required.\n   * @default false\n   */\n  required: PropTypes.bool,\n  /**\n   * The size of the component.\n   * `small` is equivalent to the dense switch styling.\n   * @default 'medium'\n   */\n  size: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['medium', 'small']), PropTypes.string]),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The value of the component. The DOM API casts this to a string.\n   * The browser uses \"on\" as the default value.\n   */\n  value: PropTypes.any\n} : void 0;\nexport default Switch;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "React", "PropTypes", "clsx", "refType", "unstable_composeClasses", "composeClasses", "alpha", "darken", "lighten", "capitalize", "SwitchBase", "useThemeProps", "styled", "switchClasses", "getSwitchUtilityClass", "jsx", "_jsx", "jsxs", "_jsxs", "useUtilityClasses", "ownerState", "classes", "edge", "size", "color", "checked", "disabled", "slots", "root", "switchBase", "thumb", "track", "input", "composedClasses", "SwitchRoot", "name", "slot", "overridesResolver", "props", "styles", "display", "width", "height", "overflow", "padding", "boxSizing", "position", "flexShrink", "zIndex", "verticalAlign", "colorAdjust", "marginLeft", "marginRight", "transform", "SwitchSwitchBase", "theme", "top", "left", "vars", "palette", "Switch", "defaultColor", "mode", "common", "white", "grey", "transition", "transitions", "create", "duration", "shortest", "defaultDisabledColor", "opacity", "switchTrackDisabled", "backgroundColor", "action", "activeChannel", "hoverOpacity", "active", "main", "mainChannel", "SwitchTrack", "borderRadius", "onBackground", "black", "switchTrack", "SwitchThumb", "boxShadow", "shadows", "forwardRef", "inProps", "ref", "className", "sx", "other", "icon", "children", "type", "checkedIcon", "process", "env", "NODE_ENV", "propTypes", "bool", "node", "object", "string", "oneOfType", "oneOf", "defaultChecked", "disable<PERSON><PERSON><PERSON>", "id", "inputProps", "inputRef", "onChange", "func", "required", "arrayOf", "value", "any"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/material/Switch/Switch.js"], "sourcesContent": ["'use client';\n\n// @inheritedComponent IconButton\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"className\", \"color\", \"edge\", \"size\", \"sx\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { refType } from '@mui/utils';\nimport { unstable_composeClasses as composeClasses } from '@mui/base/composeClasses';\nimport { alpha, darken, lighten } from '@mui/system';\nimport capitalize from '../utils/capitalize';\nimport SwitchBase from '../internal/SwitchBase';\nimport useThemeProps from '../styles/useThemeProps';\nimport styled from '../styles/styled';\nimport switchClasses, { getSwitchUtilityClass } from './switchClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    edge,\n    size,\n    color,\n    checked,\n    disabled\n  } = ownerState;\n  const slots = {\n    root: ['root', edge && `edge${capitalize(edge)}`, `size${capitalize(size)}`],\n    switchBase: ['switchBase', `color${capitalize(color)}`, checked && 'checked', disabled && 'disabled'],\n    thumb: ['thumb'],\n    track: ['track'],\n    input: ['input']\n  };\n  const composedClasses = composeClasses(slots, getSwitchUtilityClass, classes);\n  return _extends({}, classes, composedClasses);\n};\nconst SwitchRoot = styled('span', {\n  name: 'MuiSwitch',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.edge && styles[`edge${capitalize(ownerState.edge)}`], styles[`size${capitalize(ownerState.size)}`]];\n  }\n})(({\n  ownerState\n}) => _extends({\n  display: 'inline-flex',\n  width: 34 + 12 * 2,\n  height: 14 + 12 * 2,\n  overflow: 'hidden',\n  padding: 12,\n  boxSizing: 'border-box',\n  position: 'relative',\n  flexShrink: 0,\n  zIndex: 0,\n  // Reset the stacking context.\n  verticalAlign: 'middle',\n  // For correct alignment with the text.\n  '@media print': {\n    colorAdjust: 'exact'\n  }\n}, ownerState.edge === 'start' && {\n  marginLeft: -8\n}, ownerState.edge === 'end' && {\n  marginRight: -8\n}, ownerState.size === 'small' && {\n  width: 40,\n  height: 24,\n  padding: 7,\n  [`& .${switchClasses.thumb}`]: {\n    width: 16,\n    height: 16\n  },\n  [`& .${switchClasses.switchBase}`]: {\n    padding: 4,\n    [`&.${switchClasses.checked}`]: {\n      transform: 'translateX(16px)'\n    }\n  }\n}));\nconst SwitchSwitchBase = styled(SwitchBase, {\n  name: 'MuiSwitch',\n  slot: 'SwitchBase',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.switchBase, {\n      [`& .${switchClasses.input}`]: styles.input\n    }, ownerState.color !== 'default' && styles[`color${capitalize(ownerState.color)}`]];\n  }\n})(({\n  theme\n}) => ({\n  position: 'absolute',\n  top: 0,\n  left: 0,\n  zIndex: 1,\n  // Render above the focus ripple.\n  color: theme.vars ? theme.vars.palette.Switch.defaultColor : `${theme.palette.mode === 'light' ? theme.palette.common.white : theme.palette.grey[300]}`,\n  transition: theme.transitions.create(['left', 'transform'], {\n    duration: theme.transitions.duration.shortest\n  }),\n  [`&.${switchClasses.checked}`]: {\n    transform: 'translateX(20px)'\n  },\n  [`&.${switchClasses.disabled}`]: {\n    color: theme.vars ? theme.vars.palette.Switch.defaultDisabledColor : `${theme.palette.mode === 'light' ? theme.palette.grey[100] : theme.palette.grey[600]}`\n  },\n  [`&.${switchClasses.checked} + .${switchClasses.track}`]: {\n    opacity: 0.5\n  },\n  [`&.${switchClasses.disabled} + .${switchClasses.track}`]: {\n    opacity: theme.vars ? theme.vars.opacity.switchTrackDisabled : `${theme.palette.mode === 'light' ? 0.12 : 0.2}`\n  },\n  [`& .${switchClasses.input}`]: {\n    left: '-100%',\n    width: '300%'\n  }\n}), ({\n  theme,\n  ownerState\n}) => _extends({\n  '&:hover': {\n    backgroundColor: theme.vars ? `rgba(${theme.vars.palette.action.activeChannel} / ${theme.vars.palette.action.hoverOpacity})` : alpha(theme.palette.action.active, theme.palette.action.hoverOpacity),\n    // Reset on touch devices, it doesn't add specificity\n    '@media (hover: none)': {\n      backgroundColor: 'transparent'\n    }\n  }\n}, ownerState.color !== 'default' && {\n  [`&.${switchClasses.checked}`]: {\n    color: (theme.vars || theme).palette[ownerState.color].main,\n    '&:hover': {\n      backgroundColor: theme.vars ? `rgba(${theme.vars.palette[ownerState.color].mainChannel} / ${theme.vars.palette.action.hoverOpacity})` : alpha(theme.palette[ownerState.color].main, theme.palette.action.hoverOpacity),\n      '@media (hover: none)': {\n        backgroundColor: 'transparent'\n      }\n    },\n    [`&.${switchClasses.disabled}`]: {\n      color: theme.vars ? theme.vars.palette.Switch[`${ownerState.color}DisabledColor`] : `${theme.palette.mode === 'light' ? lighten(theme.palette[ownerState.color].main, 0.62) : darken(theme.palette[ownerState.color].main, 0.55)}`\n    }\n  },\n  [`&.${switchClasses.checked} + .${switchClasses.track}`]: {\n    backgroundColor: (theme.vars || theme).palette[ownerState.color].main\n  }\n}));\nconst SwitchTrack = styled('span', {\n  name: 'MuiSwitch',\n  slot: 'Track',\n  overridesResolver: (props, styles) => styles.track\n})(({\n  theme\n}) => ({\n  height: '100%',\n  width: '100%',\n  borderRadius: 14 / 2,\n  zIndex: -1,\n  transition: theme.transitions.create(['opacity', 'background-color'], {\n    duration: theme.transitions.duration.shortest\n  }),\n  backgroundColor: theme.vars ? theme.vars.palette.common.onBackground : `${theme.palette.mode === 'light' ? theme.palette.common.black : theme.palette.common.white}`,\n  opacity: theme.vars ? theme.vars.opacity.switchTrack : `${theme.palette.mode === 'light' ? 0.38 : 0.3}`\n}));\nconst SwitchThumb = styled('span', {\n  name: 'MuiSwitch',\n  slot: 'Thumb',\n  overridesResolver: (props, styles) => styles.thumb\n})(({\n  theme\n}) => ({\n  boxShadow: (theme.vars || theme).shadows[1],\n  backgroundColor: 'currentColor',\n  width: 20,\n  height: 20,\n  borderRadius: '50%'\n}));\nconst Switch = /*#__PURE__*/React.forwardRef(function Switch(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiSwitch'\n  });\n  const {\n      className,\n      color = 'primary',\n      edge = false,\n      size = 'medium',\n      sx\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    color,\n    edge,\n    size\n  });\n  const classes = useUtilityClasses(ownerState);\n  const icon = /*#__PURE__*/_jsx(SwitchThumb, {\n    className: classes.thumb,\n    ownerState: ownerState\n  });\n  return /*#__PURE__*/_jsxs(SwitchRoot, {\n    className: clsx(classes.root, className),\n    sx: sx,\n    ownerState: ownerState,\n    children: [/*#__PURE__*/_jsx(SwitchSwitchBase, _extends({\n      type: \"checkbox\",\n      icon: icon,\n      checkedIcon: icon,\n      ref: ref,\n      ownerState: ownerState\n    }, other, {\n      classes: _extends({}, classes, {\n        root: classes.switchBase\n      })\n    })), /*#__PURE__*/_jsx(SwitchTrack, {\n      className: classes.track,\n      ownerState: ownerState\n    })]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Switch.propTypes /* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit the d.ts file and run \"yarn proptypes\"     |\n  // ----------------------------------------------------------------------\n  /**\n   * If `true`, the component is checked.\n   */\n  checked: PropTypes.bool,\n  /**\n   * The icon to display when the component is checked.\n   */\n  checkedIcon: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The color of the component.\n   * It supports both default and custom theme colors, which can be added as shown in the\n   * [palette customization guide](https://mui.com/material-ui/customization/palette/#custom-colors).\n   * @default 'primary'\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['default', 'primary', 'secondary', 'error', 'info', 'success', 'warning']), PropTypes.string]),\n  /**\n   * The default checked state. Use when the component is not controlled.\n   */\n  defaultChecked: PropTypes.bool,\n  /**\n   * If `true`, the component is disabled.\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, the ripple effect is disabled.\n   * @default false\n   */\n  disableRipple: PropTypes.bool,\n  /**\n   * If given, uses a negative margin to counteract the padding on one\n   * side (this is often helpful for aligning the left or right\n   * side of the icon with content above or below, without ruining the border\n   * size and shape).\n   * @default false\n   */\n  edge: PropTypes.oneOf(['end', 'start', false]),\n  /**\n   * The icon to display when the component is unchecked.\n   */\n  icon: PropTypes.node,\n  /**\n   * The id of the `input` element.\n   */\n  id: PropTypes.string,\n  /**\n   * [Attributes](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input#Attributes) applied to the `input` element.\n   */\n  inputProps: PropTypes.object,\n  /**\n   * Pass a ref to the `input` element.\n   */\n  inputRef: refType,\n  /**\n   * Callback fired when the state is changed.\n   *\n   * @param {React.ChangeEvent<HTMLInputElement>} event The event source of the callback.\n   * You can pull out the new value by accessing `event.target.value` (string).\n   * You can pull out the new checked state by accessing `event.target.checked` (boolean).\n   */\n  onChange: PropTypes.func,\n  /**\n   * If `true`, the `input` element is required.\n   * @default false\n   */\n  required: PropTypes.bool,\n  /**\n   * The size of the component.\n   * `small` is equivalent to the dense switch styling.\n   * @default 'medium'\n   */\n  size: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['medium', 'small']), PropTypes.string]),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The value of the component. The DOM API casts this to a string.\n   * The browser uses \"on\" as the default value.\n   */\n  value: PropTypes.any\n} : void 0;\nexport default Switch;"], "mappings": "AAAA,YAAY;;AAEZ;AACA,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,WAAW,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC;AAC9D,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,OAAO,QAAQ,YAAY;AACpC,SAASC,uBAAuB,IAAIC,cAAc,QAAQ,0BAA0B;AACpF,SAASC,KAAK,EAAEC,MAAM,EAAEC,OAAO,QAAQ,aAAa;AACpD,OAAOC,UAAU,MAAM,qBAAqB;AAC5C,OAAOC,UAAU,MAAM,wBAAwB;AAC/C,OAAOC,aAAa,MAAM,yBAAyB;AACnD,OAAOC,MAAM,MAAM,kBAAkB;AACrC,OAAOC,aAAa,IAAIC,qBAAqB,QAAQ,iBAAiB;AACtE,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,OAAO;IACPC,IAAI;IACJC,IAAI;IACJC,KAAK;IACLC,OAAO;IACPC;EACF,CAAC,GAAGN,UAAU;EACd,MAAMO,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEN,IAAI,IAAK,OAAMb,UAAU,CAACa,IAAI,CAAE,EAAC,EAAG,OAAMb,UAAU,CAACc,IAAI,CAAE,EAAC,CAAC;IAC5EM,UAAU,EAAE,CAAC,YAAY,EAAG,QAAOpB,UAAU,CAACe,KAAK,CAAE,EAAC,EAAEC,OAAO,IAAI,SAAS,EAAEC,QAAQ,IAAI,UAAU,CAAC;IACrGI,KAAK,EAAE,CAAC,OAAO,CAAC;IAChBC,KAAK,EAAE,CAAC,OAAO,CAAC;IAChBC,KAAK,EAAE,CAAC,OAAO;EACjB,CAAC;EACD,MAAMC,eAAe,GAAG5B,cAAc,CAACsB,KAAK,EAAEb,qBAAqB,EAAEO,OAAO,CAAC;EAC7E,OAAOvB,QAAQ,CAAC,CAAC,CAAC,EAAEuB,OAAO,EAAEY,eAAe,CAAC;AAC/C,CAAC;AACD,MAAMC,UAAU,GAAGtB,MAAM,CAAC,MAAM,EAAE;EAChCuB,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJnB;IACF,CAAC,GAAGkB,KAAK;IACT,OAAO,CAACC,MAAM,CAACX,IAAI,EAAER,UAAU,CAACE,IAAI,IAAIiB,MAAM,CAAE,OAAM9B,UAAU,CAACW,UAAU,CAACE,IAAI,CAAE,EAAC,CAAC,EAAEiB,MAAM,CAAE,OAAM9B,UAAU,CAACW,UAAU,CAACG,IAAI,CAAE,EAAC,CAAC,CAAC;EACrI;AACF,CAAC,CAAC,CAAC,CAAC;EACFH;AACF,CAAC,KAAKtB,QAAQ,CAAC;EACb0C,OAAO,EAAE,aAAa;EACtBC,KAAK,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;EAClBC,MAAM,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;EACnBC,QAAQ,EAAE,QAAQ;EAClBC,OAAO,EAAE,EAAE;EACXC,SAAS,EAAE,YAAY;EACvBC,QAAQ,EAAE,UAAU;EACpBC,UAAU,EAAE,CAAC;EACbC,MAAM,EAAE,CAAC;EACT;EACAC,aAAa,EAAE,QAAQ;EACvB;EACA,cAAc,EAAE;IACdC,WAAW,EAAE;EACf;AACF,CAAC,EAAE9B,UAAU,CAACE,IAAI,KAAK,OAAO,IAAI;EAChC6B,UAAU,EAAE,CAAC;AACf,CAAC,EAAE/B,UAAU,CAACE,IAAI,KAAK,KAAK,IAAI;EAC9B8B,WAAW,EAAE,CAAC;AAChB,CAAC,EAAEhC,UAAU,CAACG,IAAI,KAAK,OAAO,IAAI;EAChCkB,KAAK,EAAE,EAAE;EACTC,MAAM,EAAE,EAAE;EACVE,OAAO,EAAE,CAAC;EACV,CAAE,MAAK/B,aAAa,CAACiB,KAAM,EAAC,GAAG;IAC7BW,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC;EACD,CAAE,MAAK7B,aAAa,CAACgB,UAAW,EAAC,GAAG;IAClCe,OAAO,EAAE,CAAC;IACV,CAAE,KAAI/B,aAAa,CAACY,OAAQ,EAAC,GAAG;MAC9B4B,SAAS,EAAE;IACb;EACF;AACF,CAAC,CAAC,CAAC;AACH,MAAMC,gBAAgB,GAAG1C,MAAM,CAACF,UAAU,EAAE;EAC1CyB,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,YAAY;EAClBC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJnB;IACF,CAAC,GAAGkB,KAAK;IACT,OAAO,CAACC,MAAM,CAACV,UAAU,EAAE;MACzB,CAAE,MAAKhB,aAAa,CAACmB,KAAM,EAAC,GAAGO,MAAM,CAACP;IACxC,CAAC,EAAEZ,UAAU,CAACI,KAAK,KAAK,SAAS,IAAIe,MAAM,CAAE,QAAO9B,UAAU,CAACW,UAAU,CAACI,KAAK,CAAE,EAAC,CAAC,CAAC;EACtF;AACF,CAAC,CAAC,CAAC,CAAC;EACF+B;AACF,CAAC,MAAM;EACLT,QAAQ,EAAE,UAAU;EACpBU,GAAG,EAAE,CAAC;EACNC,IAAI,EAAE,CAAC;EACPT,MAAM,EAAE,CAAC;EACT;EACAxB,KAAK,EAAE+B,KAAK,CAACG,IAAI,GAAGH,KAAK,CAACG,IAAI,CAACC,OAAO,CAACC,MAAM,CAACC,YAAY,GAAI,GAAEN,KAAK,CAACI,OAAO,CAACG,IAAI,KAAK,OAAO,GAAGP,KAAK,CAACI,OAAO,CAACI,MAAM,CAACC,KAAK,GAAGT,KAAK,CAACI,OAAO,CAACM,IAAI,CAAC,GAAG,CAAE,EAAC;EACvJC,UAAU,EAAEX,KAAK,CAACY,WAAW,CAACC,MAAM,CAAC,CAAC,MAAM,EAAE,WAAW,CAAC,EAAE;IAC1DC,QAAQ,EAAEd,KAAK,CAACY,WAAW,CAACE,QAAQ,CAACC;EACvC,CAAC,CAAC;EACF,CAAE,KAAIzD,aAAa,CAACY,OAAQ,EAAC,GAAG;IAC9B4B,SAAS,EAAE;EACb,CAAC;EACD,CAAE,KAAIxC,aAAa,CAACa,QAAS,EAAC,GAAG;IAC/BF,KAAK,EAAE+B,KAAK,CAACG,IAAI,GAAGH,KAAK,CAACG,IAAI,CAACC,OAAO,CAACC,MAAM,CAACW,oBAAoB,GAAI,GAAEhB,KAAK,CAACI,OAAO,CAACG,IAAI,KAAK,OAAO,GAAGP,KAAK,CAACI,OAAO,CAACM,IAAI,CAAC,GAAG,CAAC,GAAGV,KAAK,CAACI,OAAO,CAACM,IAAI,CAAC,GAAG,CAAE;EAC7J,CAAC;EACD,CAAE,KAAIpD,aAAa,CAACY,OAAQ,OAAMZ,aAAa,CAACkB,KAAM,EAAC,GAAG;IACxDyC,OAAO,EAAE;EACX,CAAC;EACD,CAAE,KAAI3D,aAAa,CAACa,QAAS,OAAMb,aAAa,CAACkB,KAAM,EAAC,GAAG;IACzDyC,OAAO,EAAEjB,KAAK,CAACG,IAAI,GAAGH,KAAK,CAACG,IAAI,CAACc,OAAO,CAACC,mBAAmB,GAAI,GAAElB,KAAK,CAACI,OAAO,CAACG,IAAI,KAAK,OAAO,GAAG,IAAI,GAAG,GAAI;EAChH,CAAC;EACD,CAAE,MAAKjD,aAAa,CAACmB,KAAM,EAAC,GAAG;IAC7ByB,IAAI,EAAE,OAAO;IACbhB,KAAK,EAAE;EACT;AACF,CAAC,CAAC,EAAE,CAAC;EACHc,KAAK;EACLnC;AACF,CAAC,KAAKtB,QAAQ,CAAC;EACb,SAAS,EAAE;IACT4E,eAAe,EAAEnB,KAAK,CAACG,IAAI,GAAI,QAAOH,KAAK,CAACG,IAAI,CAACC,OAAO,CAACgB,MAAM,CAACC,aAAc,MAAKrB,KAAK,CAACG,IAAI,CAACC,OAAO,CAACgB,MAAM,CAACE,YAAa,GAAE,GAAGvE,KAAK,CAACiD,KAAK,CAACI,OAAO,CAACgB,MAAM,CAACG,MAAM,EAAEvB,KAAK,CAACI,OAAO,CAACgB,MAAM,CAACE,YAAY,CAAC;IACpM;IACA,sBAAsB,EAAE;MACtBH,eAAe,EAAE;IACnB;EACF;AACF,CAAC,EAAEtD,UAAU,CAACI,KAAK,KAAK,SAAS,IAAI;EACnC,CAAE,KAAIX,aAAa,CAACY,OAAQ,EAAC,GAAG;IAC9BD,KAAK,EAAE,CAAC+B,KAAK,CAACG,IAAI,IAAIH,KAAK,EAAEI,OAAO,CAACvC,UAAU,CAACI,KAAK,CAAC,CAACuD,IAAI;IAC3D,SAAS,EAAE;MACTL,eAAe,EAAEnB,KAAK,CAACG,IAAI,GAAI,QAAOH,KAAK,CAACG,IAAI,CAACC,OAAO,CAACvC,UAAU,CAACI,KAAK,CAAC,CAACwD,WAAY,MAAKzB,KAAK,CAACG,IAAI,CAACC,OAAO,CAACgB,MAAM,CAACE,YAAa,GAAE,GAAGvE,KAAK,CAACiD,KAAK,CAACI,OAAO,CAACvC,UAAU,CAACI,KAAK,CAAC,CAACuD,IAAI,EAAExB,KAAK,CAACI,OAAO,CAACgB,MAAM,CAACE,YAAY,CAAC;MACtN,sBAAsB,EAAE;QACtBH,eAAe,EAAE;MACnB;IACF,CAAC;IACD,CAAE,KAAI7D,aAAa,CAACa,QAAS,EAAC,GAAG;MAC/BF,KAAK,EAAE+B,KAAK,CAACG,IAAI,GAAGH,KAAK,CAACG,IAAI,CAACC,OAAO,CAACC,MAAM,CAAE,GAAExC,UAAU,CAACI,KAAM,eAAc,CAAC,GAAI,GAAE+B,KAAK,CAACI,OAAO,CAACG,IAAI,KAAK,OAAO,GAAGtD,OAAO,CAAC+C,KAAK,CAACI,OAAO,CAACvC,UAAU,CAACI,KAAK,CAAC,CAACuD,IAAI,EAAE,IAAI,CAAC,GAAGxE,MAAM,CAACgD,KAAK,CAACI,OAAO,CAACvC,UAAU,CAACI,KAAK,CAAC,CAACuD,IAAI,EAAE,IAAI,CAAE;IACnO;EACF,CAAC;EACD,CAAE,KAAIlE,aAAa,CAACY,OAAQ,OAAMZ,aAAa,CAACkB,KAAM,EAAC,GAAG;IACxD2C,eAAe,EAAE,CAACnB,KAAK,CAACG,IAAI,IAAIH,KAAK,EAAEI,OAAO,CAACvC,UAAU,CAACI,KAAK,CAAC,CAACuD;EACnE;AACF,CAAC,CAAC,CAAC;AACH,MAAME,WAAW,GAAGrE,MAAM,CAAC,MAAM,EAAE;EACjCuB,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,OAAO;EACbC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACR;AAC/C,CAAC,CAAC,CAAC,CAAC;EACFwB;AACF,CAAC,MAAM;EACLb,MAAM,EAAE,MAAM;EACdD,KAAK,EAAE,MAAM;EACbyC,YAAY,EAAE,EAAE,GAAG,CAAC;EACpBlC,MAAM,EAAE,CAAC,CAAC;EACVkB,UAAU,EAAEX,KAAK,CAACY,WAAW,CAACC,MAAM,CAAC,CAAC,SAAS,EAAE,kBAAkB,CAAC,EAAE;IACpEC,QAAQ,EAAEd,KAAK,CAACY,WAAW,CAACE,QAAQ,CAACC;EACvC,CAAC,CAAC;EACFI,eAAe,EAAEnB,KAAK,CAACG,IAAI,GAAGH,KAAK,CAACG,IAAI,CAACC,OAAO,CAACI,MAAM,CAACoB,YAAY,GAAI,GAAE5B,KAAK,CAACI,OAAO,CAACG,IAAI,KAAK,OAAO,GAAGP,KAAK,CAACI,OAAO,CAACI,MAAM,CAACqB,KAAK,GAAG7B,KAAK,CAACI,OAAO,CAACI,MAAM,CAACC,KAAM,EAAC;EACpKQ,OAAO,EAAEjB,KAAK,CAACG,IAAI,GAAGH,KAAK,CAACG,IAAI,CAACc,OAAO,CAACa,WAAW,GAAI,GAAE9B,KAAK,CAACI,OAAO,CAACG,IAAI,KAAK,OAAO,GAAG,IAAI,GAAG,GAAI;AACxG,CAAC,CAAC,CAAC;AACH,MAAMwB,WAAW,GAAG1E,MAAM,CAAC,MAAM,EAAE;EACjCuB,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,OAAO;EACbC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACT;AAC/C,CAAC,CAAC,CAAC,CAAC;EACFyB;AACF,CAAC,MAAM;EACLgC,SAAS,EAAE,CAAChC,KAAK,CAACG,IAAI,IAAIH,KAAK,EAAEiC,OAAO,CAAC,CAAC,CAAC;EAC3Cd,eAAe,EAAE,cAAc;EAC/BjC,KAAK,EAAE,EAAE;EACTC,MAAM,EAAE,EAAE;EACVwC,YAAY,EAAE;AAChB,CAAC,CAAC,CAAC;AACH,MAAMtB,MAAM,GAAG,aAAa5D,KAAK,CAACyF,UAAU,CAAC,SAAS7B,MAAMA,CAAC8B,OAAO,EAAEC,GAAG,EAAE;EACzE,MAAMrD,KAAK,GAAG3B,aAAa,CAAC;IAC1B2B,KAAK,EAAEoD,OAAO;IACdvD,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFyD,SAAS;MACTpE,KAAK,GAAG,SAAS;MACjBF,IAAI,GAAG,KAAK;MACZC,IAAI,GAAG,QAAQ;MACfsE;IACF,CAAC,GAAGvD,KAAK;IACTwD,KAAK,GAAGjG,6BAA6B,CAACyC,KAAK,EAAEvC,SAAS,CAAC;EACzD,MAAMqB,UAAU,GAAGtB,QAAQ,CAAC,CAAC,CAAC,EAAEwC,KAAK,EAAE;IACrCd,KAAK;IACLF,IAAI;IACJC;EACF,CAAC,CAAC;EACF,MAAMF,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAM2E,IAAI,GAAG,aAAa/E,IAAI,CAACsE,WAAW,EAAE;IAC1CM,SAAS,EAAEvE,OAAO,CAACS,KAAK;IACxBV,UAAU,EAAEA;EACd,CAAC,CAAC;EACF,OAAO,aAAaF,KAAK,CAACgB,UAAU,EAAE;IACpC0D,SAAS,EAAE1F,IAAI,CAACmB,OAAO,CAACO,IAAI,EAAEgE,SAAS,CAAC;IACxCC,EAAE,EAAEA,EAAE;IACNzE,UAAU,EAAEA,UAAU;IACtB4E,QAAQ,EAAE,CAAC,aAAahF,IAAI,CAACsC,gBAAgB,EAAExD,QAAQ,CAAC;MACtDmG,IAAI,EAAE,UAAU;MAChBF,IAAI,EAAEA,IAAI;MACVG,WAAW,EAAEH,IAAI;MACjBJ,GAAG,EAAEA,GAAG;MACRvE,UAAU,EAAEA;IACd,CAAC,EAAE0E,KAAK,EAAE;MACRzE,OAAO,EAAEvB,QAAQ,CAAC,CAAC,CAAC,EAAEuB,OAAO,EAAE;QAC7BO,IAAI,EAAEP,OAAO,CAACQ;MAChB,CAAC;IACH,CAAC,CAAC,CAAC,EAAE,aAAab,IAAI,CAACiE,WAAW,EAAE;MAClCW,SAAS,EAAEvE,OAAO,CAACU,KAAK;MACxBX,UAAU,EAAEA;IACd,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC;AACF+E,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGzC,MAAM,CAAC0C,SAAS,CAAC,yBAAyB;EAChF;EACA;EACA;EACA;EACA;AACF;AACA;EACE7E,OAAO,EAAExB,SAAS,CAACsG,IAAI;EACvB;AACF;AACA;EACEL,WAAW,EAAEjG,SAAS,CAACuG,IAAI;EAC3B;AACF;AACA;EACEnF,OAAO,EAAEpB,SAAS,CAACwG,MAAM;EACzB;AACF;AACA;EACEb,SAAS,EAAE3F,SAAS,CAACyG,MAAM;EAC3B;AACF;AACA;AACA;AACA;AACA;EACElF,KAAK,EAAEvB,SAAS,CAAC,sCAAsC0G,SAAS,CAAC,CAAC1G,SAAS,CAAC2G,KAAK,CAAC,CAAC,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,EAAE3G,SAAS,CAACyG,MAAM,CAAC,CAAC;EACjL;AACF;AACA;EACEG,cAAc,EAAE5G,SAAS,CAACsG,IAAI;EAC9B;AACF;AACA;EACE7E,QAAQ,EAAEzB,SAAS,CAACsG,IAAI;EACxB;AACF;AACA;AACA;EACEO,aAAa,EAAE7G,SAAS,CAACsG,IAAI;EAC7B;AACF;AACA;AACA;AACA;AACA;AACA;EACEjF,IAAI,EAAErB,SAAS,CAAC2G,KAAK,CAAC,CAAC,KAAK,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;EAC9C;AACF;AACA;EACEb,IAAI,EAAE9F,SAAS,CAACuG,IAAI;EACpB;AACF;AACA;EACEO,EAAE,EAAE9G,SAAS,CAACyG,MAAM;EACpB;AACF;AACA;EACEM,UAAU,EAAE/G,SAAS,CAACwG,MAAM;EAC5B;AACF;AACA;EACEQ,QAAQ,EAAE9G,OAAO;EACjB;AACF;AACA;AACA;AACA;AACA;AACA;EACE+G,QAAQ,EAAEjH,SAAS,CAACkH,IAAI;EACxB;AACF;AACA;AACA;EACEC,QAAQ,EAAEnH,SAAS,CAACsG,IAAI;EACxB;AACF;AACA;AACA;AACA;EACEhF,IAAI,EAAEtB,SAAS,CAAC,sCAAsC0G,SAAS,CAAC,CAAC1G,SAAS,CAAC2G,KAAK,CAAC,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,EAAE3G,SAAS,CAACyG,MAAM,CAAC,CAAC;EACzH;AACF;AACA;EACEb,EAAE,EAAE5F,SAAS,CAAC0G,SAAS,CAAC,CAAC1G,SAAS,CAACoH,OAAO,CAACpH,SAAS,CAAC0G,SAAS,CAAC,CAAC1G,SAAS,CAACkH,IAAI,EAAElH,SAAS,CAACwG,MAAM,EAAExG,SAAS,CAACsG,IAAI,CAAC,CAAC,CAAC,EAAEtG,SAAS,CAACkH,IAAI,EAAElH,SAAS,CAACwG,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;AACA;EACEa,KAAK,EAAErH,SAAS,CAACsH;AACnB,CAAC,GAAG,KAAK,CAAC;AACV,eAAe3D,MAAM"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}