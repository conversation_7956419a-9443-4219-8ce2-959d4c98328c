{"ast": null, "code": "export { FocusTrap } from './FocusTrap';\nexport * from './FocusTrap.types';", "map": {"version": 3, "names": ["FocusTrap"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/base/FocusTrap/index.js"], "sourcesContent": ["export { FocusTrap } from './FocusTrap';\nexport * from './FocusTrap.types';"], "mappings": "AAAA,SAASA,SAAS,QAAQ,aAAa;AACvC,cAAc,mBAAmB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}