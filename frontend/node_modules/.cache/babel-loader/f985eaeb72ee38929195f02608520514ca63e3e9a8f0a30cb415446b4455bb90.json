{"ast": null, "code": "'use client';\n\nexport { default } from './useEnhancedEffect';", "map": {"version": 3, "names": ["default"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/utils/esm/useEnhancedEffect/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './useEnhancedEffect';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,qBAAqB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}