{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/src/data-form.js\",\n  _s = $RefreshSig$();\nimport { useState } from 'react';\nimport { Box, TextField, Button } from '@mui/material';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst endpointMapping = {\n  'Notion': 'notion',\n  'Airtable': 'airtable',\n  'HubSpot': 'hubspot'\n};\nexport const DataForm = ({\n  integrationType,\n  credentials\n}) => {\n  _s();\n  const [loadedData, setLoadedData] = useState(null);\n  const endpoint = endpointMapping[integrationType];\n  const formatLoadedData = data => {\n    if (!data || !Array.isArray(data)) {\n      return 'No data available';\n    }\n    return data.map((item, index) => {\n      const name = item.name || 'Unnamed';\n      const type = item.type || 'Unknown';\n      const id = item.id || 'No ID';\n      return `${index + 1}. ${name} (${type}) - ID: ${id}`;\n    }).join('\\n');\n  };\n  const handleLoad = async () => {\n    try {\n      const formData = new FormData();\n      formData.append('credentials', JSON.stringify(credentials));\n      const response = await axios.post(`http://localhost:8000/integrations/${endpoint}/load`, formData);\n      const data = response.data;\n      const formattedData = formatLoadedData(data);\n      setLoadedData(formattedData);\n    } catch (e) {\n      var _e$response, _e$response$data;\n      alert(e === null || e === void 0 ? void 0 : (_e$response = e.response) === null || _e$response === void 0 ? void 0 : (_e$response$data = _e$response.data) === null || _e$response$data === void 0 ? void 0 : _e$response$data.detail);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    display: \"flex\",\n    justifyContent: \"center\",\n    alignItems: \"center\",\n    flexDirection: \"column\",\n    width: \"100%\",\n    children: /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      flexDirection: \"column\",\n      width: \"100%\",\n      children: [/*#__PURE__*/_jsxDEV(TextField, {\n        label: \"Loaded Data\",\n        value: loadedData || '',\n        sx: {\n          mt: 2\n        },\n        InputLabelProps: {\n          shrink: true\n        },\n        disabled: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleLoad,\n        sx: {\n          mt: 2\n        },\n        variant: \"contained\",\n        children: \"Load Data\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => setLoadedData(null),\n        sx: {\n          mt: 1\n        },\n        variant: \"contained\",\n        children: \"Clear Data\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 46,\n    columnNumber: 9\n  }, this);\n};\n_s(DataForm, \"5cDt2cux/nLAg6lHurbYodgFI4w=\");\n_c = DataForm;\nvar _c;\n$RefreshReg$(_c, \"DataForm\");", "map": {"version": 3, "names": ["useState", "Box", "TextField", "<PERSON><PERSON>", "axios", "jsxDEV", "_jsxDEV", "endpointMapping", "DataForm", "integrationType", "credentials", "_s", "loadedData", "setLoadedData", "endpoint", "formatLoadedData", "data", "Array", "isArray", "map", "item", "index", "name", "type", "id", "join", "handleLoad", "formData", "FormData", "append", "JSON", "stringify", "response", "post", "formattedData", "e", "_e$response", "_e$response$data", "alert", "detail", "display", "justifyContent", "alignItems", "flexDirection", "width", "children", "label", "value", "sx", "mt", "InputLabelProps", "shrink", "disabled", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "variant", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/src/data-form.js"], "sourcesContent": ["import { useState } from 'react';\nimport {\n    Box,\n    TextField,\n    Button,\n} from '@mui/material';\nimport axios from 'axios';\n\nconst endpointMapping = {\n    'Notion': 'notion',\n    'Airtable': 'airtable',\n    'HubSpot': 'hubspot',\n};\n\nexport const DataForm = ({ integrationType, credentials }) => {\n    const [loadedData, setLoadedData] = useState(null);\n    const endpoint = endpointMapping[integrationType];\n\n    const formatLoadedData = (data) => {\n        if (!data || !Array.isArray(data)) {\n            return 'No data available';\n        }\n\n        return data.map((item, index) => {\n            const name = item.name || 'Unnamed';\n            const type = item.type || 'Unknown';\n            const id = item.id || 'No ID';\n            return `${index + 1}. ${name} (${type}) - ID: ${id}`;\n        }).join('\\n');\n    };\n\n    const handleLoad = async () => {\n        try {\n            const formData = new FormData();\n            formData.append('credentials', JSON.stringify(credentials));\n            const response = await axios.post(`http://localhost:8000/integrations/${endpoint}/load`, formData);\n            const data = response.data;\n            const formattedData = formatLoadedData(data);\n            setLoadedData(formattedData);\n        } catch (e) {\n            alert(e?.response?.data?.detail);\n        }\n    }\n\n    return (\n        <Box display='flex' justifyContent='center' alignItems='center' flexDirection='column' width='100%'>\n            <Box display='flex' flexDirection='column' width='100%'>\n                <TextField\n                    label=\"Loaded Data\"\n                    value={loadedData || ''}\n                    sx={{mt: 2}}\n                    InputLabelProps={{ shrink: true }}\n                    disabled\n                />\n                <Button\n                    onClick={handleLoad}\n                    sx={{mt: 2}}\n                    variant='contained'\n                >\n                    Load Data\n                </Button>\n                <Button\n                    onClick={() => setLoadedData(null)}\n                    sx={{mt: 1}}\n                    variant='contained'\n                >\n                    Clear Data\n                </Button>\n            </Box>\n        </Box>\n    );\n}\n"], "mappings": ";;AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SACIC,GAAG,EACHC,SAAS,EACTC,MAAM,QACH,eAAe;AACtB,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,eAAe,GAAG;EACpB,QAAQ,EAAE,QAAQ;EAClB,UAAU,EAAE,UAAU;EACtB,SAAS,EAAE;AACf,CAAC;AAED,OAAO,MAAMC,QAAQ,GAAGA,CAAC;EAAEC,eAAe;EAAEC;AAAY,CAAC,KAAK;EAAAC,EAAA;EAC1D,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAMc,QAAQ,GAAGP,eAAe,CAACE,eAAe,CAAC;EAEjD,MAAMM,gBAAgB,GAAIC,IAAI,IAAK;IAC/B,IAAI,CAACA,IAAI,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,IAAI,CAAC,EAAE;MAC/B,OAAO,mBAAmB;IAC9B;IAEA,OAAOA,IAAI,CAACG,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;MAC7B,MAAMC,IAAI,GAAGF,IAAI,CAACE,IAAI,IAAI,SAAS;MACnC,MAAMC,IAAI,GAAGH,IAAI,CAACG,IAAI,IAAI,SAAS;MACnC,MAAMC,EAAE,GAAGJ,IAAI,CAACI,EAAE,IAAI,OAAO;MAC7B,OAAQ,GAAEH,KAAK,GAAG,CAAE,KAAIC,IAAK,KAAIC,IAAK,WAAUC,EAAG,EAAC;IACxD,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;EACjB,CAAC;EAED,MAAMC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACA,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,aAAa,EAAEC,IAAI,CAACC,SAAS,CAACrB,WAAW,CAAC,CAAC;MAC3D,MAAMsB,QAAQ,GAAG,MAAM5B,KAAK,CAAC6B,IAAI,CAAE,sCAAqCnB,QAAS,OAAM,EAAEa,QAAQ,CAAC;MAClG,MAAMX,IAAI,GAAGgB,QAAQ,CAAChB,IAAI;MAC1B,MAAMkB,aAAa,GAAGnB,gBAAgB,CAACC,IAAI,CAAC;MAC5CH,aAAa,CAACqB,aAAa,CAAC;IAChC,CAAC,CAAC,OAAOC,CAAC,EAAE;MAAA,IAAAC,WAAA,EAAAC,gBAAA;MACRC,KAAK,CAACH,CAAC,aAADA,CAAC,wBAAAC,WAAA,GAADD,CAAC,CAAEH,QAAQ,cAAAI,WAAA,wBAAAC,gBAAA,GAAXD,WAAA,CAAapB,IAAI,cAAAqB,gBAAA,uBAAjBA,gBAAA,CAAmBE,MAAM,CAAC;IACpC;EACJ,CAAC;EAED,oBACIjC,OAAA,CAACL,GAAG;IAACuC,OAAO,EAAC,MAAM;IAACC,cAAc,EAAC,QAAQ;IAACC,UAAU,EAAC,QAAQ;IAACC,aAAa,EAAC,QAAQ;IAACC,KAAK,EAAC,MAAM;IAAAC,QAAA,eAC/FvC,OAAA,CAACL,GAAG;MAACuC,OAAO,EAAC,MAAM;MAACG,aAAa,EAAC,QAAQ;MAACC,KAAK,EAAC,MAAM;MAAAC,QAAA,gBACnDvC,OAAA,CAACJ,SAAS;QACN4C,KAAK,EAAC,aAAa;QACnBC,KAAK,EAAEnC,UAAU,IAAI,EAAG;QACxBoC,EAAE,EAAE;UAACC,EAAE,EAAE;QAAC,CAAE;QACZC,eAAe,EAAE;UAAEC,MAAM,EAAE;QAAK,CAAE;QAClCC,QAAQ;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC,eACFlD,OAAA,CAACH,MAAM;QACHsD,OAAO,EAAE/B,UAAW;QACpBsB,EAAE,EAAE;UAACC,EAAE,EAAE;QAAC,CAAE;QACZS,OAAO,EAAC,WAAW;QAAAb,QAAA,EACtB;MAED;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTlD,OAAA,CAACH,MAAM;QACHsD,OAAO,EAAEA,CAAA,KAAM5C,aAAa,CAAC,IAAI,CAAE;QACnCmC,EAAE,EAAE;UAACC,EAAE,EAAE;QAAC,CAAE;QACZS,OAAO,EAAC,WAAW;QAAAb,QAAA,EACtB;MAED;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAA7C,EAAA,CAzDYH,QAAQ;AAAAmD,EAAA,GAARnD,QAAQ;AAAA,IAAAmD,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}