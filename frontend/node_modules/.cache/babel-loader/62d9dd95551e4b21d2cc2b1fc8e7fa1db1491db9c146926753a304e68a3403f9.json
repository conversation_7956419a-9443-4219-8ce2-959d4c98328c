{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"children\", \"disabled\", \"label\", \"slots\", \"slotProps\", \"focusableWhenDisabled\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { useSlotProps } from '../utils';\nimport { useMenuButton } from '../useMenuButton';\nimport { unstable_composeClasses as composeClasses } from '../composeClasses';\nimport { useClassNamesOverride } from '../utils/ClassNameConfigurator';\nimport { getMenuButtonUtilityClass } from './menuButtonClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    active,\n    disabled,\n    open\n  } = ownerState;\n  const slots = {\n    root: ['root', disabled && 'disabled', active && 'active', open && 'expanded']\n  };\n  return composeClasses(slots, useClassNamesOverride(getMenuButtonUtilityClass));\n};\n\n/**\n *\n * Demos:\n *\n * - [Menu](https://mui.com/base-ui/react-menu/)\n *\n * API:\n *\n * - [MenuButton API](https://mui.com/base-ui/react-menu/components-api/#menu-button)\n */\nconst MenuButton = /*#__PURE__*/React.forwardRef(function MenuButton(props, forwardedRef) {\n  const {\n      children,\n      disabled = false,\n      slots = {},\n      slotProps = {},\n      focusableWhenDisabled = false\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    getRootProps,\n    open,\n    active\n  } = useMenuButton({\n    disabled,\n    focusableWhenDisabled,\n    rootRef: forwardedRef\n  });\n  const ownerState = _extends({}, props, {\n    open,\n    active,\n    disabled,\n    focusableWhenDisabled\n  });\n  const classes = useUtilityClasses(ownerState);\n  const Root = slots.root || 'button';\n  const rootProps = useSlotProps({\n    elementType: Root,\n    getSlotProps: getRootProps,\n    externalForwardedProps: other,\n    externalSlotProps: slotProps.root,\n    additionalProps: {\n      ref: forwardedRef,\n      type: 'button'\n    },\n    ownerState,\n    className: classes.root\n  });\n  return /*#__PURE__*/_jsx(Root, _extends({}, rootProps, {\n    children: children\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? MenuButton.propTypes /* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * Class name applied to the root element.\n   */\n  className: PropTypes.string,\n  /**\n   * If `true`, the component is disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, allows a disabled button to receive focus.\n   * @default false\n   */\n  focusableWhenDisabled: PropTypes.bool,\n  /**\n   * Label of the button\n   */\n  label: PropTypes.string,\n  /**\n   * The components used for each slot inside the MenuButton.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The props used for each slot inside the MenuButton.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    root: PropTypes.elementType\n  })\n} : void 0;\nexport { MenuButton };", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "PropTypes", "useSlotProps", "useMenuButton", "unstable_composeClasses", "composeClasses", "useClassNamesOverride", "getMenuButtonUtilityClass", "jsx", "_jsx", "useUtilityClasses", "ownerState", "active", "disabled", "open", "slots", "root", "MenuButton", "forwardRef", "props", "forwardedRef", "children", "slotProps", "focusableWhenDisabled", "other", "getRootProps", "rootRef", "classes", "Root", "rootProps", "elementType", "getSlotProps", "externalForwardedProps", "externalSlotProps", "additionalProps", "ref", "type", "className", "process", "env", "NODE_ENV", "propTypes", "node", "string", "bool", "label", "shape", "oneOfType", "func", "object"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/base/MenuButton/MenuButton.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"children\", \"disabled\", \"label\", \"slots\", \"slotProps\", \"focusableWhenDisabled\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { useSlotProps } from '../utils';\nimport { useMenuButton } from '../useMenuButton';\nimport { unstable_composeClasses as composeClasses } from '../composeClasses';\nimport { useClassNamesOverride } from '../utils/ClassNameConfigurator';\nimport { getMenuButtonUtilityClass } from './menuButtonClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    active,\n    disabled,\n    open\n  } = ownerState;\n  const slots = {\n    root: ['root', disabled && 'disabled', active && 'active', open && 'expanded']\n  };\n  return composeClasses(slots, useClassNamesOverride(getMenuButtonUtilityClass));\n};\n\n/**\n *\n * Demos:\n *\n * - [Menu](https://mui.com/base-ui/react-menu/)\n *\n * API:\n *\n * - [MenuButton API](https://mui.com/base-ui/react-menu/components-api/#menu-button)\n */\nconst MenuButton = /*#__PURE__*/React.forwardRef(function MenuButton(props, forwardedRef) {\n  const {\n      children,\n      disabled = false,\n      slots = {},\n      slotProps = {},\n      focusableWhenDisabled = false\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    getRootProps,\n    open,\n    active\n  } = useMenuButton({\n    disabled,\n    focusableWhenDisabled,\n    rootRef: forwardedRef\n  });\n  const ownerState = _extends({}, props, {\n    open,\n    active,\n    disabled,\n    focusableWhenDisabled\n  });\n  const classes = useUtilityClasses(ownerState);\n  const Root = slots.root || 'button';\n  const rootProps = useSlotProps({\n    elementType: Root,\n    getSlotProps: getRootProps,\n    externalForwardedProps: other,\n    externalSlotProps: slotProps.root,\n    additionalProps: {\n      ref: forwardedRef,\n      type: 'button'\n    },\n    ownerState,\n    className: classes.root\n  });\n  return /*#__PURE__*/_jsx(Root, _extends({}, rootProps, {\n    children: children\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? MenuButton.propTypes /* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * Class name applied to the root element.\n   */\n  className: PropTypes.string,\n  /**\n   * If `true`, the component is disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, allows a disabled button to receive focus.\n   * @default false\n   */\n  focusableWhenDisabled: PropTypes.bool,\n  /**\n   * Label of the button\n   */\n  label: PropTypes.string,\n  /**\n   * The components used for each slot inside the MenuButton.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The props used for each slot inside the MenuButton.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    root: PropTypes.elementType\n  })\n} : void 0;\nexport { MenuButton };"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,UAAU,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,WAAW,EAAE,uBAAuB,CAAC;AAClG,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,YAAY,QAAQ,UAAU;AACvC,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,uBAAuB,IAAIC,cAAc,QAAQ,mBAAmB;AAC7E,SAASC,qBAAqB,QAAQ,gCAAgC;AACtE,SAASC,yBAAyB,QAAQ,qBAAqB;AAC/D,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,MAAM;IACNC,QAAQ;IACRC;EACF,CAAC,GAAGH,UAAU;EACd,MAAMI,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEH,QAAQ,IAAI,UAAU,EAAED,MAAM,IAAI,QAAQ,EAAEE,IAAI,IAAI,UAAU;EAC/E,CAAC;EACD,OAAOT,cAAc,CAACU,KAAK,EAAET,qBAAqB,CAACC,yBAAyB,CAAC,CAAC;AAChF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMU,UAAU,GAAG,aAAajB,KAAK,CAACkB,UAAU,CAAC,SAASD,UAAUA,CAACE,KAAK,EAAEC,YAAY,EAAE;EACxF,MAAM;MACFC,QAAQ;MACRR,QAAQ,GAAG,KAAK;MAChBE,KAAK,GAAG,CAAC,CAAC;MACVO,SAAS,GAAG,CAAC,CAAC;MACdC,qBAAqB,GAAG;IAC1B,CAAC,GAAGJ,KAAK;IACTK,KAAK,GAAG1B,6BAA6B,CAACqB,KAAK,EAAEpB,SAAS,CAAC;EACzD,MAAM;IACJ0B,YAAY;IACZX,IAAI;IACJF;EACF,CAAC,GAAGT,aAAa,CAAC;IAChBU,QAAQ;IACRU,qBAAqB;IACrBG,OAAO,EAAEN;EACX,CAAC,CAAC;EACF,MAAMT,UAAU,GAAGd,QAAQ,CAAC,CAAC,CAAC,EAAEsB,KAAK,EAAE;IACrCL,IAAI;IACJF,MAAM;IACNC,QAAQ;IACRU;EACF,CAAC,CAAC;EACF,MAAMI,OAAO,GAAGjB,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAMiB,IAAI,GAAGb,KAAK,CAACC,IAAI,IAAI,QAAQ;EACnC,MAAMa,SAAS,GAAG3B,YAAY,CAAC;IAC7B4B,WAAW,EAAEF,IAAI;IACjBG,YAAY,EAAEN,YAAY;IAC1BO,sBAAsB,EAAER,KAAK;IAC7BS,iBAAiB,EAAEX,SAAS,CAACN,IAAI;IACjCkB,eAAe,EAAE;MACfC,GAAG,EAAEf,YAAY;MACjBgB,IAAI,EAAE;IACR,CAAC;IACDzB,UAAU;IACV0B,SAAS,EAAEV,OAAO,CAACX;EACrB,CAAC,CAAC;EACF,OAAO,aAAaP,IAAI,CAACmB,IAAI,EAAE/B,QAAQ,CAAC,CAAC,CAAC,EAAEgC,SAAS,EAAE;IACrDR,QAAQ,EAAEA;EACZ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFiB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGvB,UAAU,CAACwB,SAAS,CAAC,yBAAyB;EACpF;EACA;EACA;EACA;EACA;AACF;AACA;EACEpB,QAAQ,EAAEpB,SAAS,CAACyC,IAAI;EACxB;AACF;AACA;EACEL,SAAS,EAAEpC,SAAS,CAAC0C,MAAM;EAC3B;AACF;AACA;AACA;EACE9B,QAAQ,EAAEZ,SAAS,CAAC2C,IAAI;EACxB;AACF;AACA;AACA;EACErB,qBAAqB,EAAEtB,SAAS,CAAC2C,IAAI;EACrC;AACF;AACA;EACEC,KAAK,EAAE5C,SAAS,CAAC0C,MAAM;EACvB;AACF;AACA;AACA;AACA;EACErB,SAAS,EAAErB,SAAS,CAAC6C,KAAK,CAAC;IACzB9B,IAAI,EAAEf,SAAS,CAAC8C,SAAS,CAAC,CAAC9C,SAAS,CAAC+C,IAAI,EAAE/C,SAAS,CAACgD,MAAM,CAAC;EAC9D,CAAC,CAAC;EACF;AACF;AACA;AACA;EACElC,KAAK,EAAEd,SAAS,CAAC6C,KAAK,CAAC;IACrB9B,IAAI,EAAEf,SAAS,CAAC6B;EAClB,CAAC;AACH,CAAC,GAAG,KAAK,CAAC;AACV,SAASb,UAAU"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}