{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"arrow\", \"children\", \"classes\", \"components\", \"componentsProps\", \"describeChild\", \"disableFocusListener\", \"disableHoverListener\", \"disableInteractive\", \"disableTouchListener\", \"enterDelay\", \"enterNextDelay\", \"enterTouchDelay\", \"followCursor\", \"id\", \"leaveDelay\", \"leaveTouchDelay\", \"onClose\", \"onOpen\", \"open\", \"placement\", \"PopperComponent\", \"PopperProps\", \"slotProps\", \"slots\", \"title\", \"TransitionComponent\", \"TransitionProps\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { elementAcceptingRef } from '@mui/utils';\nimport { unstable_composeClasses as composeClasses, appendOwnerState } from '@mui/base';\nimport { alpha } from '@mui/system';\nimport styled from '../styles/styled';\nimport useTheme from '../styles/useTheme';\nimport useThemeProps from '../styles/useThemeProps';\nimport capitalize from '../utils/capitalize';\nimport Grow from '../Grow';\nimport Popper from '../Popper';\nimport useEventCallback from '../utils/useEventCallback';\nimport useForkRef from '../utils/useForkRef';\nimport useId from '../utils/useId';\nimport useIsFocusVisible from '../utils/useIsFocusVisible';\nimport useControlled from '../utils/useControlled';\nimport tooltipClasses, { getTooltipUtilityClass } from './tooltipClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nfunction round(value) {\n  return Math.round(value * 1e5) / 1e5;\n}\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    disableInteractive,\n    arrow,\n    touch,\n    placement\n  } = ownerState;\n  const slots = {\n    popper: ['popper', !disableInteractive && 'popperInteractive', arrow && 'popperArrow'],\n    tooltip: ['tooltip', arrow && 'tooltipArrow', touch && 'touch', `tooltipPlacement${capitalize(placement.split('-')[0])}`],\n    arrow: ['arrow']\n  };\n  return composeClasses(slots, getTooltipUtilityClass, classes);\n};\nconst TooltipPopper = styled(Popper, {\n  name: 'MuiTooltip',\n  slot: 'Popper',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.popper, !ownerState.disableInteractive && styles.popperInteractive, ownerState.arrow && styles.popperArrow, !ownerState.open && styles.popperClose];\n  }\n})(({\n  theme,\n  ownerState,\n  open\n}) => _extends({\n  zIndex: (theme.vars || theme).zIndex.tooltip,\n  pointerEvents: 'none'\n}, !ownerState.disableInteractive && {\n  pointerEvents: 'auto'\n}, !open && {\n  pointerEvents: 'none'\n}, ownerState.arrow && {\n  [`&[data-popper-placement*=\"bottom\"] .${tooltipClasses.arrow}`]: {\n    top: 0,\n    marginTop: '-0.71em',\n    '&::before': {\n      transformOrigin: '0 100%'\n    }\n  },\n  [`&[data-popper-placement*=\"top\"] .${tooltipClasses.arrow}`]: {\n    bottom: 0,\n    marginBottom: '-0.71em',\n    '&::before': {\n      transformOrigin: '100% 0'\n    }\n  },\n  [`&[data-popper-placement*=\"right\"] .${tooltipClasses.arrow}`]: _extends({}, !ownerState.isRtl ? {\n    left: 0,\n    marginLeft: '-0.71em'\n  } : {\n    right: 0,\n    marginRight: '-0.71em'\n  }, {\n    height: '1em',\n    width: '0.71em',\n    '&::before': {\n      transformOrigin: '100% 100%'\n    }\n  }),\n  [`&[data-popper-placement*=\"left\"] .${tooltipClasses.arrow}`]: _extends({}, !ownerState.isRtl ? {\n    right: 0,\n    marginRight: '-0.71em'\n  } : {\n    left: 0,\n    marginLeft: '-0.71em'\n  }, {\n    height: '1em',\n    width: '0.71em',\n    '&::before': {\n      transformOrigin: '0 0'\n    }\n  })\n}));\nconst TooltipTooltip = styled('div', {\n  name: 'MuiTooltip',\n  slot: 'Tooltip',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.tooltip, ownerState.touch && styles.touch, ownerState.arrow && styles.tooltipArrow, styles[`tooltipPlacement${capitalize(ownerState.placement.split('-')[0])}`]];\n  }\n})(({\n  theme,\n  ownerState\n}) => _extends({\n  backgroundColor: theme.vars ? theme.vars.palette.Tooltip.bg : alpha(theme.palette.grey[700], 0.92),\n  borderRadius: (theme.vars || theme).shape.borderRadius,\n  color: (theme.vars || theme).palette.common.white,\n  fontFamily: theme.typography.fontFamily,\n  padding: '4px 8px',\n  fontSize: theme.typography.pxToRem(11),\n  maxWidth: 300,\n  margin: 2,\n  wordWrap: 'break-word',\n  fontWeight: theme.typography.fontWeightMedium\n}, ownerState.arrow && {\n  position: 'relative',\n  margin: 0\n}, ownerState.touch && {\n  padding: '8px 16px',\n  fontSize: theme.typography.pxToRem(14),\n  lineHeight: `${round(16 / 14)}em`,\n  fontWeight: theme.typography.fontWeightRegular\n}, {\n  [`.${tooltipClasses.popper}[data-popper-placement*=\"left\"] &`]: _extends({\n    transformOrigin: 'right center'\n  }, !ownerState.isRtl ? _extends({\n    marginRight: '14px'\n  }, ownerState.touch && {\n    marginRight: '24px'\n  }) : _extends({\n    marginLeft: '14px'\n  }, ownerState.touch && {\n    marginLeft: '24px'\n  })),\n  [`.${tooltipClasses.popper}[data-popper-placement*=\"right\"] &`]: _extends({\n    transformOrigin: 'left center'\n  }, !ownerState.isRtl ? _extends({\n    marginLeft: '14px'\n  }, ownerState.touch && {\n    marginLeft: '24px'\n  }) : _extends({\n    marginRight: '14px'\n  }, ownerState.touch && {\n    marginRight: '24px'\n  })),\n  [`.${tooltipClasses.popper}[data-popper-placement*=\"top\"] &`]: _extends({\n    transformOrigin: 'center bottom',\n    marginBottom: '14px'\n  }, ownerState.touch && {\n    marginBottom: '24px'\n  }),\n  [`.${tooltipClasses.popper}[data-popper-placement*=\"bottom\"] &`]: _extends({\n    transformOrigin: 'center top',\n    marginTop: '14px'\n  }, ownerState.touch && {\n    marginTop: '24px'\n  })\n}));\nconst TooltipArrow = styled('span', {\n  name: 'MuiTooltip',\n  slot: 'Arrow',\n  overridesResolver: (props, styles) => styles.arrow\n})(({\n  theme\n}) => ({\n  overflow: 'hidden',\n  position: 'absolute',\n  width: '1em',\n  height: '0.71em' /* = width / sqrt(2) = (length of the hypotenuse) */,\n  boxSizing: 'border-box',\n  color: theme.vars ? theme.vars.palette.Tooltip.bg : alpha(theme.palette.grey[700], 0.9),\n  '&::before': {\n    content: '\"\"',\n    margin: 'auto',\n    display: 'block',\n    width: '100%',\n    height: '100%',\n    backgroundColor: 'currentColor',\n    transform: 'rotate(45deg)'\n  }\n}));\nlet hystersisOpen = false;\nlet hystersisTimer = null;\nlet cursorPosition = {\n  x: 0,\n  y: 0\n};\nexport function testReset() {\n  hystersisOpen = false;\n  clearTimeout(hystersisTimer);\n}\nfunction composeEventHandler(handler, eventHandler) {\n  return event => {\n    if (eventHandler) {\n      eventHandler(event);\n    }\n    handler(event);\n  };\n}\n\n// TODO v6: Remove PopperComponent, PopperProps, TransitionComponent and TransitionProps.\nconst Tooltip = /*#__PURE__*/React.forwardRef(function Tooltip(inProps, ref) {\n  var _ref, _slots$popper, _ref2, _ref3, _slots$transition, _ref4, _slots$tooltip, _ref5, _slots$arrow, _slotProps$popper, _ref6, _slotProps$popper2, _slotProps$transition, _slotProps$tooltip, _ref7, _slotProps$tooltip2, _slotProps$arrow, _ref8, _slotProps$arrow2;\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiTooltip'\n  });\n  const {\n      arrow = false,\n      children: childrenProp,\n      components = {},\n      componentsProps = {},\n      describeChild = false,\n      disableFocusListener = false,\n      disableHoverListener = false,\n      disableInteractive: disableInteractiveProp = false,\n      disableTouchListener = false,\n      enterDelay = 100,\n      enterNextDelay = 0,\n      enterTouchDelay = 700,\n      followCursor = false,\n      id: idProp,\n      leaveDelay = 0,\n      leaveTouchDelay = 1500,\n      onClose,\n      onOpen,\n      open: openProp,\n      placement = 'bottom',\n      PopperComponent: PopperComponentProp,\n      PopperProps = {},\n      slotProps = {},\n      slots = {},\n      title,\n      TransitionComponent: TransitionComponentProp = Grow,\n      TransitionProps\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n\n  // to prevent runtime errors, developers will need to provide a child as a React element anyway.\n  const children = /*#__PURE__*/React.isValidElement(childrenProp) ? childrenProp : /*#__PURE__*/_jsx(\"span\", {\n    children: childrenProp\n  });\n  const theme = useTheme();\n  const isRtl = theme.direction === 'rtl';\n  const [childNode, setChildNode] = React.useState();\n  const [arrowRef, setArrowRef] = React.useState(null);\n  const ignoreNonTouchEvents = React.useRef(false);\n  const disableInteractive = disableInteractiveProp || followCursor;\n  const closeTimer = React.useRef();\n  const enterTimer = React.useRef();\n  const leaveTimer = React.useRef();\n  const touchTimer = React.useRef();\n  const [openState, setOpenState] = useControlled({\n    controlled: openProp,\n    default: false,\n    name: 'Tooltip',\n    state: 'open'\n  });\n  let open = openState;\n  if (process.env.NODE_ENV !== 'production') {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    const {\n      current: isControlled\n    } = React.useRef(openProp !== undefined);\n\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useEffect(() => {\n      if (childNode && childNode.disabled && !isControlled && title !== '' && childNode.tagName.toLowerCase() === 'button') {\n        console.error(['MUI: You are providing a disabled `button` child to the Tooltip component.', 'A disabled element does not fire events.', \"Tooltip needs to listen to the child element's events to display the title.\", '', 'Add a simple wrapper element, such as a `span`.'].join('\\n'));\n      }\n    }, [title, childNode, isControlled]);\n  }\n  const id = useId(idProp);\n  const prevUserSelect = React.useRef();\n  const stopTouchInteraction = React.useCallback(() => {\n    if (prevUserSelect.current !== undefined) {\n      document.body.style.WebkitUserSelect = prevUserSelect.current;\n      prevUserSelect.current = undefined;\n    }\n    clearTimeout(touchTimer.current);\n  }, []);\n  React.useEffect(() => {\n    return () => {\n      clearTimeout(closeTimer.current);\n      clearTimeout(enterTimer.current);\n      clearTimeout(leaveTimer.current);\n      stopTouchInteraction();\n    };\n  }, [stopTouchInteraction]);\n  const handleOpen = event => {\n    clearTimeout(hystersisTimer);\n    hystersisOpen = true;\n\n    // The mouseover event will trigger for every nested element in the tooltip.\n    // We can skip rerendering when the tooltip is already open.\n    // We are using the mouseover event instead of the mouseenter event to fix a hide/show issue.\n    setOpenState(true);\n    if (onOpen && !open) {\n      onOpen(event);\n    }\n  };\n  const handleClose = useEventCallback(\n  /**\n   * @param {React.SyntheticEvent | Event} event\n   */\n  event => {\n    clearTimeout(hystersisTimer);\n    hystersisTimer = setTimeout(() => {\n      hystersisOpen = false;\n    }, 800 + leaveDelay);\n    setOpenState(false);\n    if (onClose && open) {\n      onClose(event);\n    }\n    clearTimeout(closeTimer.current);\n    closeTimer.current = setTimeout(() => {\n      ignoreNonTouchEvents.current = false;\n    }, theme.transitions.duration.shortest);\n  });\n  const handleEnter = event => {\n    if (ignoreNonTouchEvents.current && event.type !== 'touchstart') {\n      return;\n    }\n\n    // Remove the title ahead of time.\n    // We don't want to wait for the next render commit.\n    // We would risk displaying two tooltips at the same time (native + this one).\n    if (childNode) {\n      childNode.removeAttribute('title');\n    }\n    clearTimeout(enterTimer.current);\n    clearTimeout(leaveTimer.current);\n    if (enterDelay || hystersisOpen && enterNextDelay) {\n      enterTimer.current = setTimeout(() => {\n        handleOpen(event);\n      }, hystersisOpen ? enterNextDelay : enterDelay);\n    } else {\n      handleOpen(event);\n    }\n  };\n  const handleLeave = event => {\n    clearTimeout(enterTimer.current);\n    clearTimeout(leaveTimer.current);\n    leaveTimer.current = setTimeout(() => {\n      handleClose(event);\n    }, leaveDelay);\n  };\n  const {\n    isFocusVisibleRef,\n    onBlur: handleBlurVisible,\n    onFocus: handleFocusVisible,\n    ref: focusVisibleRef\n  } = useIsFocusVisible();\n  // We don't necessarily care about the focusVisible state (which is safe to access via ref anyway).\n  // We just need to re-render the Tooltip if the focus-visible state changes.\n  const [, setChildIsFocusVisible] = React.useState(false);\n  const handleBlur = event => {\n    handleBlurVisible(event);\n    if (isFocusVisibleRef.current === false) {\n      setChildIsFocusVisible(false);\n      handleLeave(event);\n    }\n  };\n  const handleFocus = event => {\n    // Workaround for https://github.com/facebook/react/issues/7769\n    // The autoFocus of React might trigger the event before the componentDidMount.\n    // We need to account for this eventuality.\n    if (!childNode) {\n      setChildNode(event.currentTarget);\n    }\n    handleFocusVisible(event);\n    if (isFocusVisibleRef.current === true) {\n      setChildIsFocusVisible(true);\n      handleEnter(event);\n    }\n  };\n  const detectTouchStart = event => {\n    ignoreNonTouchEvents.current = true;\n    const childrenProps = children.props;\n    if (childrenProps.onTouchStart) {\n      childrenProps.onTouchStart(event);\n    }\n  };\n  const handleMouseOver = handleEnter;\n  const handleMouseLeave = handleLeave;\n  const handleTouchStart = event => {\n    detectTouchStart(event);\n    clearTimeout(leaveTimer.current);\n    clearTimeout(closeTimer.current);\n    stopTouchInteraction();\n    prevUserSelect.current = document.body.style.WebkitUserSelect;\n    // Prevent iOS text selection on long-tap.\n    document.body.style.WebkitUserSelect = 'none';\n    touchTimer.current = setTimeout(() => {\n      document.body.style.WebkitUserSelect = prevUserSelect.current;\n      handleEnter(event);\n    }, enterTouchDelay);\n  };\n  const handleTouchEnd = event => {\n    if (children.props.onTouchEnd) {\n      children.props.onTouchEnd(event);\n    }\n    stopTouchInteraction();\n    clearTimeout(leaveTimer.current);\n    leaveTimer.current = setTimeout(() => {\n      handleClose(event);\n    }, leaveTouchDelay);\n  };\n  React.useEffect(() => {\n    if (!open) {\n      return undefined;\n    }\n\n    /**\n     * @param {KeyboardEvent} nativeEvent\n     */\n    function handleKeyDown(nativeEvent) {\n      // IE11, Edge (prior to using Bink?) use 'Esc'\n      if (nativeEvent.key === 'Escape' || nativeEvent.key === 'Esc') {\n        handleClose(nativeEvent);\n      }\n    }\n    document.addEventListener('keydown', handleKeyDown);\n    return () => {\n      document.removeEventListener('keydown', handleKeyDown);\n    };\n  }, [handleClose, open]);\n  const handleRef = useForkRef(children.ref, focusVisibleRef, setChildNode, ref);\n\n  // There is no point in displaying an empty tooltip.\n  if (!title && title !== 0) {\n    open = false;\n  }\n  const popperRef = React.useRef();\n  const handleMouseMove = event => {\n    const childrenProps = children.props;\n    if (childrenProps.onMouseMove) {\n      childrenProps.onMouseMove(event);\n    }\n    cursorPosition = {\n      x: event.clientX,\n      y: event.clientY\n    };\n    if (popperRef.current) {\n      popperRef.current.update();\n    }\n  };\n  const nameOrDescProps = {};\n  const titleIsString = typeof title === 'string';\n  if (describeChild) {\n    nameOrDescProps.title = !open && titleIsString && !disableHoverListener ? title : null;\n    nameOrDescProps['aria-describedby'] = open ? id : null;\n  } else {\n    nameOrDescProps['aria-label'] = titleIsString ? title : null;\n    nameOrDescProps['aria-labelledby'] = open && !titleIsString ? id : null;\n  }\n  const childrenProps = _extends({}, nameOrDescProps, other, children.props, {\n    className: clsx(other.className, children.props.className),\n    onTouchStart: detectTouchStart,\n    ref: handleRef\n  }, followCursor ? {\n    onMouseMove: handleMouseMove\n  } : {});\n  if (process.env.NODE_ENV !== 'production') {\n    childrenProps['data-mui-internal-clone-element'] = true;\n\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useEffect(() => {\n      if (childNode && !childNode.getAttribute('data-mui-internal-clone-element')) {\n        console.error(['MUI: The `children` component of the Tooltip is not forwarding its props correctly.', 'Please make sure that props are spread on the same element that the ref is applied to.'].join('\\n'));\n      }\n    }, [childNode]);\n  }\n  const interactiveWrapperListeners = {};\n  if (!disableTouchListener) {\n    childrenProps.onTouchStart = handleTouchStart;\n    childrenProps.onTouchEnd = handleTouchEnd;\n  }\n  if (!disableHoverListener) {\n    childrenProps.onMouseOver = composeEventHandler(handleMouseOver, childrenProps.onMouseOver);\n    childrenProps.onMouseLeave = composeEventHandler(handleMouseLeave, childrenProps.onMouseLeave);\n    if (!disableInteractive) {\n      interactiveWrapperListeners.onMouseOver = handleMouseOver;\n      interactiveWrapperListeners.onMouseLeave = handleMouseLeave;\n    }\n  }\n  if (!disableFocusListener) {\n    childrenProps.onFocus = composeEventHandler(handleFocus, childrenProps.onFocus);\n    childrenProps.onBlur = composeEventHandler(handleBlur, childrenProps.onBlur);\n    if (!disableInteractive) {\n      interactiveWrapperListeners.onFocus = handleFocus;\n      interactiveWrapperListeners.onBlur = handleBlur;\n    }\n  }\n  if (process.env.NODE_ENV !== 'production') {\n    if (children.props.title) {\n      console.error(['MUI: You have provided a `title` prop to the child of <Tooltip />.', `Remove this title prop \\`${children.props.title}\\` or the Tooltip component.`].join('\\n'));\n    }\n  }\n  const popperOptions = React.useMemo(() => {\n    var _PopperProps$popperOp;\n    let tooltipModifiers = [{\n      name: 'arrow',\n      enabled: Boolean(arrowRef),\n      options: {\n        element: arrowRef,\n        padding: 4\n      }\n    }];\n    if ((_PopperProps$popperOp = PopperProps.popperOptions) != null && _PopperProps$popperOp.modifiers) {\n      tooltipModifiers = tooltipModifiers.concat(PopperProps.popperOptions.modifiers);\n    }\n    return _extends({}, PopperProps.popperOptions, {\n      modifiers: tooltipModifiers\n    });\n  }, [arrowRef, PopperProps]);\n  const ownerState = _extends({}, props, {\n    isRtl,\n    arrow,\n    disableInteractive,\n    placement,\n    PopperComponentProp,\n    touch: ignoreNonTouchEvents.current\n  });\n  const classes = useUtilityClasses(ownerState);\n  const PopperComponent = (_ref = (_slots$popper = slots.popper) != null ? _slots$popper : components.Popper) != null ? _ref : TooltipPopper;\n  const TransitionComponent = (_ref2 = (_ref3 = (_slots$transition = slots.transition) != null ? _slots$transition : components.Transition) != null ? _ref3 : TransitionComponentProp) != null ? _ref2 : Grow;\n  const TooltipComponent = (_ref4 = (_slots$tooltip = slots.tooltip) != null ? _slots$tooltip : components.Tooltip) != null ? _ref4 : TooltipTooltip;\n  const ArrowComponent = (_ref5 = (_slots$arrow = slots.arrow) != null ? _slots$arrow : components.Arrow) != null ? _ref5 : TooltipArrow;\n  const popperProps = appendOwnerState(PopperComponent, _extends({}, PopperProps, (_slotProps$popper = slotProps.popper) != null ? _slotProps$popper : componentsProps.popper, {\n    className: clsx(classes.popper, PopperProps == null ? void 0 : PopperProps.className, (_ref6 = (_slotProps$popper2 = slotProps.popper) != null ? _slotProps$popper2 : componentsProps.popper) == null ? void 0 : _ref6.className)\n  }), ownerState);\n  const transitionProps = appendOwnerState(TransitionComponent, _extends({}, TransitionProps, (_slotProps$transition = slotProps.transition) != null ? _slotProps$transition : componentsProps.transition), ownerState);\n  const tooltipProps = appendOwnerState(TooltipComponent, _extends({}, (_slotProps$tooltip = slotProps.tooltip) != null ? _slotProps$tooltip : componentsProps.tooltip, {\n    className: clsx(classes.tooltip, (_ref7 = (_slotProps$tooltip2 = slotProps.tooltip) != null ? _slotProps$tooltip2 : componentsProps.tooltip) == null ? void 0 : _ref7.className)\n  }), ownerState);\n  const tooltipArrowProps = appendOwnerState(ArrowComponent, _extends({}, (_slotProps$arrow = slotProps.arrow) != null ? _slotProps$arrow : componentsProps.arrow, {\n    className: clsx(classes.arrow, (_ref8 = (_slotProps$arrow2 = slotProps.arrow) != null ? _slotProps$arrow2 : componentsProps.arrow) == null ? void 0 : _ref8.className)\n  }), ownerState);\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [/*#__PURE__*/React.cloneElement(children, childrenProps), /*#__PURE__*/_jsx(PopperComponent, _extends({\n      as: PopperComponentProp != null ? PopperComponentProp : Popper,\n      placement: placement,\n      anchorEl: followCursor ? {\n        getBoundingClientRect: () => ({\n          top: cursorPosition.y,\n          left: cursorPosition.x,\n          right: cursorPosition.x,\n          bottom: cursorPosition.y,\n          width: 0,\n          height: 0\n        })\n      } : childNode,\n      popperRef: popperRef,\n      open: childNode ? open : false,\n      id: id,\n      transition: true\n    }, interactiveWrapperListeners, popperProps, {\n      popperOptions: popperOptions,\n      children: ({\n        TransitionProps: TransitionPropsInner\n      }) => /*#__PURE__*/_jsx(TransitionComponent, _extends({\n        timeout: theme.transitions.duration.shorter\n      }, TransitionPropsInner, transitionProps, {\n        children: /*#__PURE__*/_jsxs(TooltipComponent, _extends({}, tooltipProps, {\n          children: [title, arrow ? /*#__PURE__*/_jsx(ArrowComponent, _extends({}, tooltipArrowProps, {\n            ref: setArrowRef\n          })) : null]\n        }))\n      }))\n    }))]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Tooltip.propTypes /* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit the d.ts file and run \"yarn proptypes\"     |\n  // ----------------------------------------------------------------------\n  /**\n   * If `true`, adds an arrow to the tooltip.\n   * @default false\n   */\n  arrow: PropTypes.bool,\n  /**\n   * Tooltip reference element.\n   */\n  children: elementAcceptingRef.isRequired,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The components used for each slot inside.\n   *\n   * This prop is an alias for the `slots` prop.\n   * It's recommended to use the `slots` prop instead.\n   *\n   * @default {}\n   */\n  components: PropTypes.shape({\n    Arrow: PropTypes.elementType,\n    Popper: PropTypes.elementType,\n    Tooltip: PropTypes.elementType,\n    Transition: PropTypes.elementType\n  }),\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * This prop is an alias for the `slotProps` prop.\n   * It's recommended to use the `slotProps` prop instead, as `componentsProps` will be deprecated in the future.\n   *\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    arrow: PropTypes.object,\n    popper: PropTypes.object,\n    tooltip: PropTypes.object,\n    transition: PropTypes.object\n  }),\n  /**\n   * Set to `true` if the `title` acts as an accessible description.\n   * By default the `title` acts as an accessible label for the child.\n   * @default false\n   */\n  describeChild: PropTypes.bool,\n  /**\n   * Do not respond to focus-visible events.\n   * @default false\n   */\n  disableFocusListener: PropTypes.bool,\n  /**\n   * Do not respond to hover events.\n   * @default false\n   */\n  disableHoverListener: PropTypes.bool,\n  /**\n   * Makes a tooltip not interactive, i.e. it will close when the user\n   * hovers over the tooltip before the `leaveDelay` is expired.\n   * @default false\n   */\n  disableInteractive: PropTypes.bool,\n  /**\n   * Do not respond to long press touch events.\n   * @default false\n   */\n  disableTouchListener: PropTypes.bool,\n  /**\n   * The number of milliseconds to wait before showing the tooltip.\n   * This prop won't impact the enter touch delay (`enterTouchDelay`).\n   * @default 100\n   */\n  enterDelay: PropTypes.number,\n  /**\n   * The number of milliseconds to wait before showing the tooltip when one was already recently opened.\n   * @default 0\n   */\n  enterNextDelay: PropTypes.number,\n  /**\n   * The number of milliseconds a user must touch the element before showing the tooltip.\n   * @default 700\n   */\n  enterTouchDelay: PropTypes.number,\n  /**\n   * If `true`, the tooltip follow the cursor over the wrapped element.\n   * @default false\n   */\n  followCursor: PropTypes.bool,\n  /**\n   * This prop is used to help implement the accessibility logic.\n   * If you don't provide this prop. It falls back to a randomly generated id.\n   */\n  id: PropTypes.string,\n  /**\n   * The number of milliseconds to wait before hiding the tooltip.\n   * This prop won't impact the leave touch delay (`leaveTouchDelay`).\n   * @default 0\n   */\n  leaveDelay: PropTypes.number,\n  /**\n   * The number of milliseconds after the user stops touching an element before hiding the tooltip.\n   * @default 1500\n   */\n  leaveTouchDelay: PropTypes.number,\n  /**\n   * Callback fired when the component requests to be closed.\n   *\n   * @param {React.SyntheticEvent} event The event source of the callback.\n   */\n  onClose: PropTypes.func,\n  /**\n   * Callback fired when the component requests to be open.\n   *\n   * @param {React.SyntheticEvent} event The event source of the callback.\n   */\n  onOpen: PropTypes.func,\n  /**\n   * If `true`, the component is shown.\n   */\n  open: PropTypes.bool,\n  /**\n   * Tooltip placement.\n   * @default 'bottom'\n   */\n  placement: PropTypes.oneOf(['bottom-end', 'bottom-start', 'bottom', 'left-end', 'left-start', 'left', 'right-end', 'right-start', 'right', 'top-end', 'top-start', 'top']),\n  /**\n   * The component used for the popper.\n   * @default Popper\n   */\n  PopperComponent: PropTypes.elementType,\n  /**\n   * Props applied to the [`Popper`](/material-ui/api/popper/) element.\n   * @default {}\n   */\n  PopperProps: PropTypes.object,\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * This prop is an alias for the `componentsProps` prop, which will be deprecated in the future.\n   *\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    arrow: PropTypes.object,\n    popper: PropTypes.object,\n    tooltip: PropTypes.object,\n    transition: PropTypes.object\n  }),\n  /**\n   * The components used for each slot inside.\n   *\n   * This prop is an alias for the `components` prop, which will be deprecated in the future.\n   *\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    arrow: PropTypes.elementType,\n    popper: PropTypes.elementType,\n    tooltip: PropTypes.elementType,\n    transition: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * Tooltip title. Zero-length titles string, undefined, null and false are never displayed.\n   */\n  title: PropTypes.node,\n  /**\n   * The component used for the transition.\n   * [Follow this guide](/material-ui/transitions/#transitioncomponent-prop) to learn more about the requirements for this component.\n   * @default Grow\n   */\n  TransitionComponent: PropTypes.elementType,\n  /**\n   * Props applied to the transition element.\n   * By default, the element is based on this [`Transition`](http://reactcommunity.org/react-transition-group/transition/) component.\n   */\n  TransitionProps: PropTypes.object\n} : void 0;\nexport default Tooltip;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "React", "PropTypes", "clsx", "elementAcceptingRef", "unstable_composeClasses", "composeClasses", "appendOwnerState", "alpha", "styled", "useTheme", "useThemeProps", "capitalize", "Grow", "<PERSON><PERSON>", "useEventCallback", "useForkRef", "useId", "useIsFocusVisible", "useControlled", "tooltipClasses", "getTooltipUtilityClass", "jsx", "_jsx", "jsxs", "_jsxs", "round", "value", "Math", "useUtilityClasses", "ownerState", "classes", "disableInteractive", "arrow", "touch", "placement", "slots", "popper", "tooltip", "split", "TooltipPopper", "name", "slot", "overridesResolver", "props", "styles", "popperInteractive", "popperArrow", "open", "popperClose", "theme", "zIndex", "vars", "pointerEvents", "top", "marginTop", "transform<PERSON><PERSON>in", "bottom", "marginBottom", "isRtl", "left", "marginLeft", "right", "marginRight", "height", "width", "TooltipTooltip", "tooltipArrow", "backgroundColor", "palette", "<PERSON><PERSON><PERSON>", "bg", "grey", "borderRadius", "shape", "color", "common", "white", "fontFamily", "typography", "padding", "fontSize", "pxToRem", "max<PERSON><PERSON><PERSON>", "margin", "wordWrap", "fontWeight", "fontWeightMedium", "position", "lineHeight", "fontWeightRegular", "TooltipArrow", "overflow", "boxSizing", "content", "display", "transform", "hystersisOpen", "hystersis<PERSON><PERSON>r", "cursorPosition", "x", "y", "testReset", "clearTimeout", "composeEventHandler", "handler", "<PERSON><PERSON><PERSON><PERSON>", "event", "forwardRef", "inProps", "ref", "_ref", "_slots$popper", "_ref2", "_ref3", "_slots$transition", "_ref4", "_slots$tooltip", "_ref5", "_slots$arrow", "_slotProps$popper", "_ref6", "_slotProps$popper2", "_slotProps$transition", "_slotProps$tooltip", "_ref7", "_slotProps$tooltip2", "_slotProps$arrow", "_ref8", "_slotProps$arrow2", "children", "childrenProp", "components", "componentsProps", "<PERSON><PERSON><PERSON><PERSON>", "disableFocusListener", "disableHoverListener", "disableInteractiveProp", "disableTouch<PERSON><PERSON>ener", "enterDelay", "enterNextDelay", "enterTouchDelay", "followCursor", "id", "idProp", "leaveDelay", "leaveTouchDelay", "onClose", "onOpen", "openProp", "PopperComponent", "PopperComponentProp", "PopperProps", "slotProps", "title", "TransitionComponent", "TransitionComponentProp", "TransitionProps", "other", "isValidElement", "direction", "childNode", "setChildNode", "useState", "arrowRef", "setArrowRef", "ignoreNonTouchEvents", "useRef", "closeTimer", "enterTimer", "leaveTimer", "touchTimer", "openState", "setOpenState", "controlled", "default", "state", "process", "env", "NODE_ENV", "current", "isControlled", "undefined", "useEffect", "disabled", "tagName", "toLowerCase", "console", "error", "join", "prevUserSelect", "stopTouchInteraction", "useCallback", "document", "body", "style", "WebkitUserSelect", "handleOpen", "handleClose", "setTimeout", "transitions", "duration", "shortest", "handleEnter", "type", "removeAttribute", "handleLeave", "isFocusVisibleRef", "onBlur", "handleBlurVisible", "onFocus", "handleFocusVisible", "focusVisibleRef", "setChildIsFocusVisible", "handleBlur", "handleFocus", "currentTarget", "detectTouchStart", "childrenProps", "onTouchStart", "handleMouseOver", "handleMouseLeave", "handleTouchStart", "handleTouchEnd", "onTouchEnd", "handleKeyDown", "nativeEvent", "key", "addEventListener", "removeEventListener", "handleRef", "popperRef", "handleMouseMove", "onMouseMove", "clientX", "clientY", "update", "nameOrDescProps", "titleIsString", "className", "getAttribute", "interactiveWrapperListeners", "onMouseOver", "onMouseLeave", "popperOptions", "useMemo", "_PopperProps$popperOp", "tooltipModifiers", "enabled", "Boolean", "options", "element", "modifiers", "concat", "transition", "Transition", "TooltipComponent", "ArrowComponent", "Arrow", "popperProps", "transitionProps", "tooltipProps", "tooltipArrowProps", "Fragment", "cloneElement", "as", "anchorEl", "getBoundingClientRect", "TransitionPropsInner", "timeout", "shorter", "propTypes", "bool", "isRequired", "object", "string", "elementType", "number", "func", "oneOf", "sx", "oneOfType", "arrayOf", "node"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/material/Tooltip/Tooltip.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"arrow\", \"children\", \"classes\", \"components\", \"componentsProps\", \"describeChild\", \"disableFocusListener\", \"disableHoverListener\", \"disableInteractive\", \"disableTouchListener\", \"enterDelay\", \"enterNextDelay\", \"enterTouchDelay\", \"followCursor\", \"id\", \"leaveDelay\", \"leaveTouchDelay\", \"onClose\", \"onOpen\", \"open\", \"placement\", \"PopperComponent\", \"PopperProps\", \"slotProps\", \"slots\", \"title\", \"TransitionComponent\", \"TransitionProps\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { elementAcceptingRef } from '@mui/utils';\nimport { unstable_composeClasses as composeClasses, appendOwnerState } from '@mui/base';\nimport { alpha } from '@mui/system';\nimport styled from '../styles/styled';\nimport useTheme from '../styles/useTheme';\nimport useThemeProps from '../styles/useThemeProps';\nimport capitalize from '../utils/capitalize';\nimport Grow from '../Grow';\nimport Popper from '../Popper';\nimport useEventCallback from '../utils/useEventCallback';\nimport useForkRef from '../utils/useForkRef';\nimport useId from '../utils/useId';\nimport useIsFocusVisible from '../utils/useIsFocusVisible';\nimport useControlled from '../utils/useControlled';\nimport tooltipClasses, { getTooltipUtilityClass } from './tooltipClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nfunction round(value) {\n  return Math.round(value * 1e5) / 1e5;\n}\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    disableInteractive,\n    arrow,\n    touch,\n    placement\n  } = ownerState;\n  const slots = {\n    popper: ['popper', !disableInteractive && 'popperInteractive', arrow && 'popperArrow'],\n    tooltip: ['tooltip', arrow && 'tooltipArrow', touch && 'touch', `tooltipPlacement${capitalize(placement.split('-')[0])}`],\n    arrow: ['arrow']\n  };\n  return composeClasses(slots, getTooltipUtilityClass, classes);\n};\nconst TooltipPopper = styled(Popper, {\n  name: 'MuiTooltip',\n  slot: 'Popper',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.popper, !ownerState.disableInteractive && styles.popperInteractive, ownerState.arrow && styles.popperArrow, !ownerState.open && styles.popperClose];\n  }\n})(({\n  theme,\n  ownerState,\n  open\n}) => _extends({\n  zIndex: (theme.vars || theme).zIndex.tooltip,\n  pointerEvents: 'none'\n}, !ownerState.disableInteractive && {\n  pointerEvents: 'auto'\n}, !open && {\n  pointerEvents: 'none'\n}, ownerState.arrow && {\n  [`&[data-popper-placement*=\"bottom\"] .${tooltipClasses.arrow}`]: {\n    top: 0,\n    marginTop: '-0.71em',\n    '&::before': {\n      transformOrigin: '0 100%'\n    }\n  },\n  [`&[data-popper-placement*=\"top\"] .${tooltipClasses.arrow}`]: {\n    bottom: 0,\n    marginBottom: '-0.71em',\n    '&::before': {\n      transformOrigin: '100% 0'\n    }\n  },\n  [`&[data-popper-placement*=\"right\"] .${tooltipClasses.arrow}`]: _extends({}, !ownerState.isRtl ? {\n    left: 0,\n    marginLeft: '-0.71em'\n  } : {\n    right: 0,\n    marginRight: '-0.71em'\n  }, {\n    height: '1em',\n    width: '0.71em',\n    '&::before': {\n      transformOrigin: '100% 100%'\n    }\n  }),\n  [`&[data-popper-placement*=\"left\"] .${tooltipClasses.arrow}`]: _extends({}, !ownerState.isRtl ? {\n    right: 0,\n    marginRight: '-0.71em'\n  } : {\n    left: 0,\n    marginLeft: '-0.71em'\n  }, {\n    height: '1em',\n    width: '0.71em',\n    '&::before': {\n      transformOrigin: '0 0'\n    }\n  })\n}));\nconst TooltipTooltip = styled('div', {\n  name: 'MuiTooltip',\n  slot: 'Tooltip',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.tooltip, ownerState.touch && styles.touch, ownerState.arrow && styles.tooltipArrow, styles[`tooltipPlacement${capitalize(ownerState.placement.split('-')[0])}`]];\n  }\n})(({\n  theme,\n  ownerState\n}) => _extends({\n  backgroundColor: theme.vars ? theme.vars.palette.Tooltip.bg : alpha(theme.palette.grey[700], 0.92),\n  borderRadius: (theme.vars || theme).shape.borderRadius,\n  color: (theme.vars || theme).palette.common.white,\n  fontFamily: theme.typography.fontFamily,\n  padding: '4px 8px',\n  fontSize: theme.typography.pxToRem(11),\n  maxWidth: 300,\n  margin: 2,\n  wordWrap: 'break-word',\n  fontWeight: theme.typography.fontWeightMedium\n}, ownerState.arrow && {\n  position: 'relative',\n  margin: 0\n}, ownerState.touch && {\n  padding: '8px 16px',\n  fontSize: theme.typography.pxToRem(14),\n  lineHeight: `${round(16 / 14)}em`,\n  fontWeight: theme.typography.fontWeightRegular\n}, {\n  [`.${tooltipClasses.popper}[data-popper-placement*=\"left\"] &`]: _extends({\n    transformOrigin: 'right center'\n  }, !ownerState.isRtl ? _extends({\n    marginRight: '14px'\n  }, ownerState.touch && {\n    marginRight: '24px'\n  }) : _extends({\n    marginLeft: '14px'\n  }, ownerState.touch && {\n    marginLeft: '24px'\n  })),\n  [`.${tooltipClasses.popper}[data-popper-placement*=\"right\"] &`]: _extends({\n    transformOrigin: 'left center'\n  }, !ownerState.isRtl ? _extends({\n    marginLeft: '14px'\n  }, ownerState.touch && {\n    marginLeft: '24px'\n  }) : _extends({\n    marginRight: '14px'\n  }, ownerState.touch && {\n    marginRight: '24px'\n  })),\n  [`.${tooltipClasses.popper}[data-popper-placement*=\"top\"] &`]: _extends({\n    transformOrigin: 'center bottom',\n    marginBottom: '14px'\n  }, ownerState.touch && {\n    marginBottom: '24px'\n  }),\n  [`.${tooltipClasses.popper}[data-popper-placement*=\"bottom\"] &`]: _extends({\n    transformOrigin: 'center top',\n    marginTop: '14px'\n  }, ownerState.touch && {\n    marginTop: '24px'\n  })\n}));\nconst TooltipArrow = styled('span', {\n  name: 'MuiTooltip',\n  slot: 'Arrow',\n  overridesResolver: (props, styles) => styles.arrow\n})(({\n  theme\n}) => ({\n  overflow: 'hidden',\n  position: 'absolute',\n  width: '1em',\n  height: '0.71em' /* = width / sqrt(2) = (length of the hypotenuse) */,\n  boxSizing: 'border-box',\n  color: theme.vars ? theme.vars.palette.Tooltip.bg : alpha(theme.palette.grey[700], 0.9),\n  '&::before': {\n    content: '\"\"',\n    margin: 'auto',\n    display: 'block',\n    width: '100%',\n    height: '100%',\n    backgroundColor: 'currentColor',\n    transform: 'rotate(45deg)'\n  }\n}));\nlet hystersisOpen = false;\nlet hystersisTimer = null;\nlet cursorPosition = {\n  x: 0,\n  y: 0\n};\nexport function testReset() {\n  hystersisOpen = false;\n  clearTimeout(hystersisTimer);\n}\nfunction composeEventHandler(handler, eventHandler) {\n  return event => {\n    if (eventHandler) {\n      eventHandler(event);\n    }\n    handler(event);\n  };\n}\n\n// TODO v6: Remove PopperComponent, PopperProps, TransitionComponent and TransitionProps.\nconst Tooltip = /*#__PURE__*/React.forwardRef(function Tooltip(inProps, ref) {\n  var _ref, _slots$popper, _ref2, _ref3, _slots$transition, _ref4, _slots$tooltip, _ref5, _slots$arrow, _slotProps$popper, _ref6, _slotProps$popper2, _slotProps$transition, _slotProps$tooltip, _ref7, _slotProps$tooltip2, _slotProps$arrow, _ref8, _slotProps$arrow2;\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiTooltip'\n  });\n  const {\n      arrow = false,\n      children: childrenProp,\n      components = {},\n      componentsProps = {},\n      describeChild = false,\n      disableFocusListener = false,\n      disableHoverListener = false,\n      disableInteractive: disableInteractiveProp = false,\n      disableTouchListener = false,\n      enterDelay = 100,\n      enterNextDelay = 0,\n      enterTouchDelay = 700,\n      followCursor = false,\n      id: idProp,\n      leaveDelay = 0,\n      leaveTouchDelay = 1500,\n      onClose,\n      onOpen,\n      open: openProp,\n      placement = 'bottom',\n      PopperComponent: PopperComponentProp,\n      PopperProps = {},\n      slotProps = {},\n      slots = {},\n      title,\n      TransitionComponent: TransitionComponentProp = Grow,\n      TransitionProps\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n\n  // to prevent runtime errors, developers will need to provide a child as a React element anyway.\n  const children = /*#__PURE__*/React.isValidElement(childrenProp) ? childrenProp : /*#__PURE__*/_jsx(\"span\", {\n    children: childrenProp\n  });\n  const theme = useTheme();\n  const isRtl = theme.direction === 'rtl';\n  const [childNode, setChildNode] = React.useState();\n  const [arrowRef, setArrowRef] = React.useState(null);\n  const ignoreNonTouchEvents = React.useRef(false);\n  const disableInteractive = disableInteractiveProp || followCursor;\n  const closeTimer = React.useRef();\n  const enterTimer = React.useRef();\n  const leaveTimer = React.useRef();\n  const touchTimer = React.useRef();\n  const [openState, setOpenState] = useControlled({\n    controlled: openProp,\n    default: false,\n    name: 'Tooltip',\n    state: 'open'\n  });\n  let open = openState;\n  if (process.env.NODE_ENV !== 'production') {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    const {\n      current: isControlled\n    } = React.useRef(openProp !== undefined);\n\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useEffect(() => {\n      if (childNode && childNode.disabled && !isControlled && title !== '' && childNode.tagName.toLowerCase() === 'button') {\n        console.error(['MUI: You are providing a disabled `button` child to the Tooltip component.', 'A disabled element does not fire events.', \"Tooltip needs to listen to the child element's events to display the title.\", '', 'Add a simple wrapper element, such as a `span`.'].join('\\n'));\n      }\n    }, [title, childNode, isControlled]);\n  }\n  const id = useId(idProp);\n  const prevUserSelect = React.useRef();\n  const stopTouchInteraction = React.useCallback(() => {\n    if (prevUserSelect.current !== undefined) {\n      document.body.style.WebkitUserSelect = prevUserSelect.current;\n      prevUserSelect.current = undefined;\n    }\n    clearTimeout(touchTimer.current);\n  }, []);\n  React.useEffect(() => {\n    return () => {\n      clearTimeout(closeTimer.current);\n      clearTimeout(enterTimer.current);\n      clearTimeout(leaveTimer.current);\n      stopTouchInteraction();\n    };\n  }, [stopTouchInteraction]);\n  const handleOpen = event => {\n    clearTimeout(hystersisTimer);\n    hystersisOpen = true;\n\n    // The mouseover event will trigger for every nested element in the tooltip.\n    // We can skip rerendering when the tooltip is already open.\n    // We are using the mouseover event instead of the mouseenter event to fix a hide/show issue.\n    setOpenState(true);\n    if (onOpen && !open) {\n      onOpen(event);\n    }\n  };\n  const handleClose = useEventCallback(\n  /**\n   * @param {React.SyntheticEvent | Event} event\n   */\n  event => {\n    clearTimeout(hystersisTimer);\n    hystersisTimer = setTimeout(() => {\n      hystersisOpen = false;\n    }, 800 + leaveDelay);\n    setOpenState(false);\n    if (onClose && open) {\n      onClose(event);\n    }\n    clearTimeout(closeTimer.current);\n    closeTimer.current = setTimeout(() => {\n      ignoreNonTouchEvents.current = false;\n    }, theme.transitions.duration.shortest);\n  });\n  const handleEnter = event => {\n    if (ignoreNonTouchEvents.current && event.type !== 'touchstart') {\n      return;\n    }\n\n    // Remove the title ahead of time.\n    // We don't want to wait for the next render commit.\n    // We would risk displaying two tooltips at the same time (native + this one).\n    if (childNode) {\n      childNode.removeAttribute('title');\n    }\n    clearTimeout(enterTimer.current);\n    clearTimeout(leaveTimer.current);\n    if (enterDelay || hystersisOpen && enterNextDelay) {\n      enterTimer.current = setTimeout(() => {\n        handleOpen(event);\n      }, hystersisOpen ? enterNextDelay : enterDelay);\n    } else {\n      handleOpen(event);\n    }\n  };\n  const handleLeave = event => {\n    clearTimeout(enterTimer.current);\n    clearTimeout(leaveTimer.current);\n    leaveTimer.current = setTimeout(() => {\n      handleClose(event);\n    }, leaveDelay);\n  };\n  const {\n    isFocusVisibleRef,\n    onBlur: handleBlurVisible,\n    onFocus: handleFocusVisible,\n    ref: focusVisibleRef\n  } = useIsFocusVisible();\n  // We don't necessarily care about the focusVisible state (which is safe to access via ref anyway).\n  // We just need to re-render the Tooltip if the focus-visible state changes.\n  const [, setChildIsFocusVisible] = React.useState(false);\n  const handleBlur = event => {\n    handleBlurVisible(event);\n    if (isFocusVisibleRef.current === false) {\n      setChildIsFocusVisible(false);\n      handleLeave(event);\n    }\n  };\n  const handleFocus = event => {\n    // Workaround for https://github.com/facebook/react/issues/7769\n    // The autoFocus of React might trigger the event before the componentDidMount.\n    // We need to account for this eventuality.\n    if (!childNode) {\n      setChildNode(event.currentTarget);\n    }\n    handleFocusVisible(event);\n    if (isFocusVisibleRef.current === true) {\n      setChildIsFocusVisible(true);\n      handleEnter(event);\n    }\n  };\n  const detectTouchStart = event => {\n    ignoreNonTouchEvents.current = true;\n    const childrenProps = children.props;\n    if (childrenProps.onTouchStart) {\n      childrenProps.onTouchStart(event);\n    }\n  };\n  const handleMouseOver = handleEnter;\n  const handleMouseLeave = handleLeave;\n  const handleTouchStart = event => {\n    detectTouchStart(event);\n    clearTimeout(leaveTimer.current);\n    clearTimeout(closeTimer.current);\n    stopTouchInteraction();\n    prevUserSelect.current = document.body.style.WebkitUserSelect;\n    // Prevent iOS text selection on long-tap.\n    document.body.style.WebkitUserSelect = 'none';\n    touchTimer.current = setTimeout(() => {\n      document.body.style.WebkitUserSelect = prevUserSelect.current;\n      handleEnter(event);\n    }, enterTouchDelay);\n  };\n  const handleTouchEnd = event => {\n    if (children.props.onTouchEnd) {\n      children.props.onTouchEnd(event);\n    }\n    stopTouchInteraction();\n    clearTimeout(leaveTimer.current);\n    leaveTimer.current = setTimeout(() => {\n      handleClose(event);\n    }, leaveTouchDelay);\n  };\n  React.useEffect(() => {\n    if (!open) {\n      return undefined;\n    }\n\n    /**\n     * @param {KeyboardEvent} nativeEvent\n     */\n    function handleKeyDown(nativeEvent) {\n      // IE11, Edge (prior to using Bink?) use 'Esc'\n      if (nativeEvent.key === 'Escape' || nativeEvent.key === 'Esc') {\n        handleClose(nativeEvent);\n      }\n    }\n    document.addEventListener('keydown', handleKeyDown);\n    return () => {\n      document.removeEventListener('keydown', handleKeyDown);\n    };\n  }, [handleClose, open]);\n  const handleRef = useForkRef(children.ref, focusVisibleRef, setChildNode, ref);\n\n  // There is no point in displaying an empty tooltip.\n  if (!title && title !== 0) {\n    open = false;\n  }\n  const popperRef = React.useRef();\n  const handleMouseMove = event => {\n    const childrenProps = children.props;\n    if (childrenProps.onMouseMove) {\n      childrenProps.onMouseMove(event);\n    }\n    cursorPosition = {\n      x: event.clientX,\n      y: event.clientY\n    };\n    if (popperRef.current) {\n      popperRef.current.update();\n    }\n  };\n  const nameOrDescProps = {};\n  const titleIsString = typeof title === 'string';\n  if (describeChild) {\n    nameOrDescProps.title = !open && titleIsString && !disableHoverListener ? title : null;\n    nameOrDescProps['aria-describedby'] = open ? id : null;\n  } else {\n    nameOrDescProps['aria-label'] = titleIsString ? title : null;\n    nameOrDescProps['aria-labelledby'] = open && !titleIsString ? id : null;\n  }\n  const childrenProps = _extends({}, nameOrDescProps, other, children.props, {\n    className: clsx(other.className, children.props.className),\n    onTouchStart: detectTouchStart,\n    ref: handleRef\n  }, followCursor ? {\n    onMouseMove: handleMouseMove\n  } : {});\n  if (process.env.NODE_ENV !== 'production') {\n    childrenProps['data-mui-internal-clone-element'] = true;\n\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useEffect(() => {\n      if (childNode && !childNode.getAttribute('data-mui-internal-clone-element')) {\n        console.error(['MUI: The `children` component of the Tooltip is not forwarding its props correctly.', 'Please make sure that props are spread on the same element that the ref is applied to.'].join('\\n'));\n      }\n    }, [childNode]);\n  }\n  const interactiveWrapperListeners = {};\n  if (!disableTouchListener) {\n    childrenProps.onTouchStart = handleTouchStart;\n    childrenProps.onTouchEnd = handleTouchEnd;\n  }\n  if (!disableHoverListener) {\n    childrenProps.onMouseOver = composeEventHandler(handleMouseOver, childrenProps.onMouseOver);\n    childrenProps.onMouseLeave = composeEventHandler(handleMouseLeave, childrenProps.onMouseLeave);\n    if (!disableInteractive) {\n      interactiveWrapperListeners.onMouseOver = handleMouseOver;\n      interactiveWrapperListeners.onMouseLeave = handleMouseLeave;\n    }\n  }\n  if (!disableFocusListener) {\n    childrenProps.onFocus = composeEventHandler(handleFocus, childrenProps.onFocus);\n    childrenProps.onBlur = composeEventHandler(handleBlur, childrenProps.onBlur);\n    if (!disableInteractive) {\n      interactiveWrapperListeners.onFocus = handleFocus;\n      interactiveWrapperListeners.onBlur = handleBlur;\n    }\n  }\n  if (process.env.NODE_ENV !== 'production') {\n    if (children.props.title) {\n      console.error(['MUI: You have provided a `title` prop to the child of <Tooltip />.', `Remove this title prop \\`${children.props.title}\\` or the Tooltip component.`].join('\\n'));\n    }\n  }\n  const popperOptions = React.useMemo(() => {\n    var _PopperProps$popperOp;\n    let tooltipModifiers = [{\n      name: 'arrow',\n      enabled: Boolean(arrowRef),\n      options: {\n        element: arrowRef,\n        padding: 4\n      }\n    }];\n    if ((_PopperProps$popperOp = PopperProps.popperOptions) != null && _PopperProps$popperOp.modifiers) {\n      tooltipModifiers = tooltipModifiers.concat(PopperProps.popperOptions.modifiers);\n    }\n    return _extends({}, PopperProps.popperOptions, {\n      modifiers: tooltipModifiers\n    });\n  }, [arrowRef, PopperProps]);\n  const ownerState = _extends({}, props, {\n    isRtl,\n    arrow,\n    disableInteractive,\n    placement,\n    PopperComponentProp,\n    touch: ignoreNonTouchEvents.current\n  });\n  const classes = useUtilityClasses(ownerState);\n  const PopperComponent = (_ref = (_slots$popper = slots.popper) != null ? _slots$popper : components.Popper) != null ? _ref : TooltipPopper;\n  const TransitionComponent = (_ref2 = (_ref3 = (_slots$transition = slots.transition) != null ? _slots$transition : components.Transition) != null ? _ref3 : TransitionComponentProp) != null ? _ref2 : Grow;\n  const TooltipComponent = (_ref4 = (_slots$tooltip = slots.tooltip) != null ? _slots$tooltip : components.Tooltip) != null ? _ref4 : TooltipTooltip;\n  const ArrowComponent = (_ref5 = (_slots$arrow = slots.arrow) != null ? _slots$arrow : components.Arrow) != null ? _ref5 : TooltipArrow;\n  const popperProps = appendOwnerState(PopperComponent, _extends({}, PopperProps, (_slotProps$popper = slotProps.popper) != null ? _slotProps$popper : componentsProps.popper, {\n    className: clsx(classes.popper, PopperProps == null ? void 0 : PopperProps.className, (_ref6 = (_slotProps$popper2 = slotProps.popper) != null ? _slotProps$popper2 : componentsProps.popper) == null ? void 0 : _ref6.className)\n  }), ownerState);\n  const transitionProps = appendOwnerState(TransitionComponent, _extends({}, TransitionProps, (_slotProps$transition = slotProps.transition) != null ? _slotProps$transition : componentsProps.transition), ownerState);\n  const tooltipProps = appendOwnerState(TooltipComponent, _extends({}, (_slotProps$tooltip = slotProps.tooltip) != null ? _slotProps$tooltip : componentsProps.tooltip, {\n    className: clsx(classes.tooltip, (_ref7 = (_slotProps$tooltip2 = slotProps.tooltip) != null ? _slotProps$tooltip2 : componentsProps.tooltip) == null ? void 0 : _ref7.className)\n  }), ownerState);\n  const tooltipArrowProps = appendOwnerState(ArrowComponent, _extends({}, (_slotProps$arrow = slotProps.arrow) != null ? _slotProps$arrow : componentsProps.arrow, {\n    className: clsx(classes.arrow, (_ref8 = (_slotProps$arrow2 = slotProps.arrow) != null ? _slotProps$arrow2 : componentsProps.arrow) == null ? void 0 : _ref8.className)\n  }), ownerState);\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [/*#__PURE__*/React.cloneElement(children, childrenProps), /*#__PURE__*/_jsx(PopperComponent, _extends({\n      as: PopperComponentProp != null ? PopperComponentProp : Popper,\n      placement: placement,\n      anchorEl: followCursor ? {\n        getBoundingClientRect: () => ({\n          top: cursorPosition.y,\n          left: cursorPosition.x,\n          right: cursorPosition.x,\n          bottom: cursorPosition.y,\n          width: 0,\n          height: 0\n        })\n      } : childNode,\n      popperRef: popperRef,\n      open: childNode ? open : false,\n      id: id,\n      transition: true\n    }, interactiveWrapperListeners, popperProps, {\n      popperOptions: popperOptions,\n      children: ({\n        TransitionProps: TransitionPropsInner\n      }) => /*#__PURE__*/_jsx(TransitionComponent, _extends({\n        timeout: theme.transitions.duration.shorter\n      }, TransitionPropsInner, transitionProps, {\n        children: /*#__PURE__*/_jsxs(TooltipComponent, _extends({}, tooltipProps, {\n          children: [title, arrow ? /*#__PURE__*/_jsx(ArrowComponent, _extends({}, tooltipArrowProps, {\n            ref: setArrowRef\n          })) : null]\n        }))\n      }))\n    }))]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Tooltip.propTypes /* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit the d.ts file and run \"yarn proptypes\"     |\n  // ----------------------------------------------------------------------\n  /**\n   * If `true`, adds an arrow to the tooltip.\n   * @default false\n   */\n  arrow: PropTypes.bool,\n  /**\n   * Tooltip reference element.\n   */\n  children: elementAcceptingRef.isRequired,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The components used for each slot inside.\n   *\n   * This prop is an alias for the `slots` prop.\n   * It's recommended to use the `slots` prop instead.\n   *\n   * @default {}\n   */\n  components: PropTypes.shape({\n    Arrow: PropTypes.elementType,\n    Popper: PropTypes.elementType,\n    Tooltip: PropTypes.elementType,\n    Transition: PropTypes.elementType\n  }),\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * This prop is an alias for the `slotProps` prop.\n   * It's recommended to use the `slotProps` prop instead, as `componentsProps` will be deprecated in the future.\n   *\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    arrow: PropTypes.object,\n    popper: PropTypes.object,\n    tooltip: PropTypes.object,\n    transition: PropTypes.object\n  }),\n  /**\n   * Set to `true` if the `title` acts as an accessible description.\n   * By default the `title` acts as an accessible label for the child.\n   * @default false\n   */\n  describeChild: PropTypes.bool,\n  /**\n   * Do not respond to focus-visible events.\n   * @default false\n   */\n  disableFocusListener: PropTypes.bool,\n  /**\n   * Do not respond to hover events.\n   * @default false\n   */\n  disableHoverListener: PropTypes.bool,\n  /**\n   * Makes a tooltip not interactive, i.e. it will close when the user\n   * hovers over the tooltip before the `leaveDelay` is expired.\n   * @default false\n   */\n  disableInteractive: PropTypes.bool,\n  /**\n   * Do not respond to long press touch events.\n   * @default false\n   */\n  disableTouchListener: PropTypes.bool,\n  /**\n   * The number of milliseconds to wait before showing the tooltip.\n   * This prop won't impact the enter touch delay (`enterTouchDelay`).\n   * @default 100\n   */\n  enterDelay: PropTypes.number,\n  /**\n   * The number of milliseconds to wait before showing the tooltip when one was already recently opened.\n   * @default 0\n   */\n  enterNextDelay: PropTypes.number,\n  /**\n   * The number of milliseconds a user must touch the element before showing the tooltip.\n   * @default 700\n   */\n  enterTouchDelay: PropTypes.number,\n  /**\n   * If `true`, the tooltip follow the cursor over the wrapped element.\n   * @default false\n   */\n  followCursor: PropTypes.bool,\n  /**\n   * This prop is used to help implement the accessibility logic.\n   * If you don't provide this prop. It falls back to a randomly generated id.\n   */\n  id: PropTypes.string,\n  /**\n   * The number of milliseconds to wait before hiding the tooltip.\n   * This prop won't impact the leave touch delay (`leaveTouchDelay`).\n   * @default 0\n   */\n  leaveDelay: PropTypes.number,\n  /**\n   * The number of milliseconds after the user stops touching an element before hiding the tooltip.\n   * @default 1500\n   */\n  leaveTouchDelay: PropTypes.number,\n  /**\n   * Callback fired when the component requests to be closed.\n   *\n   * @param {React.SyntheticEvent} event The event source of the callback.\n   */\n  onClose: PropTypes.func,\n  /**\n   * Callback fired when the component requests to be open.\n   *\n   * @param {React.SyntheticEvent} event The event source of the callback.\n   */\n  onOpen: PropTypes.func,\n  /**\n   * If `true`, the component is shown.\n   */\n  open: PropTypes.bool,\n  /**\n   * Tooltip placement.\n   * @default 'bottom'\n   */\n  placement: PropTypes.oneOf(['bottom-end', 'bottom-start', 'bottom', 'left-end', 'left-start', 'left', 'right-end', 'right-start', 'right', 'top-end', 'top-start', 'top']),\n  /**\n   * The component used for the popper.\n   * @default Popper\n   */\n  PopperComponent: PropTypes.elementType,\n  /**\n   * Props applied to the [`Popper`](/material-ui/api/popper/) element.\n   * @default {}\n   */\n  PopperProps: PropTypes.object,\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * This prop is an alias for the `componentsProps` prop, which will be deprecated in the future.\n   *\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    arrow: PropTypes.object,\n    popper: PropTypes.object,\n    tooltip: PropTypes.object,\n    transition: PropTypes.object\n  }),\n  /**\n   * The components used for each slot inside.\n   *\n   * This prop is an alias for the `components` prop, which will be deprecated in the future.\n   *\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    arrow: PropTypes.elementType,\n    popper: PropTypes.elementType,\n    tooltip: PropTypes.elementType,\n    transition: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * Tooltip title. Zero-length titles string, undefined, null and false are never displayed.\n   */\n  title: PropTypes.node,\n  /**\n   * The component used for the transition.\n   * [Follow this guide](/material-ui/transitions/#transitioncomponent-prop) to learn more about the requirements for this component.\n   * @default Grow\n   */\n  TransitionComponent: PropTypes.elementType,\n  /**\n   * Props applied to the transition element.\n   * By default, the element is based on this [`Transition`](http://reactcommunity.org/react-transition-group/transition/) component.\n   */\n  TransitionProps: PropTypes.object\n} : void 0;\nexport default Tooltip;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,YAAY,EAAE,iBAAiB,EAAE,eAAe,EAAE,sBAAsB,EAAE,sBAAsB,EAAE,oBAAoB,EAAE,sBAAsB,EAAE,YAAY,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,cAAc,EAAE,IAAI,EAAE,YAAY,EAAE,iBAAiB,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,EAAE,WAAW,EAAE,iBAAiB,EAAE,aAAa,EAAE,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,qBAAqB,EAAE,iBAAiB,CAAC;AACjc,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,mBAAmB,QAAQ,YAAY;AAChD,SAASC,uBAAuB,IAAIC,cAAc,EAAEC,gBAAgB,QAAQ,WAAW;AACvF,SAASC,KAAK,QAAQ,aAAa;AACnC,OAAOC,MAAM,MAAM,kBAAkB;AACrC,OAAOC,QAAQ,MAAM,oBAAoB;AACzC,OAAOC,aAAa,MAAM,yBAAyB;AACnD,OAAOC,UAAU,MAAM,qBAAqB;AAC5C,OAAOC,IAAI,MAAM,SAAS;AAC1B,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAOC,gBAAgB,MAAM,2BAA2B;AACxD,OAAOC,UAAU,MAAM,qBAAqB;AAC5C,OAAOC,KAAK,MAAM,gBAAgB;AAClC,OAAOC,iBAAiB,MAAM,4BAA4B;AAC1D,OAAOC,aAAa,MAAM,wBAAwB;AAClD,OAAOC,cAAc,IAAIC,sBAAsB,QAAQ,kBAAkB;AACzE,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,SAASC,KAAKA,CAACC,KAAK,EAAE;EACpB,OAAOC,IAAI,CAACF,KAAK,CAACC,KAAK,GAAG,GAAG,CAAC,GAAG,GAAG;AACtC;AACA,MAAME,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,OAAO;IACPC,kBAAkB;IAClBC,KAAK;IACLC,KAAK;IACLC;EACF,CAAC,GAAGL,UAAU;EACd,MAAMM,KAAK,GAAG;IACZC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAACL,kBAAkB,IAAI,mBAAmB,EAAEC,KAAK,IAAI,aAAa,CAAC;IACtFK,OAAO,EAAE,CAAC,SAAS,EAAEL,KAAK,IAAI,cAAc,EAAEC,KAAK,IAAI,OAAO,EAAG,mBAAkBtB,UAAU,CAACuB,SAAS,CAACI,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAE,EAAC,CAAC;IACzHN,KAAK,EAAE,CAAC,OAAO;EACjB,CAAC;EACD,OAAO3B,cAAc,CAAC8B,KAAK,EAAEf,sBAAsB,EAAEU,OAAO,CAAC;AAC/D,CAAC;AACD,MAAMS,aAAa,GAAG/B,MAAM,CAACK,MAAM,EAAE;EACnC2B,IAAI,EAAE,YAAY;EAClBC,IAAI,EAAE,QAAQ;EACdC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJf;IACF,CAAC,GAAGc,KAAK;IACT,OAAO,CAACC,MAAM,CAACR,MAAM,EAAE,CAACP,UAAU,CAACE,kBAAkB,IAAIa,MAAM,CAACC,iBAAiB,EAAEhB,UAAU,CAACG,KAAK,IAAIY,MAAM,CAACE,WAAW,EAAE,CAACjB,UAAU,CAACkB,IAAI,IAAIH,MAAM,CAACI,WAAW,CAAC;EACpK;AACF,CAAC,CAAC,CAAC,CAAC;EACFC,KAAK;EACLpB,UAAU;EACVkB;AACF,CAAC,KAAKjD,QAAQ,CAAC;EACboD,MAAM,EAAE,CAACD,KAAK,CAACE,IAAI,IAAIF,KAAK,EAAEC,MAAM,CAACb,OAAO;EAC5Ce,aAAa,EAAE;AACjB,CAAC,EAAE,CAACvB,UAAU,CAACE,kBAAkB,IAAI;EACnCqB,aAAa,EAAE;AACjB,CAAC,EAAE,CAACL,IAAI,IAAI;EACVK,aAAa,EAAE;AACjB,CAAC,EAAEvB,UAAU,CAACG,KAAK,IAAI;EACrB,CAAE,uCAAsCb,cAAc,CAACa,KAAM,EAAC,GAAG;IAC/DqB,GAAG,EAAE,CAAC;IACNC,SAAS,EAAE,SAAS;IACpB,WAAW,EAAE;MACXC,eAAe,EAAE;IACnB;EACF,CAAC;EACD,CAAE,oCAAmCpC,cAAc,CAACa,KAAM,EAAC,GAAG;IAC5DwB,MAAM,EAAE,CAAC;IACTC,YAAY,EAAE,SAAS;IACvB,WAAW,EAAE;MACXF,eAAe,EAAE;IACnB;EACF,CAAC;EACD,CAAE,sCAAqCpC,cAAc,CAACa,KAAM,EAAC,GAAGlC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC+B,UAAU,CAAC6B,KAAK,GAAG;IAC/FC,IAAI,EAAE,CAAC;IACPC,UAAU,EAAE;EACd,CAAC,GAAG;IACFC,KAAK,EAAE,CAAC;IACRC,WAAW,EAAE;EACf,CAAC,EAAE;IACDC,MAAM,EAAE,KAAK;IACbC,KAAK,EAAE,QAAQ;IACf,WAAW,EAAE;MACXT,eAAe,EAAE;IACnB;EACF,CAAC,CAAC;EACF,CAAE,qCAAoCpC,cAAc,CAACa,KAAM,EAAC,GAAGlC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC+B,UAAU,CAAC6B,KAAK,GAAG;IAC9FG,KAAK,EAAE,CAAC;IACRC,WAAW,EAAE;EACf,CAAC,GAAG;IACFH,IAAI,EAAE,CAAC;IACPC,UAAU,EAAE;EACd,CAAC,EAAE;IACDG,MAAM,EAAE,KAAK;IACbC,KAAK,EAAE,QAAQ;IACf,WAAW,EAAE;MACXT,eAAe,EAAE;IACnB;EACF,CAAC;AACH,CAAC,CAAC,CAAC;AACH,MAAMU,cAAc,GAAGzD,MAAM,CAAC,KAAK,EAAE;EACnCgC,IAAI,EAAE,YAAY;EAClBC,IAAI,EAAE,SAAS;EACfC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJf;IACF,CAAC,GAAGc,KAAK;IACT,OAAO,CAACC,MAAM,CAACP,OAAO,EAAER,UAAU,CAACI,KAAK,IAAIW,MAAM,CAACX,KAAK,EAAEJ,UAAU,CAACG,KAAK,IAAIY,MAAM,CAACsB,YAAY,EAAEtB,MAAM,CAAE,mBAAkBjC,UAAU,CAACkB,UAAU,CAACK,SAAS,CAACI,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAE,EAAC,CAAC,CAAC;EACjL;AACF,CAAC,CAAC,CAAC,CAAC;EACFW,KAAK;EACLpB;AACF,CAAC,KAAK/B,QAAQ,CAAC;EACbqE,eAAe,EAAElB,KAAK,CAACE,IAAI,GAAGF,KAAK,CAACE,IAAI,CAACiB,OAAO,CAACC,OAAO,CAACC,EAAE,GAAG/D,KAAK,CAAC0C,KAAK,CAACmB,OAAO,CAACG,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC;EAClGC,YAAY,EAAE,CAACvB,KAAK,CAACE,IAAI,IAAIF,KAAK,EAAEwB,KAAK,CAACD,YAAY;EACtDE,KAAK,EAAE,CAACzB,KAAK,CAACE,IAAI,IAAIF,KAAK,EAAEmB,OAAO,CAACO,MAAM,CAACC,KAAK;EACjDC,UAAU,EAAE5B,KAAK,CAAC6B,UAAU,CAACD,UAAU;EACvCE,OAAO,EAAE,SAAS;EAClBC,QAAQ,EAAE/B,KAAK,CAAC6B,UAAU,CAACG,OAAO,CAAC,EAAE,CAAC;EACtCC,QAAQ,EAAE,GAAG;EACbC,MAAM,EAAE,CAAC;EACTC,QAAQ,EAAE,YAAY;EACtBC,UAAU,EAAEpC,KAAK,CAAC6B,UAAU,CAACQ;AAC/B,CAAC,EAAEzD,UAAU,CAACG,KAAK,IAAI;EACrBuD,QAAQ,EAAE,UAAU;EACpBJ,MAAM,EAAE;AACV,CAAC,EAAEtD,UAAU,CAACI,KAAK,IAAI;EACrB8C,OAAO,EAAE,UAAU;EACnBC,QAAQ,EAAE/B,KAAK,CAAC6B,UAAU,CAACG,OAAO,CAAC,EAAE,CAAC;EACtCO,UAAU,EAAG,GAAE/D,KAAK,CAAC,EAAE,GAAG,EAAE,CAAE,IAAG;EACjC4D,UAAU,EAAEpC,KAAK,CAAC6B,UAAU,CAACW;AAC/B,CAAC,EAAE;EACD,CAAE,IAAGtE,cAAc,CAACiB,MAAO,mCAAkC,GAAGtC,QAAQ,CAAC;IACvEyD,eAAe,EAAE;EACnB,CAAC,EAAE,CAAC1B,UAAU,CAAC6B,KAAK,GAAG5D,QAAQ,CAAC;IAC9BgE,WAAW,EAAE;EACf,CAAC,EAAEjC,UAAU,CAACI,KAAK,IAAI;IACrB6B,WAAW,EAAE;EACf,CAAC,CAAC,GAAGhE,QAAQ,CAAC;IACZ8D,UAAU,EAAE;EACd,CAAC,EAAE/B,UAAU,CAACI,KAAK,IAAI;IACrB2B,UAAU,EAAE;EACd,CAAC,CAAC,CAAC;EACH,CAAE,IAAGzC,cAAc,CAACiB,MAAO,oCAAmC,GAAGtC,QAAQ,CAAC;IACxEyD,eAAe,EAAE;EACnB,CAAC,EAAE,CAAC1B,UAAU,CAAC6B,KAAK,GAAG5D,QAAQ,CAAC;IAC9B8D,UAAU,EAAE;EACd,CAAC,EAAE/B,UAAU,CAACI,KAAK,IAAI;IACrB2B,UAAU,EAAE;EACd,CAAC,CAAC,GAAG9D,QAAQ,CAAC;IACZgE,WAAW,EAAE;EACf,CAAC,EAAEjC,UAAU,CAACI,KAAK,IAAI;IACrB6B,WAAW,EAAE;EACf,CAAC,CAAC,CAAC;EACH,CAAE,IAAG3C,cAAc,CAACiB,MAAO,kCAAiC,GAAGtC,QAAQ,CAAC;IACtEyD,eAAe,EAAE,eAAe;IAChCE,YAAY,EAAE;EAChB,CAAC,EAAE5B,UAAU,CAACI,KAAK,IAAI;IACrBwB,YAAY,EAAE;EAChB,CAAC,CAAC;EACF,CAAE,IAAGtC,cAAc,CAACiB,MAAO,qCAAoC,GAAGtC,QAAQ,CAAC;IACzEyD,eAAe,EAAE,YAAY;IAC7BD,SAAS,EAAE;EACb,CAAC,EAAEzB,UAAU,CAACI,KAAK,IAAI;IACrBqB,SAAS,EAAE;EACb,CAAC;AACH,CAAC,CAAC,CAAC;AACH,MAAMoC,YAAY,GAAGlF,MAAM,CAAC,MAAM,EAAE;EAClCgC,IAAI,EAAE,YAAY;EAClBC,IAAI,EAAE,OAAO;EACbC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACZ;AAC/C,CAAC,CAAC,CAAC,CAAC;EACFiB;AACF,CAAC,MAAM;EACL0C,QAAQ,EAAE,QAAQ;EAClBJ,QAAQ,EAAE,UAAU;EACpBvB,KAAK,EAAE,KAAK;EACZD,MAAM,EAAE,QAAQ,CAAC;EACjB6B,SAAS,EAAE,YAAY;EACvBlB,KAAK,EAAEzB,KAAK,CAACE,IAAI,GAAGF,KAAK,CAACE,IAAI,CAACiB,OAAO,CAACC,OAAO,CAACC,EAAE,GAAG/D,KAAK,CAAC0C,KAAK,CAACmB,OAAO,CAACG,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC;EACvF,WAAW,EAAE;IACXsB,OAAO,EAAE,IAAI;IACbV,MAAM,EAAE,MAAM;IACdW,OAAO,EAAE,OAAO;IAChB9B,KAAK,EAAE,MAAM;IACbD,MAAM,EAAE,MAAM;IACdI,eAAe,EAAE,cAAc;IAC/B4B,SAAS,EAAE;EACb;AACF,CAAC,CAAC,CAAC;AACH,IAAIC,aAAa,GAAG,KAAK;AACzB,IAAIC,cAAc,GAAG,IAAI;AACzB,IAAIC,cAAc,GAAG;EACnBC,CAAC,EAAE,CAAC;EACJC,CAAC,EAAE;AACL,CAAC;AACD,OAAO,SAASC,SAASA,CAAA,EAAG;EAC1BL,aAAa,GAAG,KAAK;EACrBM,YAAY,CAACL,cAAc,CAAC;AAC9B;AACA,SAASM,mBAAmBA,CAACC,OAAO,EAAEC,YAAY,EAAE;EAClD,OAAOC,KAAK,IAAI;IACd,IAAID,YAAY,EAAE;MAChBA,YAAY,CAACC,KAAK,CAAC;IACrB;IACAF,OAAO,CAACE,KAAK,CAAC;EAChB,CAAC;AACH;;AAEA;AACA,MAAMrC,OAAO,GAAG,aAAarE,KAAK,CAAC2G,UAAU,CAAC,SAAStC,OAAOA,CAACuC,OAAO,EAAEC,GAAG,EAAE;EAC3E,IAAIC,IAAI,EAAEC,aAAa,EAAEC,KAAK,EAAEC,KAAK,EAAEC,iBAAiB,EAAEC,KAAK,EAAEC,cAAc,EAAEC,KAAK,EAAEC,YAAY,EAAEC,iBAAiB,EAAEC,KAAK,EAAEC,kBAAkB,EAAEC,qBAAqB,EAAEC,kBAAkB,EAAEC,KAAK,EAAEC,mBAAmB,EAAEC,gBAAgB,EAAEC,KAAK,EAAEC,iBAAiB;EACrQ,MAAMrF,KAAK,GAAGjC,aAAa,CAAC;IAC1BiC,KAAK,EAAEiE,OAAO;IACdpE,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFR,KAAK,GAAG,KAAK;MACbiG,QAAQ,EAAEC,YAAY;MACtBC,UAAU,GAAG,CAAC,CAAC;MACfC,eAAe,GAAG,CAAC,CAAC;MACpBC,aAAa,GAAG,KAAK;MACrBC,oBAAoB,GAAG,KAAK;MAC5BC,oBAAoB,GAAG,KAAK;MAC5BxG,kBAAkB,EAAEyG,sBAAsB,GAAG,KAAK;MAClDC,oBAAoB,GAAG,KAAK;MAC5BC,UAAU,GAAG,GAAG;MAChBC,cAAc,GAAG,CAAC;MAClBC,eAAe,GAAG,GAAG;MACrBC,YAAY,GAAG,KAAK;MACpBC,EAAE,EAAEC,MAAM;MACVC,UAAU,GAAG,CAAC;MACdC,eAAe,GAAG,IAAI;MACtBC,OAAO;MACPC,MAAM;MACNpG,IAAI,EAAEqG,QAAQ;MACdlH,SAAS,GAAG,QAAQ;MACpBmH,eAAe,EAAEC,mBAAmB;MACpCC,WAAW,GAAG,CAAC,CAAC;MAChBC,SAAS,GAAG,CAAC,CAAC;MACdrH,KAAK,GAAG,CAAC,CAAC;MACVsH,KAAK;MACLC,mBAAmB,EAAEC,uBAAuB,GAAG/I,IAAI;MACnDgJ;IACF,CAAC,GAAGjH,KAAK;IACTkH,KAAK,GAAGhK,6BAA6B,CAAC8C,KAAK,EAAE5C,SAAS,CAAC;;EAEzD;EACA,MAAMkI,QAAQ,GAAG,aAAajI,KAAK,CAAC8J,cAAc,CAAC5B,YAAY,CAAC,GAAGA,YAAY,GAAG,aAAa5G,IAAI,CAAC,MAAM,EAAE;IAC1G2G,QAAQ,EAAEC;EACZ,CAAC,CAAC;EACF,MAAMjF,KAAK,GAAGxC,QAAQ,CAAC,CAAC;EACxB,MAAMiD,KAAK,GAAGT,KAAK,CAAC8G,SAAS,KAAK,KAAK;EACvC,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGjK,KAAK,CAACkK,QAAQ,CAAC,CAAC;EAClD,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGpK,KAAK,CAACkK,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAMG,oBAAoB,GAAGrK,KAAK,CAACsK,MAAM,CAAC,KAAK,CAAC;EAChD,MAAMvI,kBAAkB,GAAGyG,sBAAsB,IAAIK,YAAY;EACjE,MAAM0B,UAAU,GAAGvK,KAAK,CAACsK,MAAM,CAAC,CAAC;EACjC,MAAME,UAAU,GAAGxK,KAAK,CAACsK,MAAM,CAAC,CAAC;EACjC,MAAMG,UAAU,GAAGzK,KAAK,CAACsK,MAAM,CAAC,CAAC;EACjC,MAAMI,UAAU,GAAG1K,KAAK,CAACsK,MAAM,CAAC,CAAC;EACjC,MAAM,CAACK,SAAS,EAAEC,YAAY,CAAC,GAAG1J,aAAa,CAAC;IAC9C2J,UAAU,EAAEzB,QAAQ;IACpB0B,OAAO,EAAE,KAAK;IACdtI,IAAI,EAAE,SAAS;IACfuI,KAAK,EAAE;EACT,CAAC,CAAC;EACF,IAAIhI,IAAI,GAAG4H,SAAS;EACpB,IAAIK,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC;IACA,MAAM;MACJC,OAAO,EAAEC;IACX,CAAC,GAAGpL,KAAK,CAACsK,MAAM,CAAClB,QAAQ,KAAKiC,SAAS,CAAC;;IAExC;IACArL,KAAK,CAACsL,SAAS,CAAC,MAAM;MACpB,IAAItB,SAAS,IAAIA,SAAS,CAACuB,QAAQ,IAAI,CAACH,YAAY,IAAI3B,KAAK,KAAK,EAAE,IAAIO,SAAS,CAACwB,OAAO,CAACC,WAAW,CAAC,CAAC,KAAK,QAAQ,EAAE;QACpHC,OAAO,CAACC,KAAK,CAAC,CAAC,4EAA4E,EAAE,0CAA0C,EAAE,6EAA6E,EAAE,EAAE,EAAE,iDAAiD,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;MAC5R;IACF,CAAC,EAAE,CAACnC,KAAK,EAAEO,SAAS,EAAEoB,YAAY,CAAC,CAAC;EACtC;EACA,MAAMtC,EAAE,GAAG9H,KAAK,CAAC+H,MAAM,CAAC;EACxB,MAAM8C,cAAc,GAAG7L,KAAK,CAACsK,MAAM,CAAC,CAAC;EACrC,MAAMwB,oBAAoB,GAAG9L,KAAK,CAAC+L,WAAW,CAAC,MAAM;IACnD,IAAIF,cAAc,CAACV,OAAO,KAAKE,SAAS,EAAE;MACxCW,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,gBAAgB,GAAGN,cAAc,CAACV,OAAO;MAC7DU,cAAc,CAACV,OAAO,GAAGE,SAAS;IACpC;IACA/E,YAAY,CAACoE,UAAU,CAACS,OAAO,CAAC;EAClC,CAAC,EAAE,EAAE,CAAC;EACNnL,KAAK,CAACsL,SAAS,CAAC,MAAM;IACpB,OAAO,MAAM;MACXhF,YAAY,CAACiE,UAAU,CAACY,OAAO,CAAC;MAChC7E,YAAY,CAACkE,UAAU,CAACW,OAAO,CAAC;MAChC7E,YAAY,CAACmE,UAAU,CAACU,OAAO,CAAC;MAChCW,oBAAoB,CAAC,CAAC;IACxB,CAAC;EACH,CAAC,EAAE,CAACA,oBAAoB,CAAC,CAAC;EAC1B,MAAMM,UAAU,GAAG1F,KAAK,IAAI;IAC1BJ,YAAY,CAACL,cAAc,CAAC;IAC5BD,aAAa,GAAG,IAAI;;IAEpB;IACA;IACA;IACA4E,YAAY,CAAC,IAAI,CAAC;IAClB,IAAIzB,MAAM,IAAI,CAACpG,IAAI,EAAE;MACnBoG,MAAM,CAACzC,KAAK,CAAC;IACf;EACF,CAAC;EACD,MAAM2F,WAAW,GAAGvL,gBAAgB;EACpC;AACF;AACA;EACE4F,KAAK,IAAI;IACPJ,YAAY,CAACL,cAAc,CAAC;IAC5BA,cAAc,GAAGqG,UAAU,CAAC,MAAM;MAChCtG,aAAa,GAAG,KAAK;IACvB,CAAC,EAAE,GAAG,GAAGgD,UAAU,CAAC;IACpB4B,YAAY,CAAC,KAAK,CAAC;IACnB,IAAI1B,OAAO,IAAInG,IAAI,EAAE;MACnBmG,OAAO,CAACxC,KAAK,CAAC;IAChB;IACAJ,YAAY,CAACiE,UAAU,CAACY,OAAO,CAAC;IAChCZ,UAAU,CAACY,OAAO,GAAGmB,UAAU,CAAC,MAAM;MACpCjC,oBAAoB,CAACc,OAAO,GAAG,KAAK;IACtC,CAAC,EAAElI,KAAK,CAACsJ,WAAW,CAACC,QAAQ,CAACC,QAAQ,CAAC;EACzC,CAAC,CAAC;EACF,MAAMC,WAAW,GAAGhG,KAAK,IAAI;IAC3B,IAAI2D,oBAAoB,CAACc,OAAO,IAAIzE,KAAK,CAACiG,IAAI,KAAK,YAAY,EAAE;MAC/D;IACF;;IAEA;IACA;IACA;IACA,IAAI3C,SAAS,EAAE;MACbA,SAAS,CAAC4C,eAAe,CAAC,OAAO,CAAC;IACpC;IACAtG,YAAY,CAACkE,UAAU,CAACW,OAAO,CAAC;IAChC7E,YAAY,CAACmE,UAAU,CAACU,OAAO,CAAC;IAChC,IAAIzC,UAAU,IAAI1C,aAAa,IAAI2C,cAAc,EAAE;MACjD6B,UAAU,CAACW,OAAO,GAAGmB,UAAU,CAAC,MAAM;QACpCF,UAAU,CAAC1F,KAAK,CAAC;MACnB,CAAC,EAAEV,aAAa,GAAG2C,cAAc,GAAGD,UAAU,CAAC;IACjD,CAAC,MAAM;MACL0D,UAAU,CAAC1F,KAAK,CAAC;IACnB;EACF,CAAC;EACD,MAAMmG,WAAW,GAAGnG,KAAK,IAAI;IAC3BJ,YAAY,CAACkE,UAAU,CAACW,OAAO,CAAC;IAChC7E,YAAY,CAACmE,UAAU,CAACU,OAAO,CAAC;IAChCV,UAAU,CAACU,OAAO,GAAGmB,UAAU,CAAC,MAAM;MACpCD,WAAW,CAAC3F,KAAK,CAAC;IACpB,CAAC,EAAEsC,UAAU,CAAC;EAChB,CAAC;EACD,MAAM;IACJ8D,iBAAiB;IACjBC,MAAM,EAAEC,iBAAiB;IACzBC,OAAO,EAAEC,kBAAkB;IAC3BrG,GAAG,EAAEsG;EACP,CAAC,GAAGlM,iBAAiB,CAAC,CAAC;EACvB;EACA;EACA,MAAM,GAAGmM,sBAAsB,CAAC,GAAGpN,KAAK,CAACkK,QAAQ,CAAC,KAAK,CAAC;EACxD,MAAMmD,UAAU,GAAG3G,KAAK,IAAI;IAC1BsG,iBAAiB,CAACtG,KAAK,CAAC;IACxB,IAAIoG,iBAAiB,CAAC3B,OAAO,KAAK,KAAK,EAAE;MACvCiC,sBAAsB,CAAC,KAAK,CAAC;MAC7BP,WAAW,CAACnG,KAAK,CAAC;IACpB;EACF,CAAC;EACD,MAAM4G,WAAW,GAAG5G,KAAK,IAAI;IAC3B;IACA;IACA;IACA,IAAI,CAACsD,SAAS,EAAE;MACdC,YAAY,CAACvD,KAAK,CAAC6G,aAAa,CAAC;IACnC;IACAL,kBAAkB,CAACxG,KAAK,CAAC;IACzB,IAAIoG,iBAAiB,CAAC3B,OAAO,KAAK,IAAI,EAAE;MACtCiC,sBAAsB,CAAC,IAAI,CAAC;MAC5BV,WAAW,CAAChG,KAAK,CAAC;IACpB;EACF,CAAC;EACD,MAAM8G,gBAAgB,GAAG9G,KAAK,IAAI;IAChC2D,oBAAoB,CAACc,OAAO,GAAG,IAAI;IACnC,MAAMsC,aAAa,GAAGxF,QAAQ,CAACtF,KAAK;IACpC,IAAI8K,aAAa,CAACC,YAAY,EAAE;MAC9BD,aAAa,CAACC,YAAY,CAAChH,KAAK,CAAC;IACnC;EACF,CAAC;EACD,MAAMiH,eAAe,GAAGjB,WAAW;EACnC,MAAMkB,gBAAgB,GAAGf,WAAW;EACpC,MAAMgB,gBAAgB,GAAGnH,KAAK,IAAI;IAChC8G,gBAAgB,CAAC9G,KAAK,CAAC;IACvBJ,YAAY,CAACmE,UAAU,CAACU,OAAO,CAAC;IAChC7E,YAAY,CAACiE,UAAU,CAACY,OAAO,CAAC;IAChCW,oBAAoB,CAAC,CAAC;IACtBD,cAAc,CAACV,OAAO,GAAGa,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,gBAAgB;IAC7D;IACAH,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,gBAAgB,GAAG,MAAM;IAC7CzB,UAAU,CAACS,OAAO,GAAGmB,UAAU,CAAC,MAAM;MACpCN,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,gBAAgB,GAAGN,cAAc,CAACV,OAAO;MAC7DuB,WAAW,CAAChG,KAAK,CAAC;IACpB,CAAC,EAAEkC,eAAe,CAAC;EACrB,CAAC;EACD,MAAMkF,cAAc,GAAGpH,KAAK,IAAI;IAC9B,IAAIuB,QAAQ,CAACtF,KAAK,CAACoL,UAAU,EAAE;MAC7B9F,QAAQ,CAACtF,KAAK,CAACoL,UAAU,CAACrH,KAAK,CAAC;IAClC;IACAoF,oBAAoB,CAAC,CAAC;IACtBxF,YAAY,CAACmE,UAAU,CAACU,OAAO,CAAC;IAChCV,UAAU,CAACU,OAAO,GAAGmB,UAAU,CAAC,MAAM;MACpCD,WAAW,CAAC3F,KAAK,CAAC;IACpB,CAAC,EAAEuC,eAAe,CAAC;EACrB,CAAC;EACDjJ,KAAK,CAACsL,SAAS,CAAC,MAAM;IACpB,IAAI,CAACvI,IAAI,EAAE;MACT,OAAOsI,SAAS;IAClB;;IAEA;AACJ;AACA;IACI,SAAS2C,aAAaA,CAACC,WAAW,EAAE;MAClC;MACA,IAAIA,WAAW,CAACC,GAAG,KAAK,QAAQ,IAAID,WAAW,CAACC,GAAG,KAAK,KAAK,EAAE;QAC7D7B,WAAW,CAAC4B,WAAW,CAAC;MAC1B;IACF;IACAjC,QAAQ,CAACmC,gBAAgB,CAAC,SAAS,EAAEH,aAAa,CAAC;IACnD,OAAO,MAAM;MACXhC,QAAQ,CAACoC,mBAAmB,CAAC,SAAS,EAAEJ,aAAa,CAAC;IACxD,CAAC;EACH,CAAC,EAAE,CAAC3B,WAAW,EAAEtJ,IAAI,CAAC,CAAC;EACvB,MAAMsL,SAAS,GAAGtN,UAAU,CAACkH,QAAQ,CAACpB,GAAG,EAAEsG,eAAe,EAAElD,YAAY,EAAEpD,GAAG,CAAC;;EAE9E;EACA,IAAI,CAAC4C,KAAK,IAAIA,KAAK,KAAK,CAAC,EAAE;IACzB1G,IAAI,GAAG,KAAK;EACd;EACA,MAAMuL,SAAS,GAAGtO,KAAK,CAACsK,MAAM,CAAC,CAAC;EAChC,MAAMiE,eAAe,GAAG7H,KAAK,IAAI;IAC/B,MAAM+G,aAAa,GAAGxF,QAAQ,CAACtF,KAAK;IACpC,IAAI8K,aAAa,CAACe,WAAW,EAAE;MAC7Bf,aAAa,CAACe,WAAW,CAAC9H,KAAK,CAAC;IAClC;IACAR,cAAc,GAAG;MACfC,CAAC,EAAEO,KAAK,CAAC+H,OAAO;MAChBrI,CAAC,EAAEM,KAAK,CAACgI;IACX,CAAC;IACD,IAAIJ,SAAS,CAACnD,OAAO,EAAE;MACrBmD,SAAS,CAACnD,OAAO,CAACwD,MAAM,CAAC,CAAC;IAC5B;EACF,CAAC;EACD,MAAMC,eAAe,GAAG,CAAC,CAAC;EAC1B,MAAMC,aAAa,GAAG,OAAOpF,KAAK,KAAK,QAAQ;EAC/C,IAAIpB,aAAa,EAAE;IACjBuG,eAAe,CAACnF,KAAK,GAAG,CAAC1G,IAAI,IAAI8L,aAAa,IAAI,CAACtG,oBAAoB,GAAGkB,KAAK,GAAG,IAAI;IACtFmF,eAAe,CAAC,kBAAkB,CAAC,GAAG7L,IAAI,GAAG+F,EAAE,GAAG,IAAI;EACxD,CAAC,MAAM;IACL8F,eAAe,CAAC,YAAY,CAAC,GAAGC,aAAa,GAAGpF,KAAK,GAAG,IAAI;IAC5DmF,eAAe,CAAC,iBAAiB,CAAC,GAAG7L,IAAI,IAAI,CAAC8L,aAAa,GAAG/F,EAAE,GAAG,IAAI;EACzE;EACA,MAAM2E,aAAa,GAAG3N,QAAQ,CAAC,CAAC,CAAC,EAAE8O,eAAe,EAAE/E,KAAK,EAAE5B,QAAQ,CAACtF,KAAK,EAAE;IACzEmM,SAAS,EAAE5O,IAAI,CAAC2J,KAAK,CAACiF,SAAS,EAAE7G,QAAQ,CAACtF,KAAK,CAACmM,SAAS,CAAC;IAC1DpB,YAAY,EAAEF,gBAAgB;IAC9B3G,GAAG,EAAEwH;EACP,CAAC,EAAExF,YAAY,GAAG;IAChB2F,WAAW,EAAED;EACf,CAAC,GAAG,CAAC,CAAC,CAAC;EACP,IAAIvD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCuC,aAAa,CAAC,iCAAiC,CAAC,GAAG,IAAI;;IAEvD;IACAzN,KAAK,CAACsL,SAAS,CAAC,MAAM;MACpB,IAAItB,SAAS,IAAI,CAACA,SAAS,CAAC+E,YAAY,CAAC,iCAAiC,CAAC,EAAE;QAC3ErD,OAAO,CAACC,KAAK,CAAC,CAAC,qFAAqF,EAAE,wFAAwF,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;MAC7M;IACF,CAAC,EAAE,CAAC5B,SAAS,CAAC,CAAC;EACjB;EACA,MAAMgF,2BAA2B,GAAG,CAAC,CAAC;EACtC,IAAI,CAACvG,oBAAoB,EAAE;IACzBgF,aAAa,CAACC,YAAY,GAAGG,gBAAgB;IAC7CJ,aAAa,CAACM,UAAU,GAAGD,cAAc;EAC3C;EACA,IAAI,CAACvF,oBAAoB,EAAE;IACzBkF,aAAa,CAACwB,WAAW,GAAG1I,mBAAmB,CAACoH,eAAe,EAAEF,aAAa,CAACwB,WAAW,CAAC;IAC3FxB,aAAa,CAACyB,YAAY,GAAG3I,mBAAmB,CAACqH,gBAAgB,EAAEH,aAAa,CAACyB,YAAY,CAAC;IAC9F,IAAI,CAACnN,kBAAkB,EAAE;MACvBiN,2BAA2B,CAACC,WAAW,GAAGtB,eAAe;MACzDqB,2BAA2B,CAACE,YAAY,GAAGtB,gBAAgB;IAC7D;EACF;EACA,IAAI,CAACtF,oBAAoB,EAAE;IACzBmF,aAAa,CAACR,OAAO,GAAG1G,mBAAmB,CAAC+G,WAAW,EAAEG,aAAa,CAACR,OAAO,CAAC;IAC/EQ,aAAa,CAACV,MAAM,GAAGxG,mBAAmB,CAAC8G,UAAU,EAAEI,aAAa,CAACV,MAAM,CAAC;IAC5E,IAAI,CAAChL,kBAAkB,EAAE;MACvBiN,2BAA2B,CAAC/B,OAAO,GAAGK,WAAW;MACjD0B,2BAA2B,CAACjC,MAAM,GAAGM,UAAU;IACjD;EACF;EACA,IAAIrC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAIjD,QAAQ,CAACtF,KAAK,CAAC8G,KAAK,EAAE;MACxBiC,OAAO,CAACC,KAAK,CAAC,CAAC,oEAAoE,EAAG,4BAA2B1D,QAAQ,CAACtF,KAAK,CAAC8G,KAAM,8BAA6B,CAAC,CAACmC,IAAI,CAAC,IAAI,CAAC,CAAC;IAClL;EACF;EACA,MAAMuD,aAAa,GAAGnP,KAAK,CAACoP,OAAO,CAAC,MAAM;IACxC,IAAIC,qBAAqB;IACzB,IAAIC,gBAAgB,GAAG,CAAC;MACtB9M,IAAI,EAAE,OAAO;MACb+M,OAAO,EAAEC,OAAO,CAACrF,QAAQ,CAAC;MAC1BsF,OAAO,EAAE;QACPC,OAAO,EAAEvF,QAAQ;QACjBpF,OAAO,EAAE;MACX;IACF,CAAC,CAAC;IACF,IAAI,CAACsK,qBAAqB,GAAG9F,WAAW,CAAC4F,aAAa,KAAK,IAAI,IAAIE,qBAAqB,CAACM,SAAS,EAAE;MAClGL,gBAAgB,GAAGA,gBAAgB,CAACM,MAAM,CAACrG,WAAW,CAAC4F,aAAa,CAACQ,SAAS,CAAC;IACjF;IACA,OAAO7P,QAAQ,CAAC,CAAC,CAAC,EAAEyJ,WAAW,CAAC4F,aAAa,EAAE;MAC7CQ,SAAS,EAAEL;IACb,CAAC,CAAC;EACJ,CAAC,EAAE,CAACnF,QAAQ,EAAEZ,WAAW,CAAC,CAAC;EAC3B,MAAM1H,UAAU,GAAG/B,QAAQ,CAAC,CAAC,CAAC,EAAE6C,KAAK,EAAE;IACrCe,KAAK;IACL1B,KAAK;IACLD,kBAAkB;IAClBG,SAAS;IACToH,mBAAmB;IACnBrH,KAAK,EAAEoI,oBAAoB,CAACc;EAC9B,CAAC,CAAC;EACF,MAAMrJ,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAMwH,eAAe,GAAG,CAACvC,IAAI,GAAG,CAACC,aAAa,GAAG5E,KAAK,CAACC,MAAM,KAAK,IAAI,GAAG2E,aAAa,GAAGoB,UAAU,CAACtH,MAAM,KAAK,IAAI,GAAGiG,IAAI,GAAGvE,aAAa;EAC1I,MAAMmH,mBAAmB,GAAG,CAAC1C,KAAK,GAAG,CAACC,KAAK,GAAG,CAACC,iBAAiB,GAAG/E,KAAK,CAAC0N,UAAU,KAAK,IAAI,GAAG3I,iBAAiB,GAAGiB,UAAU,CAAC2H,UAAU,KAAK,IAAI,GAAG7I,KAAK,GAAG0C,uBAAuB,KAAK,IAAI,GAAG3C,KAAK,GAAGpG,IAAI;EAC3M,MAAMmP,gBAAgB,GAAG,CAAC5I,KAAK,GAAG,CAACC,cAAc,GAAGjF,KAAK,CAACE,OAAO,KAAK,IAAI,GAAG+E,cAAc,GAAGe,UAAU,CAAC9D,OAAO,KAAK,IAAI,GAAG8C,KAAK,GAAGlD,cAAc;EAClJ,MAAM+L,cAAc,GAAG,CAAC3I,KAAK,GAAG,CAACC,YAAY,GAAGnF,KAAK,CAACH,KAAK,KAAK,IAAI,GAAGsF,YAAY,GAAGa,UAAU,CAAC8H,KAAK,KAAK,IAAI,GAAG5I,KAAK,GAAG3B,YAAY;EACtI,MAAMwK,WAAW,GAAG5P,gBAAgB,CAAC+I,eAAe,EAAEvJ,QAAQ,CAAC,CAAC,CAAC,EAAEyJ,WAAW,EAAE,CAAChC,iBAAiB,GAAGiC,SAAS,CAACpH,MAAM,KAAK,IAAI,GAAGmF,iBAAiB,GAAGa,eAAe,CAAChG,MAAM,EAAE;IAC3K0M,SAAS,EAAE5O,IAAI,CAAC4B,OAAO,CAACM,MAAM,EAAEmH,WAAW,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACuF,SAAS,EAAE,CAACtH,KAAK,GAAG,CAACC,kBAAkB,GAAG+B,SAAS,CAACpH,MAAM,KAAK,IAAI,GAAGqF,kBAAkB,GAAGW,eAAe,CAAChG,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGoF,KAAK,CAACsH,SAAS;EAClO,CAAC,CAAC,EAAEjN,UAAU,CAAC;EACf,MAAMsO,eAAe,GAAG7P,gBAAgB,CAACoJ,mBAAmB,EAAE5J,QAAQ,CAAC,CAAC,CAAC,EAAE8J,eAAe,EAAE,CAAClC,qBAAqB,GAAG8B,SAAS,CAACqG,UAAU,KAAK,IAAI,GAAGnI,qBAAqB,GAAGU,eAAe,CAACyH,UAAU,CAAC,EAAEhO,UAAU,CAAC;EACrN,MAAMuO,YAAY,GAAG9P,gBAAgB,CAACyP,gBAAgB,EAAEjQ,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC6H,kBAAkB,GAAG6B,SAAS,CAACnH,OAAO,KAAK,IAAI,GAAGsF,kBAAkB,GAAGS,eAAe,CAAC/F,OAAO,EAAE;IACpKyM,SAAS,EAAE5O,IAAI,CAAC4B,OAAO,CAACO,OAAO,EAAE,CAACuF,KAAK,GAAG,CAACC,mBAAmB,GAAG2B,SAAS,CAACnH,OAAO,KAAK,IAAI,GAAGwF,mBAAmB,GAAGO,eAAe,CAAC/F,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGuF,KAAK,CAACkH,SAAS;EACjL,CAAC,CAAC,EAAEjN,UAAU,CAAC;EACf,MAAMwO,iBAAiB,GAAG/P,gBAAgB,CAAC0P,cAAc,EAAElQ,QAAQ,CAAC,CAAC,CAAC,EAAE,CAACgI,gBAAgB,GAAG0B,SAAS,CAACxH,KAAK,KAAK,IAAI,GAAG8F,gBAAgB,GAAGM,eAAe,CAACpG,KAAK,EAAE;IAC/J8M,SAAS,EAAE5O,IAAI,CAAC4B,OAAO,CAACE,KAAK,EAAE,CAAC+F,KAAK,GAAG,CAACC,iBAAiB,GAAGwB,SAAS,CAACxH,KAAK,KAAK,IAAI,GAAGgG,iBAAiB,GAAGI,eAAe,CAACpG,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG+F,KAAK,CAAC+G,SAAS;EACvK,CAAC,CAAC,EAAEjN,UAAU,CAAC;EACf,OAAO,aAAaL,KAAK,CAACxB,KAAK,CAACsQ,QAAQ,EAAE;IACxCrI,QAAQ,EAAE,CAAC,aAAajI,KAAK,CAACuQ,YAAY,CAACtI,QAAQ,EAAEwF,aAAa,CAAC,EAAE,aAAanM,IAAI,CAAC+H,eAAe,EAAEvJ,QAAQ,CAAC;MAC/G0Q,EAAE,EAAElH,mBAAmB,IAAI,IAAI,GAAGA,mBAAmB,GAAGzI,MAAM;MAC9DqB,SAAS,EAAEA,SAAS;MACpBuO,QAAQ,EAAE5H,YAAY,GAAG;QACvB6H,qBAAqB,EAAEA,CAAA,MAAO;UAC5BrN,GAAG,EAAE6C,cAAc,CAACE,CAAC;UACrBzC,IAAI,EAAEuC,cAAc,CAACC,CAAC;UACtBtC,KAAK,EAAEqC,cAAc,CAACC,CAAC;UACvB3C,MAAM,EAAE0C,cAAc,CAACE,CAAC;UACxBpC,KAAK,EAAE,CAAC;UACRD,MAAM,EAAE;QACV,CAAC;MACH,CAAC,GAAGiG,SAAS;MACbsE,SAAS,EAAEA,SAAS;MACpBvL,IAAI,EAAEiH,SAAS,GAAGjH,IAAI,GAAG,KAAK;MAC9B+F,EAAE,EAAEA,EAAE;MACN+G,UAAU,EAAE;IACd,CAAC,EAAEb,2BAA2B,EAAEkB,WAAW,EAAE;MAC3Cf,aAAa,EAAEA,aAAa;MAC5BlH,QAAQ,EAAEA,CAAC;QACT2B,eAAe,EAAE+G;MACnB,CAAC,KAAK,aAAarP,IAAI,CAACoI,mBAAmB,EAAE5J,QAAQ,CAAC;QACpD8Q,OAAO,EAAE3N,KAAK,CAACsJ,WAAW,CAACC,QAAQ,CAACqE;MACtC,CAAC,EAAEF,oBAAoB,EAAER,eAAe,EAAE;QACxClI,QAAQ,EAAE,aAAazG,KAAK,CAACuO,gBAAgB,EAAEjQ,QAAQ,CAAC,CAAC,CAAC,EAAEsQ,YAAY,EAAE;UACxEnI,QAAQ,EAAE,CAACwB,KAAK,EAAEzH,KAAK,GAAG,aAAaV,IAAI,CAAC0O,cAAc,EAAElQ,QAAQ,CAAC,CAAC,CAAC,EAAEuQ,iBAAiB,EAAE;YAC1FxJ,GAAG,EAAEuD;UACP,CAAC,CAAC,CAAC,GAAG,IAAI;QACZ,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;AACJ,CAAC,CAAC;AACFY,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG7G,OAAO,CAACyM,SAAS,CAAC,yBAAyB;EACjF;EACA;EACA;EACA;EACA;AACF;AACA;AACA;EACE9O,KAAK,EAAE/B,SAAS,CAAC8Q,IAAI;EACrB;AACF;AACA;EACE9I,QAAQ,EAAE9H,mBAAmB,CAAC6Q,UAAU;EACxC;AACF;AACA;EACElP,OAAO,EAAE7B,SAAS,CAACgR,MAAM;EACzB;AACF;AACA;EACEnC,SAAS,EAAE7O,SAAS,CAACiR,MAAM;EAC3B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE/I,UAAU,EAAElI,SAAS,CAACwE,KAAK,CAAC;IAC1BwL,KAAK,EAAEhQ,SAAS,CAACkR,WAAW;IAC5BtQ,MAAM,EAAEZ,SAAS,CAACkR,WAAW;IAC7B9M,OAAO,EAAEpE,SAAS,CAACkR,WAAW;IAC9BrB,UAAU,EAAE7P,SAAS,CAACkR;EACxB,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE/I,eAAe,EAAEnI,SAAS,CAACwE,KAAK,CAAC;IAC/BzC,KAAK,EAAE/B,SAAS,CAACgR,MAAM;IACvB7O,MAAM,EAAEnC,SAAS,CAACgR,MAAM;IACxB5O,OAAO,EAAEpC,SAAS,CAACgR,MAAM;IACzBpB,UAAU,EAAE5P,SAAS,CAACgR;EACxB,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;EACE5I,aAAa,EAAEpI,SAAS,CAAC8Q,IAAI;EAC7B;AACF;AACA;AACA;EACEzI,oBAAoB,EAAErI,SAAS,CAAC8Q,IAAI;EACpC;AACF;AACA;AACA;EACExI,oBAAoB,EAAEtI,SAAS,CAAC8Q,IAAI;EACpC;AACF;AACA;AACA;AACA;EACEhP,kBAAkB,EAAE9B,SAAS,CAAC8Q,IAAI;EAClC;AACF;AACA;AACA;EACEtI,oBAAoB,EAAExI,SAAS,CAAC8Q,IAAI;EACpC;AACF;AACA;AACA;AACA;EACErI,UAAU,EAAEzI,SAAS,CAACmR,MAAM;EAC5B;AACF;AACA;AACA;EACEzI,cAAc,EAAE1I,SAAS,CAACmR,MAAM;EAChC;AACF;AACA;AACA;EACExI,eAAe,EAAE3I,SAAS,CAACmR,MAAM;EACjC;AACF;AACA;AACA;EACEvI,YAAY,EAAE5I,SAAS,CAAC8Q,IAAI;EAC5B;AACF;AACA;AACA;EACEjI,EAAE,EAAE7I,SAAS,CAACiR,MAAM;EACpB;AACF;AACA;AACA;AACA;EACElI,UAAU,EAAE/I,SAAS,CAACmR,MAAM;EAC5B;AACF;AACA;AACA;EACEnI,eAAe,EAAEhJ,SAAS,CAACmR,MAAM;EACjC;AACF;AACA;AACA;AACA;EACElI,OAAO,EAAEjJ,SAAS,CAACoR,IAAI;EACvB;AACF;AACA;AACA;AACA;EACElI,MAAM,EAAElJ,SAAS,CAACoR,IAAI;EACtB;AACF;AACA;EACEtO,IAAI,EAAE9C,SAAS,CAAC8Q,IAAI;EACpB;AACF;AACA;AACA;EACE7O,SAAS,EAAEjC,SAAS,CAACqR,KAAK,CAAC,CAAC,YAAY,EAAE,cAAc,EAAE,QAAQ,EAAE,UAAU,EAAE,YAAY,EAAE,MAAM,EAAE,WAAW,EAAE,aAAa,EAAE,OAAO,EAAE,SAAS,EAAE,WAAW,EAAE,KAAK,CAAC,CAAC;EAC1K;AACF;AACA;AACA;EACEjI,eAAe,EAAEpJ,SAAS,CAACkR,WAAW;EACtC;AACF;AACA;AACA;EACE5H,WAAW,EAAEtJ,SAAS,CAACgR,MAAM;EAC7B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEzH,SAAS,EAAEvJ,SAAS,CAACwE,KAAK,CAAC;IACzBzC,KAAK,EAAE/B,SAAS,CAACgR,MAAM;IACvB7O,MAAM,EAAEnC,SAAS,CAACgR,MAAM;IACxB5O,OAAO,EAAEpC,SAAS,CAACgR,MAAM;IACzBpB,UAAU,EAAE5P,SAAS,CAACgR;EACxB,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;AACA;AACA;EACE9O,KAAK,EAAElC,SAAS,CAACwE,KAAK,CAAC;IACrBzC,KAAK,EAAE/B,SAAS,CAACkR,WAAW;IAC5B/O,MAAM,EAAEnC,SAAS,CAACkR,WAAW;IAC7B9O,OAAO,EAAEpC,SAAS,CAACkR,WAAW;IAC9BtB,UAAU,EAAE5P,SAAS,CAACkR;EACxB,CAAC,CAAC;EACF;AACF;AACA;EACEI,EAAE,EAAEtR,SAAS,CAACuR,SAAS,CAAC,CAACvR,SAAS,CAACwR,OAAO,CAACxR,SAAS,CAACuR,SAAS,CAAC,CAACvR,SAAS,CAACoR,IAAI,EAAEpR,SAAS,CAACgR,MAAM,EAAEhR,SAAS,CAAC8Q,IAAI,CAAC,CAAC,CAAC,EAAE9Q,SAAS,CAACoR,IAAI,EAAEpR,SAAS,CAACgR,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;EACExH,KAAK,EAAExJ,SAAS,CAACyR,IAAI;EACrB;AACF;AACA;AACA;AACA;EACEhI,mBAAmB,EAAEzJ,SAAS,CAACkR,WAAW;EAC1C;AACF;AACA;AACA;EACEvH,eAAe,EAAE3J,SAAS,CAACgR;AAC7B,CAAC,GAAG,KAAK,CAAC;AACV,eAAe5M,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}