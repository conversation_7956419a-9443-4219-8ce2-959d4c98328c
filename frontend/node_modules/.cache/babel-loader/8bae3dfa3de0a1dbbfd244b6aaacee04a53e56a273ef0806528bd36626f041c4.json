{"ast": null, "code": "'use client';\n\nexport { useNumberInput as unstable_useNumberInput } from './useNumberInput';\nexport * from './useNumberInput.types';", "map": {"version": 3, "names": ["useNumberInput", "unstable_useNumberInput"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/base/unstable_useNumberInput/index.js"], "sourcesContent": ["'use client';\n\nexport { useNumberInput as unstable_useNumberInput } from './useNumberInput';\nexport * from './useNumberInput.types';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,cAAc,IAAIC,uBAAuB,QAAQ,kBAAkB;AAC5E,cAAc,wBAAwB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}