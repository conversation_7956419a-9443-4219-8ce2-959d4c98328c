{"ast": null, "code": "import { unstable_getScrollbarSize as getScrollbarSize } from '@mui/utils';\nexport default getScrollbarSize;", "map": {"version": 3, "names": ["unstable_getScrollbarSize", "getScrollbarSize"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/material/utils/getScrollbarSize.js"], "sourcesContent": ["import { unstable_getScrollbarSize as getScrollbarSize } from '@mui/utils';\nexport default getScrollbarSize;"], "mappings": "AAAA,SAASA,yBAAyB,IAAIC,gBAAgB,QAAQ,YAAY;AAC1E,eAAeA,gBAAgB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}