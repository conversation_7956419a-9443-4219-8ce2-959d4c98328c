{"ast": null, "code": "import { unstable_unsupportedProp as unsupportedProp } from '@mui/utils';\nexport default unsupportedProp;", "map": {"version": 3, "names": ["unstable_unsupportedProp", "unsupportedProp"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/material/utils/unsupportedProp.js"], "sourcesContent": ["import { unstable_unsupportedProp as unsupportedProp } from '@mui/utils';\nexport default unsupportedProp;"], "mappings": "AAAA,SAASA,wBAAwB,IAAIC,eAAe,QAAQ,YAAY;AACxE,eAAeA,eAAe"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}