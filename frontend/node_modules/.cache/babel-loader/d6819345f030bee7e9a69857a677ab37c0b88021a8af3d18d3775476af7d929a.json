{"ast": null, "code": "export { default } from './ownerWindow';", "map": {"version": 3, "names": ["default"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/utils/esm/ownerWindow/index.js"], "sourcesContent": ["export { default } from './ownerWindow';"], "mappings": "AAAA,SAASA,OAAO,QAAQ,eAAe"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}