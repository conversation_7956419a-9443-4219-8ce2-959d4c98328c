{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"children\", \"className\", \"component\", \"componentsProps\", \"max\", \"renderSurplus\", \"slotProps\", \"spacing\", \"total\", \"variant\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { isFragment } from 'react-is';\nimport clsx from 'clsx';\nimport { chainPropTypes } from '@mui/utils';\nimport { unstable_composeClasses as composeClasses } from '@mui/base/composeClasses';\nimport styled from '../styles/styled';\nimport useThemeProps from '../styles/useThemeProps';\nimport Avatar, { avatarClasses } from '../Avatar';\nimport avatarGroupClasses, { getAvatarGroupUtilityClass } from './avatarGroupClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst SPACINGS = {\n  small: -16,\n  medium: null\n};\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root'],\n    avatar: ['avatar']\n  };\n  return composeClasses(slots, getAvatarGroupUtilityClass, classes);\n};\nconst AvatarGroupRoot = styled('div', {\n  name: 'MuiAvatarGroup',\n  slot: 'Root',\n  overridesResolver: (props, styles) => _extends({\n    [`& .${avatarGroupClasses.avatar}`]: styles.avatar\n  }, styles.root)\n})(({\n  theme\n}) => ({\n  [`& .${avatarClasses.root}`]: {\n    border: `2px solid ${(theme.vars || theme).palette.background.default}`,\n    boxSizing: 'content-box',\n    marginLeft: -8,\n    '&:last-child': {\n      marginLeft: 0\n    }\n  },\n  display: 'flex',\n  flexDirection: 'row-reverse'\n}));\nconst AvatarGroupAvatar = styled(Avatar, {\n  name: 'MuiAvatarGroup',\n  slot: 'Avatar',\n  overridesResolver: (props, styles) => styles.avatar\n})(({\n  theme\n}) => ({\n  border: `2px solid ${(theme.vars || theme).palette.background.default}`,\n  boxSizing: 'content-box',\n  marginLeft: -8,\n  '&:last-child': {\n    marginLeft: 0\n  }\n}));\nconst AvatarGroup = /*#__PURE__*/React.forwardRef(function AvatarGroup(inProps, ref) {\n  var _slotProps$additional;\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiAvatarGroup'\n  });\n  const {\n      children: childrenProp,\n      className,\n      component = 'div',\n      componentsProps = {},\n      max = 5,\n      renderSurplus,\n      slotProps = {},\n      spacing = 'medium',\n      total,\n      variant = 'circular'\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  let clampedMax = max < 2 ? 2 : max;\n  const ownerState = _extends({}, props, {\n    max,\n    spacing,\n    component,\n    variant\n  });\n  const classes = useUtilityClasses(ownerState);\n  const children = React.Children.toArray(childrenProp).filter(child => {\n    if (process.env.NODE_ENV !== 'production') {\n      if (isFragment(child)) {\n        console.error([\"MUI: The AvatarGroup component doesn't accept a Fragment as a child.\", 'Consider providing an array instead.'].join('\\n'));\n      }\n    }\n    return /*#__PURE__*/React.isValidElement(child);\n  });\n  const totalAvatars = total || children.length;\n  if (totalAvatars === clampedMax) {\n    clampedMax += 1;\n  }\n  clampedMax = Math.min(totalAvatars + 1, clampedMax);\n  const maxAvatars = Math.min(children.length, clampedMax - 1);\n  const extraAvatars = Math.max(totalAvatars - clampedMax, totalAvatars - maxAvatars, 0);\n  const extraAvatarsElement = renderSurplus ? renderSurplus(extraAvatars) : `+${extraAvatars}`;\n  const marginLeft = spacing && SPACINGS[spacing] !== undefined ? SPACINGS[spacing] : -spacing;\n  const additionalAvatarSlotProps = (_slotProps$additional = slotProps.additionalAvatar) != null ? _slotProps$additional : componentsProps.additionalAvatar;\n  return /*#__PURE__*/_jsxs(AvatarGroupRoot, _extends({\n    as: component,\n    ownerState: ownerState,\n    className: clsx(classes.root, className),\n    ref: ref\n  }, other, {\n    children: [extraAvatars ? /*#__PURE__*/_jsx(AvatarGroupAvatar, _extends({\n      ownerState: ownerState,\n      variant: variant\n    }, additionalAvatarSlotProps, {\n      className: clsx(classes.avatar, additionalAvatarSlotProps == null ? void 0 : additionalAvatarSlotProps.className),\n      style: _extends({\n        marginLeft\n      }, additionalAvatarSlotProps == null ? void 0 : additionalAvatarSlotProps.style),\n      children: extraAvatarsElement\n    })) : null, children.slice(0, maxAvatars).reverse().map((child, index) => {\n      return /*#__PURE__*/React.cloneElement(child, {\n        className: clsx(child.props.className, classes.avatar),\n        style: _extends({\n          // Consistent with \"&:last-child\" styling for the default spacing,\n          // we do not apply custom marginLeft spacing on the last child\n          marginLeft: index === maxAvatars - 1 ? undefined : marginLeft\n        }, child.props.style),\n        variant: child.props.variant || variant\n      });\n    })]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? AvatarGroup.propTypes /* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit the d.ts file and run \"yarn proptypes\"     |\n  // ----------------------------------------------------------------------\n  /**\n   * The avatars to stack.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * This prop is an alias for the `slotProps` prop.\n   * It's recommended to use the `slotProps` prop instead, as `componentsProps` will be deprecated in the future.\n   *\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    additionalAvatar: PropTypes.object\n  }),\n  /**\n   * Max avatars to show before +x.\n   * @default 5\n   */\n  max: chainPropTypes(PropTypes.number, props => {\n    if (props.max < 2) {\n      return new Error(['MUI: The prop `max` should be equal to 2 or above.', 'A value below is clamped to 2.'].join('\\n'));\n    }\n    return null;\n  }),\n  /**\n   * custom renderer of extraAvatars\n   * @param {number} surplus number of extra avatars\n   * @returns {React.ReactNode} custom element to display\n   */\n  renderSurplus: PropTypes.func,\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * This prop is an alias for the `componentsProps` prop, which will be deprecated in the future.\n   *\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    additionalAvatar: PropTypes.object\n  }),\n  /**\n   * Spacing between avatars.\n   * @default 'medium'\n   */\n  spacing: PropTypes.oneOfType([PropTypes.oneOf(['medium', 'small']), PropTypes.number]),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The total number of avatars. Used for calculating the number of extra avatars.\n   * @default children.length\n   */\n  total: PropTypes.number,\n  /**\n   * The variant to use.\n   * @default 'circular'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['circular', 'rounded', 'square']), PropTypes.string])\n} : void 0;\nexport default AvatarGroup;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "React", "PropTypes", "isFragment", "clsx", "chainPropTypes", "unstable_composeClasses", "composeClasses", "styled", "useThemeProps", "Avatar", "avatarClasses", "avatarGroupClasses", "getAvatarGroupUtilityClass", "jsx", "_jsx", "jsxs", "_jsxs", "SPACINGS", "small", "medium", "useUtilityClasses", "ownerState", "classes", "slots", "root", "avatar", "AvatarGroupRoot", "name", "slot", "overridesResolver", "props", "styles", "theme", "border", "vars", "palette", "background", "default", "boxSizing", "marginLeft", "display", "flexDirection", "AvatarGroupAvatar", "AvatarGroup", "forwardRef", "inProps", "ref", "_slotProps$additional", "children", "childrenProp", "className", "component", "componentsProps", "max", "renderSurplus", "slotProps", "spacing", "total", "variant", "other", "clampedMax", "Children", "toArray", "filter", "child", "process", "env", "NODE_ENV", "console", "error", "join", "isValidElement", "totalAvatars", "length", "Math", "min", "maxAvatars", "extraAvatars", "extraAvatarsElement", "undefined", "additionalAvatarSlotProps", "additionalAvatar", "as", "style", "slice", "reverse", "map", "index", "cloneElement", "propTypes", "node", "object", "string", "elementType", "shape", "number", "Error", "func", "oneOfType", "oneOf", "sx", "arrayOf", "bool"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/material/AvatarGroup/AvatarGroup.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"children\", \"className\", \"component\", \"componentsProps\", \"max\", \"renderSurplus\", \"slotProps\", \"spacing\", \"total\", \"variant\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { isFragment } from 'react-is';\nimport clsx from 'clsx';\nimport { chainPropTypes } from '@mui/utils';\nimport { unstable_composeClasses as composeClasses } from '@mui/base/composeClasses';\nimport styled from '../styles/styled';\nimport useThemeProps from '../styles/useThemeProps';\nimport Avatar, { avatarClasses } from '../Avatar';\nimport avatarGroupClasses, { getAvatarGroupUtilityClass } from './avatarGroupClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst SPACINGS = {\n  small: -16,\n  medium: null\n};\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root'],\n    avatar: ['avatar']\n  };\n  return composeClasses(slots, getAvatarGroupUtilityClass, classes);\n};\nconst AvatarGroupRoot = styled('div', {\n  name: 'MuiAvatarGroup',\n  slot: 'Root',\n  overridesResolver: (props, styles) => _extends({\n    [`& .${avatarGroupClasses.avatar}`]: styles.avatar\n  }, styles.root)\n})(({\n  theme\n}) => ({\n  [`& .${avatarClasses.root}`]: {\n    border: `2px solid ${(theme.vars || theme).palette.background.default}`,\n    boxSizing: 'content-box',\n    marginLeft: -8,\n    '&:last-child': {\n      marginLeft: 0\n    }\n  },\n  display: 'flex',\n  flexDirection: 'row-reverse'\n}));\nconst AvatarGroupAvatar = styled(Avatar, {\n  name: 'MuiAvatarGroup',\n  slot: 'Avatar',\n  overridesResolver: (props, styles) => styles.avatar\n})(({\n  theme\n}) => ({\n  border: `2px solid ${(theme.vars || theme).palette.background.default}`,\n  boxSizing: 'content-box',\n  marginLeft: -8,\n  '&:last-child': {\n    marginLeft: 0\n  }\n}));\nconst AvatarGroup = /*#__PURE__*/React.forwardRef(function AvatarGroup(inProps, ref) {\n  var _slotProps$additional;\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiAvatarGroup'\n  });\n  const {\n      children: childrenProp,\n      className,\n      component = 'div',\n      componentsProps = {},\n      max = 5,\n      renderSurplus,\n      slotProps = {},\n      spacing = 'medium',\n      total,\n      variant = 'circular'\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  let clampedMax = max < 2 ? 2 : max;\n  const ownerState = _extends({}, props, {\n    max,\n    spacing,\n    component,\n    variant\n  });\n  const classes = useUtilityClasses(ownerState);\n  const children = React.Children.toArray(childrenProp).filter(child => {\n    if (process.env.NODE_ENV !== 'production') {\n      if (isFragment(child)) {\n        console.error([\"MUI: The AvatarGroup component doesn't accept a Fragment as a child.\", 'Consider providing an array instead.'].join('\\n'));\n      }\n    }\n    return /*#__PURE__*/React.isValidElement(child);\n  });\n  const totalAvatars = total || children.length;\n  if (totalAvatars === clampedMax) {\n    clampedMax += 1;\n  }\n  clampedMax = Math.min(totalAvatars + 1, clampedMax);\n  const maxAvatars = Math.min(children.length, clampedMax - 1);\n  const extraAvatars = Math.max(totalAvatars - clampedMax, totalAvatars - maxAvatars, 0);\n  const extraAvatarsElement = renderSurplus ? renderSurplus(extraAvatars) : `+${extraAvatars}`;\n  const marginLeft = spacing && SPACINGS[spacing] !== undefined ? SPACINGS[spacing] : -spacing;\n  const additionalAvatarSlotProps = (_slotProps$additional = slotProps.additionalAvatar) != null ? _slotProps$additional : componentsProps.additionalAvatar;\n  return /*#__PURE__*/_jsxs(AvatarGroupRoot, _extends({\n    as: component,\n    ownerState: ownerState,\n    className: clsx(classes.root, className),\n    ref: ref\n  }, other, {\n    children: [extraAvatars ? /*#__PURE__*/_jsx(AvatarGroupAvatar, _extends({\n      ownerState: ownerState,\n      variant: variant\n    }, additionalAvatarSlotProps, {\n      className: clsx(classes.avatar, additionalAvatarSlotProps == null ? void 0 : additionalAvatarSlotProps.className),\n      style: _extends({\n        marginLeft\n      }, additionalAvatarSlotProps == null ? void 0 : additionalAvatarSlotProps.style),\n      children: extraAvatarsElement\n    })) : null, children.slice(0, maxAvatars).reverse().map((child, index) => {\n      return /*#__PURE__*/React.cloneElement(child, {\n        className: clsx(child.props.className, classes.avatar),\n        style: _extends({\n          // Consistent with \"&:last-child\" styling for the default spacing,\n          // we do not apply custom marginLeft spacing on the last child\n          marginLeft: index === maxAvatars - 1 ? undefined : marginLeft\n        }, child.props.style),\n        variant: child.props.variant || variant\n      });\n    })]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? AvatarGroup.propTypes /* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit the d.ts file and run \"yarn proptypes\"     |\n  // ----------------------------------------------------------------------\n  /**\n   * The avatars to stack.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * This prop is an alias for the `slotProps` prop.\n   * It's recommended to use the `slotProps` prop instead, as `componentsProps` will be deprecated in the future.\n   *\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    additionalAvatar: PropTypes.object\n  }),\n  /**\n   * Max avatars to show before +x.\n   * @default 5\n   */\n  max: chainPropTypes(PropTypes.number, props => {\n    if (props.max < 2) {\n      return new Error(['MUI: The prop `max` should be equal to 2 or above.', 'A value below is clamped to 2.'].join('\\n'));\n    }\n    return null;\n  }),\n  /**\n   * custom renderer of extraAvatars\n   * @param {number} surplus number of extra avatars\n   * @returns {React.ReactNode} custom element to display\n   */\n  renderSurplus: PropTypes.func,\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * This prop is an alias for the `componentsProps` prop, which will be deprecated in the future.\n   *\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    additionalAvatar: PropTypes.object\n  }),\n  /**\n   * Spacing between avatars.\n   * @default 'medium'\n   */\n  spacing: PropTypes.oneOfType([PropTypes.oneOf(['medium', 'small']), PropTypes.number]),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The total number of avatars. Used for calculating the number of extra avatars.\n   * @default children.length\n   */\n  total: PropTypes.number,\n  /**\n   * The variant to use.\n   * @default 'circular'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['circular', 'rounded', 'square']), PropTypes.string])\n} : void 0;\nexport default AvatarGroup;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,iBAAiB,EAAE,KAAK,EAAE,eAAe,EAAE,WAAW,EAAE,SAAS,EAAE,OAAO,EAAE,SAAS,CAAC;AAC/I,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,UAAU,QAAQ,UAAU;AACrC,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,cAAc,QAAQ,YAAY;AAC3C,SAASC,uBAAuB,IAAIC,cAAc,QAAQ,0BAA0B;AACpF,OAAOC,MAAM,MAAM,kBAAkB;AACrC,OAAOC,aAAa,MAAM,yBAAyB;AACnD,OAAOC,MAAM,IAAIC,aAAa,QAAQ,WAAW;AACjD,OAAOC,kBAAkB,IAAIC,0BAA0B,QAAQ,sBAAsB;AACrF,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,MAAMC,QAAQ,GAAG;EACfC,KAAK,EAAE,CAAC,EAAE;EACVC,MAAM,EAAE;AACV,CAAC;AACD,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC;EACF,CAAC,GAAGD,UAAU;EACd,MAAME,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,CAAC;IACdC,MAAM,EAAE,CAAC,QAAQ;EACnB,CAAC;EACD,OAAOnB,cAAc,CAACiB,KAAK,EAAEX,0BAA0B,EAAEU,OAAO,CAAC;AACnE,CAAC;AACD,MAAMI,eAAe,GAAGnB,MAAM,CAAC,KAAK,EAAE;EACpCoB,IAAI,EAAE,gBAAgB;EACtBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKjC,QAAQ,CAAC;IAC7C,CAAE,MAAKa,kBAAkB,CAACc,MAAO,EAAC,GAAGM,MAAM,CAACN;EAC9C,CAAC,EAAEM,MAAM,CAACP,IAAI;AAChB,CAAC,CAAC,CAAC,CAAC;EACFQ;AACF,CAAC,MAAM;EACL,CAAE,MAAKtB,aAAa,CAACc,IAAK,EAAC,GAAG;IAC5BS,MAAM,EAAG,aAAY,CAACD,KAAK,CAACE,IAAI,IAAIF,KAAK,EAAEG,OAAO,CAACC,UAAU,CAACC,OAAQ,EAAC;IACvEC,SAAS,EAAE,aAAa;IACxBC,UAAU,EAAE,CAAC,CAAC;IACd,cAAc,EAAE;MACdA,UAAU,EAAE;IACd;EACF,CAAC;EACDC,OAAO,EAAE,MAAM;EACfC,aAAa,EAAE;AACjB,CAAC,CAAC,CAAC;AACH,MAAMC,iBAAiB,GAAGnC,MAAM,CAACE,MAAM,EAAE;EACvCkB,IAAI,EAAE,gBAAgB;EACtBC,IAAI,EAAE,QAAQ;EACdC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACN;AAC/C,CAAC,CAAC,CAAC,CAAC;EACFO;AACF,CAAC,MAAM;EACLC,MAAM,EAAG,aAAY,CAACD,KAAK,CAACE,IAAI,IAAIF,KAAK,EAAEG,OAAO,CAACC,UAAU,CAACC,OAAQ,EAAC;EACvEC,SAAS,EAAE,aAAa;EACxBC,UAAU,EAAE,CAAC,CAAC;EACd,cAAc,EAAE;IACdA,UAAU,EAAE;EACd;AACF,CAAC,CAAC,CAAC;AACH,MAAMI,WAAW,GAAG,aAAa3C,KAAK,CAAC4C,UAAU,CAAC,SAASD,WAAWA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACnF,IAAIC,qBAAqB;EACzB,MAAMjB,KAAK,GAAGtB,aAAa,CAAC;IAC1BsB,KAAK,EAAEe,OAAO;IACdlB,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFqB,QAAQ,EAAEC,YAAY;MACtBC,SAAS;MACTC,SAAS,GAAG,KAAK;MACjBC,eAAe,GAAG,CAAC,CAAC;MACpBC,GAAG,GAAG,CAAC;MACPC,aAAa;MACbC,SAAS,GAAG,CAAC,CAAC;MACdC,OAAO,GAAG,QAAQ;MAClBC,KAAK;MACLC,OAAO,GAAG;IACZ,CAAC,GAAG5B,KAAK;IACT6B,KAAK,GAAG9D,6BAA6B,CAACiC,KAAK,EAAE/B,SAAS,CAAC;EACzD,IAAI6D,UAAU,GAAGP,GAAG,GAAG,CAAC,GAAG,CAAC,GAAGA,GAAG;EAClC,MAAMhC,UAAU,GAAGvB,QAAQ,CAAC,CAAC,CAAC,EAAEgC,KAAK,EAAE;IACrCuB,GAAG;IACHG,OAAO;IACPL,SAAS;IACTO;EACF,CAAC,CAAC;EACF,MAAMpC,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAM2B,QAAQ,GAAGhD,KAAK,CAAC6D,QAAQ,CAACC,OAAO,CAACb,YAAY,CAAC,CAACc,MAAM,CAACC,KAAK,IAAI;IACpE,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAIjE,UAAU,CAAC8D,KAAK,CAAC,EAAE;QACrBI,OAAO,CAACC,KAAK,CAAC,CAAC,sEAAsE,EAAE,sCAAsC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;MAC5I;IACF;IACA,OAAO,aAAatE,KAAK,CAACuE,cAAc,CAACP,KAAK,CAAC;EACjD,CAAC,CAAC;EACF,MAAMQ,YAAY,GAAGf,KAAK,IAAIT,QAAQ,CAACyB,MAAM;EAC7C,IAAID,YAAY,KAAKZ,UAAU,EAAE;IAC/BA,UAAU,IAAI,CAAC;EACjB;EACAA,UAAU,GAAGc,IAAI,CAACC,GAAG,CAACH,YAAY,GAAG,CAAC,EAAEZ,UAAU,CAAC;EACnD,MAAMgB,UAAU,GAAGF,IAAI,CAACC,GAAG,CAAC3B,QAAQ,CAACyB,MAAM,EAAEb,UAAU,GAAG,CAAC,CAAC;EAC5D,MAAMiB,YAAY,GAAGH,IAAI,CAACrB,GAAG,CAACmB,YAAY,GAAGZ,UAAU,EAAEY,YAAY,GAAGI,UAAU,EAAE,CAAC,CAAC;EACtF,MAAME,mBAAmB,GAAGxB,aAAa,GAAGA,aAAa,CAACuB,YAAY,CAAC,GAAI,IAAGA,YAAa,EAAC;EAC5F,MAAMtC,UAAU,GAAGiB,OAAO,IAAIvC,QAAQ,CAACuC,OAAO,CAAC,KAAKuB,SAAS,GAAG9D,QAAQ,CAACuC,OAAO,CAAC,GAAG,CAACA,OAAO;EAC5F,MAAMwB,yBAAyB,GAAG,CAACjC,qBAAqB,GAAGQ,SAAS,CAAC0B,gBAAgB,KAAK,IAAI,GAAGlC,qBAAqB,GAAGK,eAAe,CAAC6B,gBAAgB;EACzJ,OAAO,aAAajE,KAAK,CAACU,eAAe,EAAE5B,QAAQ,CAAC;IAClDoF,EAAE,EAAE/B,SAAS;IACb9B,UAAU,EAAEA,UAAU;IACtB6B,SAAS,EAAE/C,IAAI,CAACmB,OAAO,CAACE,IAAI,EAAE0B,SAAS,CAAC;IACxCJ,GAAG,EAAEA;EACP,CAAC,EAAEa,KAAK,EAAE;IACRX,QAAQ,EAAE,CAAC6B,YAAY,GAAG,aAAa/D,IAAI,CAAC4B,iBAAiB,EAAE5C,QAAQ,CAAC;MACtEuB,UAAU,EAAEA,UAAU;MACtBqC,OAAO,EAAEA;IACX,CAAC,EAAEsB,yBAAyB,EAAE;MAC5B9B,SAAS,EAAE/C,IAAI,CAACmB,OAAO,CAACG,MAAM,EAAEuD,yBAAyB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,yBAAyB,CAAC9B,SAAS,CAAC;MACjHiC,KAAK,EAAErF,QAAQ,CAAC;QACdyC;MACF,CAAC,EAAEyC,yBAAyB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,yBAAyB,CAACG,KAAK,CAAC;MAChFnC,QAAQ,EAAE8B;IACZ,CAAC,CAAC,CAAC,GAAG,IAAI,EAAE9B,QAAQ,CAACoC,KAAK,CAAC,CAAC,EAAER,UAAU,CAAC,CAACS,OAAO,CAAC,CAAC,CAACC,GAAG,CAAC,CAACtB,KAAK,EAAEuB,KAAK,KAAK;MACxE,OAAO,aAAavF,KAAK,CAACwF,YAAY,CAACxB,KAAK,EAAE;QAC5Cd,SAAS,EAAE/C,IAAI,CAAC6D,KAAK,CAAClC,KAAK,CAACoB,SAAS,EAAE5B,OAAO,CAACG,MAAM,CAAC;QACtD0D,KAAK,EAAErF,QAAQ,CAAC;UACd;UACA;UACAyC,UAAU,EAAEgD,KAAK,KAAKX,UAAU,GAAG,CAAC,GAAGG,SAAS,GAAGxC;QACrD,CAAC,EAAEyB,KAAK,CAAClC,KAAK,CAACqD,KAAK,CAAC;QACrBzB,OAAO,EAAEM,KAAK,CAAClC,KAAK,CAAC4B,OAAO,IAAIA;MAClC,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGxB,WAAW,CAAC8C,SAAS,CAAC,yBAAyB;EACrF;EACA;EACA;EACA;EACA;AACF;AACA;EACEzC,QAAQ,EAAE/C,SAAS,CAACyF,IAAI;EACxB;AACF;AACA;EACEpE,OAAO,EAAErB,SAAS,CAAC0F,MAAM;EACzB;AACF;AACA;EACEzC,SAAS,EAAEjD,SAAS,CAAC2F,MAAM;EAC3B;AACF;AACA;AACA;EACEzC,SAAS,EAAElD,SAAS,CAAC4F,WAAW;EAChC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEzC,eAAe,EAAEnD,SAAS,CAAC6F,KAAK,CAAC;IAC/Bb,gBAAgB,EAAEhF,SAAS,CAAC0F;EAC9B,CAAC,CAAC;EACF;AACF;AACA;AACA;EACEtC,GAAG,EAAEjD,cAAc,CAACH,SAAS,CAAC8F,MAAM,EAAEjE,KAAK,IAAI;IAC7C,IAAIA,KAAK,CAACuB,GAAG,GAAG,CAAC,EAAE;MACjB,OAAO,IAAI2C,KAAK,CAAC,CAAC,oDAAoD,EAAE,gCAAgC,CAAC,CAAC1B,IAAI,CAAC,IAAI,CAAC,CAAC;IACvH;IACA,OAAO,IAAI;EACb,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;EACEhB,aAAa,EAAErD,SAAS,CAACgG,IAAI;EAC7B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE1C,SAAS,EAAEtD,SAAS,CAAC6F,KAAK,CAAC;IACzBb,gBAAgB,EAAEhF,SAAS,CAAC0F;EAC9B,CAAC,CAAC;EACF;AACF;AACA;AACA;EACEnC,OAAO,EAAEvD,SAAS,CAACiG,SAAS,CAAC,CAACjG,SAAS,CAACkG,KAAK,CAAC,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,EAAElG,SAAS,CAAC8F,MAAM,CAAC,CAAC;EACtF;AACF;AACA;EACEK,EAAE,EAAEnG,SAAS,CAACiG,SAAS,CAAC,CAACjG,SAAS,CAACoG,OAAO,CAACpG,SAAS,CAACiG,SAAS,CAAC,CAACjG,SAAS,CAACgG,IAAI,EAAEhG,SAAS,CAAC0F,MAAM,EAAE1F,SAAS,CAACqG,IAAI,CAAC,CAAC,CAAC,EAAErG,SAAS,CAACgG,IAAI,EAAEhG,SAAS,CAAC0F,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;AACA;EACElC,KAAK,EAAExD,SAAS,CAAC8F,MAAM;EACvB;AACF;AACA;AACA;EACErC,OAAO,EAAEzD,SAAS,CAAC,sCAAsCiG,SAAS,CAAC,CAACjG,SAAS,CAACkG,KAAK,CAAC,CAAC,UAAU,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC,EAAElG,SAAS,CAAC2F,MAAM,CAAC;AAC3I,CAAC,GAAG,KAAK,CAAC;AACV,eAAejD,WAAW"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}