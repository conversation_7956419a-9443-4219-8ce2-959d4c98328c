{"ast": null, "code": "export * from './ClickAwayListener';", "map": {"version": 3, "names": [], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/base/ClickAwayListener/index.js"], "sourcesContent": ["export * from './ClickAwayListener';"], "mappings": "AAAA,cAAc,qBAAqB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}