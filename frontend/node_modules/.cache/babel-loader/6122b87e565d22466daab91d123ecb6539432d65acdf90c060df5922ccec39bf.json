{"ast": null, "code": "import { generateUtilityClass } from '../generateUtilityClass';\nimport { generateUtilityClasses } from '../generateUtilityClasses';\nexport function getTabsListUtilityClass(slot) {\n  return generateUtilityClass('MuiTabsList', slot);\n}\nexport const tabsListClasses = generateUtilityClasses('MuiTabsList', ['root', 'horizontal', 'vertical']);", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getTabsListUtilityClass", "slot", "tabsListClasses"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/base/TabsList/tabsListClasses.js"], "sourcesContent": ["import { generateUtilityClass } from '../generateUtilityClass';\nimport { generateUtilityClasses } from '../generateUtilityClasses';\nexport function getTabsListUtilityClass(slot) {\n  return generateUtilityClass('MuiTabsList', slot);\n}\nexport const tabsListClasses = generateUtilityClasses('MuiTabsList', ['root', 'horizontal', 'vertical']);"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,yBAAyB;AAC9D,SAASC,sBAAsB,QAAQ,2BAA2B;AAClE,OAAO,SAASC,uBAAuBA,CAACC,IAAI,EAAE;EAC5C,OAAOH,oBAAoB,CAAC,aAAa,EAAEG,IAAI,CAAC;AAClD;AACA,OAAO,MAAMC,eAAe,GAAGH,sBAAsB,CAAC,aAAa,EAAE,CAAC,MAAM,EAAE,YAAY,EAAE,UAAU,CAAC,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}