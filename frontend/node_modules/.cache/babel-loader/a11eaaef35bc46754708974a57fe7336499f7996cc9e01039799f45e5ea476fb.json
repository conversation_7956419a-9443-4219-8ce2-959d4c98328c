{"ast": null, "code": "import { isHostComponent } from '@mui/base';\nconst shouldSpreadAdditionalProps = Slot => {\n  return !Slot || !isHostComponent(Slot);\n};\nexport default shouldSpreadAdditionalProps;", "map": {"version": 3, "names": ["isHostComponent", "shouldSpreadAdditionalProps", "Slot"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/material/utils/shouldSpreadAdditionalProps.js"], "sourcesContent": ["import { isHostComponent } from '@mui/base';\nconst shouldSpreadAdditionalProps = Slot => {\n  return !Slot || !isHostComponent(Slot);\n};\nexport default shouldSpreadAdditionalProps;"], "mappings": "AAAA,SAASA,eAAe,QAAQ,WAAW;AAC3C,MAAMC,2BAA2B,GAAGC,IAAI,IAAI;EAC1C,OAAO,CAACA,IAAI,IAAI,CAACF,eAAe,CAACE,IAAI,CAAC;AACxC,CAAC;AACD,eAAeD,2BAA2B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}