{"ast": null, "code": "import { internal_resolveProps as resolveProps } from '@mui/utils';\nexport default function getThemeProps(params) {\n  const {\n    theme,\n    name,\n    props\n  } = params;\n  if (!theme || !theme.components || !theme.components[name] || !theme.components[name].defaultProps) {\n    return props;\n  }\n  return resolveProps(theme.components[name].defaultProps, props);\n}", "map": {"version": 3, "names": ["internal_resolveProps", "resolveProps", "getThemeProps", "params", "theme", "name", "props", "components", "defaultProps"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/system/esm/useThemeProps/getThemeProps.js"], "sourcesContent": ["import { internal_resolveProps as resolveProps } from '@mui/utils';\nexport default function getThemeProps(params) {\n  const {\n    theme,\n    name,\n    props\n  } = params;\n  if (!theme || !theme.components || !theme.components[name] || !theme.components[name].defaultProps) {\n    return props;\n  }\n  return resolveProps(theme.components[name].defaultProps, props);\n}"], "mappings": "AAAA,SAASA,qBAAqB,IAAIC,YAAY,QAAQ,YAAY;AAClE,eAAe,SAASC,aAAaA,CAACC,MAAM,EAAE;EAC5C,MAAM;IACJC,KAAK;IACLC,IAAI;IACJC;EACF,CAAC,GAAGH,MAAM;EACV,IAAI,CAACC,KAAK,IAAI,CAACA,KAAK,CAACG,UAAU,IAAI,CAACH,KAAK,CAACG,UAAU,CAACF,IAAI,CAAC,IAAI,CAACD,KAAK,CAACG,UAAU,CAACF,IAAI,CAAC,CAACG,YAAY,EAAE;IAClG,OAAOF,KAAK;EACd;EACA,OAAOL,YAAY,CAACG,KAAK,CAACG,UAAU,CAACF,IAAI,CAAC,CAACG,YAAY,EAAEF,KAAK,CAAC;AACjE"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}