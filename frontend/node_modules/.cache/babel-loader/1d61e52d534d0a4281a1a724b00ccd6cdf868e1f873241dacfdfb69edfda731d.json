{"ast": null, "code": "import { unstable_ownerDocument as ownerDocument } from '@mui/utils';\nexport default ownerDocument;", "map": {"version": 3, "names": ["unstable_ownerDocument", "ownerDocument"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/material/utils/ownerDocument.js"], "sourcesContent": ["import { unstable_ownerDocument as ownerDocument } from '@mui/utils';\nexport default ownerDocument;"], "mappings": "AAAA,SAASA,sBAAsB,IAAIC,aAAa,QAAQ,YAAY;AACpE,eAAeA,aAAa"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}