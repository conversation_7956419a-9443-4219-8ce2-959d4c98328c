{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { ListActionTypes } from './listActions.types';\n/**\n * Looks up the next valid item to highlight within the list.\n *\n * @param currentIndex The index of the start of the search.\n * @param lookupDirection Whether to look for the next or previous item.\n * @param items The array of items to search.\n * @param includeDisabledItems Whether to include disabled items in the search.\n * @param isItemDisabled A function that determines whether an item is disabled.\n * @param wrapAround Whether to wrap around the list when searching.\n * @returns The index of the next valid item to highlight or -1 if no valid item is found.\n */\nfunction findValidItemToHighlight(currentIndex, lookupDirection, items, includeDisabledItems, isItemDisabled, wrapAround) {\n  if (items.length === 0 || !includeDisabledItems && items.every((item, itemIndex) => isItemDisabled(item, itemIndex))) {\n    return -1;\n  }\n  let nextFocus = currentIndex;\n  for (;;) {\n    // No valid items found\n    if (!wrapAround && lookupDirection === 'next' && nextFocus === items.length || !wrapAround && lookupDirection === 'previous' && nextFocus === -1) {\n      return -1;\n    }\n    const nextFocusDisabled = includeDisabledItems ? false : isItemDisabled(items[nextFocus], nextFocus);\n    if (nextFocusDisabled) {\n      nextFocus += lookupDirection === 'next' ? 1 : -1;\n      if (wrapAround) {\n        nextFocus = (nextFocus + items.length) % items.length;\n      }\n    } else {\n      return nextFocus;\n    }\n  }\n}\n\n/**\n * Gets the next item to highlight based on the current highlighted item and the search direction.\n *\n * @param previouslyHighlightedValue The item from which to start the search for the next candidate.\n * @param offset The offset from the previously highlighted item to search for the next candidate or a special named value ('reset', 'start', 'end').\n * @param context The list action context.\n *\n * @returns The next item to highlight or null if no item is valid.\n */\nexport function moveHighlight(previouslyHighlightedValue, offset, context) {\n  var _items$nextIndex;\n  const {\n    items,\n    isItemDisabled,\n    disableListWrap,\n    disabledItemsFocusable,\n    itemComparer,\n    focusManagement\n  } = context;\n\n  // TODO: make this configurable\n  // The always should be an item highlighted when focus is managed by the DOM\n  // so that it's accessible by the `tab` key.\n  const defaultHighlightedIndex = focusManagement === 'DOM' ? 0 : -1;\n  const maxIndex = items.length - 1;\n  const previouslyHighlightedIndex = previouslyHighlightedValue == null ? -1 : items.findIndex(item => itemComparer(item, previouslyHighlightedValue));\n  let nextIndexCandidate;\n  let lookupDirection;\n  let wrapAround = !disableListWrap;\n  switch (offset) {\n    case 'reset':\n      if (defaultHighlightedIndex === -1) {\n        return null;\n      }\n      nextIndexCandidate = 0;\n      lookupDirection = 'next';\n      wrapAround = false;\n      break;\n    case 'start':\n      nextIndexCandidate = 0;\n      lookupDirection = 'next';\n      wrapAround = false;\n      break;\n    case 'end':\n      nextIndexCandidate = maxIndex;\n      lookupDirection = 'previous';\n      wrapAround = false;\n      break;\n    default:\n      {\n        const newIndex = previouslyHighlightedIndex + offset;\n        if (newIndex < 0) {\n          if (!wrapAround && previouslyHighlightedIndex !== -1 || Math.abs(offset) > 1) {\n            nextIndexCandidate = 0;\n            lookupDirection = 'next';\n          } else {\n            nextIndexCandidate = maxIndex;\n            lookupDirection = 'previous';\n          }\n        } else if (newIndex > maxIndex) {\n          if (!wrapAround || Math.abs(offset) > 1) {\n            nextIndexCandidate = maxIndex;\n            lookupDirection = 'previous';\n          } else {\n            nextIndexCandidate = 0;\n            lookupDirection = 'next';\n          }\n        } else {\n          nextIndexCandidate = newIndex;\n          lookupDirection = offset >= 0 ? 'next' : 'previous';\n        }\n      }\n  }\n  const nextIndex = findValidItemToHighlight(nextIndexCandidate, lookupDirection, items, disabledItemsFocusable, isItemDisabled, wrapAround);\n\n  // If there are no valid items to highlight, return the previously highlighted item (if it's still valid).\n  if (nextIndex === -1 && previouslyHighlightedValue !== null && !isItemDisabled(previouslyHighlightedValue, previouslyHighlightedIndex)) {\n    return previouslyHighlightedValue;\n  }\n  return (_items$nextIndex = items[nextIndex]) != null ? _items$nextIndex : null;\n}\n\n/**\n * Toggles the selection of an item.\n *\n * @param item Item to toggle.\n * @param selectedValues Already selected items.\n * @param selectionMode The number of items that can be simultanously selected.\n * @param itemComparer A custom item comparer function.\n *\n * @returns The new array of selected items.\n */\nexport function toggleSelection(item, selectedValues, selectionMode, itemComparer) {\n  if (selectionMode === 'none') {\n    return [];\n  }\n  if (selectionMode === 'single') {\n    // if the item to select has already been selected, return the original array\n    if (itemComparer(selectedValues[0], item)) {\n      return selectedValues;\n    }\n    return [item];\n  }\n\n  // The toggled item is selected; remove it from the selection.\n  if (selectedValues.some(sv => itemComparer(sv, item))) {\n    return selectedValues.filter(sv => !itemComparer(sv, item));\n  }\n\n  // The toggled item is not selected - add it to the selection.\n  return [...selectedValues, item];\n}\n\n/**\n * Handles item selection in a list.\n *\n * @param item - The item to be selected.\n * @param state - The current state of the list.\n * @param context - The context of the list action.\n * @returns The new state of the list after the item has been selected, or the original state if the item is disabled.\n */\nexport function handleItemSelection(item, state, context) {\n  const {\n    itemComparer,\n    isItemDisabled,\n    selectionMode,\n    items\n  } = context;\n  const {\n    selectedValues\n  } = state;\n  const itemIndex = items.findIndex(i => itemComparer(item, i));\n  if (isItemDisabled(item, itemIndex)) {\n    return state;\n  }\n\n  // if the item is already selected, remove it from the selection, otherwise add it\n  const newSelectedValues = toggleSelection(item, selectedValues, selectionMode, itemComparer);\n  return _extends({}, state, {\n    selectedValues: newSelectedValues,\n    highlightedValue: item\n  });\n}\nfunction handleKeyDown(key, state, context) {\n  const previouslySelectedValue = state.highlightedValue;\n  const {\n    orientation,\n    pageSize\n  } = context;\n  switch (key) {\n    case 'Home':\n      return _extends({}, state, {\n        highlightedValue: moveHighlight(previouslySelectedValue, 'start', context)\n      });\n    case 'End':\n      return _extends({}, state, {\n        highlightedValue: moveHighlight(previouslySelectedValue, 'end', context)\n      });\n    case 'PageUp':\n      return _extends({}, state, {\n        highlightedValue: moveHighlight(previouslySelectedValue, -pageSize, context)\n      });\n    case 'PageDown':\n      return _extends({}, state, {\n        highlightedValue: moveHighlight(previouslySelectedValue, pageSize, context)\n      });\n    case 'ArrowUp':\n      if (orientation !== 'vertical') {\n        break;\n      }\n      return _extends({}, state, {\n        highlightedValue: moveHighlight(previouslySelectedValue, -1, context)\n      });\n    case 'ArrowDown':\n      if (orientation !== 'vertical') {\n        break;\n      }\n      return _extends({}, state, {\n        highlightedValue: moveHighlight(previouslySelectedValue, 1, context)\n      });\n    case 'ArrowLeft':\n      {\n        if (orientation === 'vertical') {\n          break;\n        }\n        const offset = orientation === 'horizontal-ltr' ? -1 : 1;\n        return _extends({}, state, {\n          highlightedValue: moveHighlight(previouslySelectedValue, offset, context)\n        });\n      }\n    case 'ArrowRight':\n      {\n        if (orientation === 'vertical') {\n          break;\n        }\n        const offset = orientation === 'horizontal-ltr' ? 1 : -1;\n        return _extends({}, state, {\n          highlightedValue: moveHighlight(previouslySelectedValue, offset, context)\n        });\n      }\n    case 'Enter':\n    case ' ':\n      if (state.highlightedValue === null) {\n        return state;\n      }\n      return handleItemSelection(state.highlightedValue, state, context);\n    default:\n      break;\n  }\n  return state;\n}\nfunction handleBlur(state, context) {\n  if (context.focusManagement === 'DOM') {\n    return state;\n  }\n  return _extends({}, state, {\n    highlightedValue: null\n  });\n}\nfunction textCriteriaMatches(nextFocus, searchString, stringifyItem) {\n  var _stringifyItem;\n  const text = (_stringifyItem = stringifyItem(nextFocus)) == null ? void 0 : _stringifyItem.trim().toLowerCase();\n  if (!text || text.length === 0) {\n    // Make item not navigable if stringification fails or results in empty string.\n    return false;\n  }\n  return text.indexOf(searchString) === 0;\n}\nfunction handleTextNavigation(state, searchString, context) {\n  const {\n    items,\n    isItemDisabled,\n    disabledItemsFocusable,\n    getItemAsString\n  } = context;\n  const startWithCurrentItem = searchString.length > 1;\n  let nextItem = startWithCurrentItem ? state.highlightedValue : moveHighlight(state.highlightedValue, 1, context);\n  for (let index = 0; index < items.length; index += 1) {\n    // Return un-mutated state if looped back to the currently highlighted value\n    if (!nextItem || !startWithCurrentItem && state.highlightedValue === nextItem) {\n      return state;\n    }\n    if (textCriteriaMatches(nextItem, searchString, getItemAsString) && (!isItemDisabled(nextItem, items.indexOf(nextItem)) || disabledItemsFocusable)) {\n      // The nextItem is the element to be highlighted\n      return _extends({}, state, {\n        highlightedValue: nextItem\n      });\n    }\n    // Move to the next element.\n    nextItem = moveHighlight(nextItem, 1, context);\n  }\n\n  // No item matches the text search criteria\n  return state;\n}\nfunction handleItemsChange(items, previousItems, state, context) {\n  var _state$selectedValues;\n  const {\n    itemComparer,\n    focusManagement\n  } = context;\n  let newHighlightedValue = null;\n  if (state.highlightedValue != null) {\n    var _items$find;\n    newHighlightedValue = (_items$find = items.find(item => itemComparer(item, state.highlightedValue))) != null ? _items$find : null;\n  } else if (focusManagement === 'DOM' && previousItems.length === 0) {\n    newHighlightedValue = moveHighlight(null, 'reset', context);\n  }\n\n  // exclude selected values that are no longer in the items list\n  const selectedValues = (_state$selectedValues = state.selectedValues) != null ? _state$selectedValues : [];\n  const newSelectedValues = selectedValues.filter(selectedValue => items.some(item => itemComparer(item, selectedValue)));\n  return _extends({}, state, {\n    highlightedValue: newHighlightedValue,\n    selectedValues: newSelectedValues\n  });\n}\nfunction handleResetHighlight(state, context) {\n  return _extends({}, state, {\n    highlightedValue: moveHighlight(null, 'reset', context)\n  });\n}\nfunction handleClearSelection(state, context) {\n  return _extends({}, state, {\n    selectedValues: [],\n    highlightedValue: moveHighlight(null, 'reset', context)\n  });\n}\nexport function listReducer(state, action) {\n  const {\n    type,\n    context\n  } = action;\n  switch (type) {\n    case ListActionTypes.keyDown:\n      return handleKeyDown(action.key, state, context);\n    case ListActionTypes.itemClick:\n      return handleItemSelection(action.item, state, context);\n    case ListActionTypes.blur:\n      return handleBlur(state, context);\n    case ListActionTypes.textNavigation:\n      return handleTextNavigation(state, action.searchString, context);\n    case ListActionTypes.itemsChange:\n      return handleItemsChange(action.items, action.previousItems, state, context);\n    case ListActionTypes.resetHighlight:\n      return handleResetHighlight(state, context);\n    case ListActionTypes.clearSelection:\n      return handleClearSelection(state, context);\n    default:\n      return state;\n  }\n}", "map": {"version": 3, "names": ["_extends", "ListActionTypes", "findValidItemToHighlight", "currentIndex", "lookupDirection", "items", "includeDisabledItems", "isItemDisabled", "wrapAround", "length", "every", "item", "itemIndex", "nextFocus", "nextFocusDisabled", "moveHighlight", "previouslyHighlightedValue", "offset", "context", "_items$nextIndex", "disableListWrap", "disabledItemsFocusable", "itemComparer", "focusManagement", "defaultHighlightedIndex", "maxIndex", "previouslyHighlightedIndex", "findIndex", "nextIndexCandidate", "newIndex", "Math", "abs", "nextIndex", "toggleSelection", "<PERSON><PERSON><PERSON><PERSON>", "selectionMode", "some", "sv", "filter", "handleItemSelection", "state", "i", "newSelectedValues", "highlightedValue", "handleKeyDown", "key", "previouslySelectedValue", "orientation", "pageSize", "handleBlur", "textCriteriaMatches", "searchString", "stringifyItem", "_stringifyItem", "text", "trim", "toLowerCase", "indexOf", "handleTextNavigation", "getItemAsString", "startWithCurrentItem", "nextItem", "index", "handleItemsChange", "previousItems", "_state$selectedValues", "newHighlightedValue", "_items$find", "find", "selected<PERSON><PERSON><PERSON>", "handleResetHighlight", "handleClearSelection", "listReducer", "action", "type", "keyDown", "itemClick", "blur", "textNavigation", "itemsChange", "resetHighlight", "clearSelection"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/base/useList/listReducer.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { ListActionTypes } from './listActions.types';\n/**\n * Looks up the next valid item to highlight within the list.\n *\n * @param currentIndex The index of the start of the search.\n * @param lookupDirection Whether to look for the next or previous item.\n * @param items The array of items to search.\n * @param includeDisabledItems Whether to include disabled items in the search.\n * @param isItemDisabled A function that determines whether an item is disabled.\n * @param wrapAround Whether to wrap around the list when searching.\n * @returns The index of the next valid item to highlight or -1 if no valid item is found.\n */\nfunction findValidItemToHighlight(currentIndex, lookupDirection, items, includeDisabledItems, isItemDisabled, wrapAround) {\n  if (items.length === 0 || !includeDisabledItems && items.every((item, itemIndex) => isItemDisabled(item, itemIndex))) {\n    return -1;\n  }\n  let nextFocus = currentIndex;\n  for (;;) {\n    // No valid items found\n    if (!wrapAround && lookupDirection === 'next' && nextFocus === items.length || !wrapAround && lookupDirection === 'previous' && nextFocus === -1) {\n      return -1;\n    }\n    const nextFocusDisabled = includeDisabledItems ? false : isItemDisabled(items[nextFocus], nextFocus);\n    if (nextFocusDisabled) {\n      nextFocus += lookupDirection === 'next' ? 1 : -1;\n      if (wrapAround) {\n        nextFocus = (nextFocus + items.length) % items.length;\n      }\n    } else {\n      return nextFocus;\n    }\n  }\n}\n\n/**\n * Gets the next item to highlight based on the current highlighted item and the search direction.\n *\n * @param previouslyHighlightedValue The item from which to start the search for the next candidate.\n * @param offset The offset from the previously highlighted item to search for the next candidate or a special named value ('reset', 'start', 'end').\n * @param context The list action context.\n *\n * @returns The next item to highlight or null if no item is valid.\n */\nexport function moveHighlight(previouslyHighlightedValue, offset, context) {\n  var _items$nextIndex;\n  const {\n    items,\n    isItemDisabled,\n    disableListWrap,\n    disabledItemsFocusable,\n    itemComparer,\n    focusManagement\n  } = context;\n\n  // TODO: make this configurable\n  // The always should be an item highlighted when focus is managed by the DOM\n  // so that it's accessible by the `tab` key.\n  const defaultHighlightedIndex = focusManagement === 'DOM' ? 0 : -1;\n  const maxIndex = items.length - 1;\n  const previouslyHighlightedIndex = previouslyHighlightedValue == null ? -1 : items.findIndex(item => itemComparer(item, previouslyHighlightedValue));\n  let nextIndexCandidate;\n  let lookupDirection;\n  let wrapAround = !disableListWrap;\n  switch (offset) {\n    case 'reset':\n      if (defaultHighlightedIndex === -1) {\n        return null;\n      }\n      nextIndexCandidate = 0;\n      lookupDirection = 'next';\n      wrapAround = false;\n      break;\n    case 'start':\n      nextIndexCandidate = 0;\n      lookupDirection = 'next';\n      wrapAround = false;\n      break;\n    case 'end':\n      nextIndexCandidate = maxIndex;\n      lookupDirection = 'previous';\n      wrapAround = false;\n      break;\n    default:\n      {\n        const newIndex = previouslyHighlightedIndex + offset;\n        if (newIndex < 0) {\n          if (!wrapAround && previouslyHighlightedIndex !== -1 || Math.abs(offset) > 1) {\n            nextIndexCandidate = 0;\n            lookupDirection = 'next';\n          } else {\n            nextIndexCandidate = maxIndex;\n            lookupDirection = 'previous';\n          }\n        } else if (newIndex > maxIndex) {\n          if (!wrapAround || Math.abs(offset) > 1) {\n            nextIndexCandidate = maxIndex;\n            lookupDirection = 'previous';\n          } else {\n            nextIndexCandidate = 0;\n            lookupDirection = 'next';\n          }\n        } else {\n          nextIndexCandidate = newIndex;\n          lookupDirection = offset >= 0 ? 'next' : 'previous';\n        }\n      }\n  }\n  const nextIndex = findValidItemToHighlight(nextIndexCandidate, lookupDirection, items, disabledItemsFocusable, isItemDisabled, wrapAround);\n\n  // If there are no valid items to highlight, return the previously highlighted item (if it's still valid).\n  if (nextIndex === -1 && previouslyHighlightedValue !== null && !isItemDisabled(previouslyHighlightedValue, previouslyHighlightedIndex)) {\n    return previouslyHighlightedValue;\n  }\n  return (_items$nextIndex = items[nextIndex]) != null ? _items$nextIndex : null;\n}\n\n/**\n * Toggles the selection of an item.\n *\n * @param item Item to toggle.\n * @param selectedValues Already selected items.\n * @param selectionMode The number of items that can be simultanously selected.\n * @param itemComparer A custom item comparer function.\n *\n * @returns The new array of selected items.\n */\nexport function toggleSelection(item, selectedValues, selectionMode, itemComparer) {\n  if (selectionMode === 'none') {\n    return [];\n  }\n  if (selectionMode === 'single') {\n    // if the item to select has already been selected, return the original array\n    if (itemComparer(selectedValues[0], item)) {\n      return selectedValues;\n    }\n    return [item];\n  }\n\n  // The toggled item is selected; remove it from the selection.\n  if (selectedValues.some(sv => itemComparer(sv, item))) {\n    return selectedValues.filter(sv => !itemComparer(sv, item));\n  }\n\n  // The toggled item is not selected - add it to the selection.\n  return [...selectedValues, item];\n}\n\n/**\n * Handles item selection in a list.\n *\n * @param item - The item to be selected.\n * @param state - The current state of the list.\n * @param context - The context of the list action.\n * @returns The new state of the list after the item has been selected, or the original state if the item is disabled.\n */\nexport function handleItemSelection(item, state, context) {\n  const {\n    itemComparer,\n    isItemDisabled,\n    selectionMode,\n    items\n  } = context;\n  const {\n    selectedValues\n  } = state;\n  const itemIndex = items.findIndex(i => itemComparer(item, i));\n  if (isItemDisabled(item, itemIndex)) {\n    return state;\n  }\n\n  // if the item is already selected, remove it from the selection, otherwise add it\n  const newSelectedValues = toggleSelection(item, selectedValues, selectionMode, itemComparer);\n  return _extends({}, state, {\n    selectedValues: newSelectedValues,\n    highlightedValue: item\n  });\n}\nfunction handleKeyDown(key, state, context) {\n  const previouslySelectedValue = state.highlightedValue;\n  const {\n    orientation,\n    pageSize\n  } = context;\n  switch (key) {\n    case 'Home':\n      return _extends({}, state, {\n        highlightedValue: moveHighlight(previouslySelectedValue, 'start', context)\n      });\n    case 'End':\n      return _extends({}, state, {\n        highlightedValue: moveHighlight(previouslySelectedValue, 'end', context)\n      });\n    case 'PageUp':\n      return _extends({}, state, {\n        highlightedValue: moveHighlight(previouslySelectedValue, -pageSize, context)\n      });\n    case 'PageDown':\n      return _extends({}, state, {\n        highlightedValue: moveHighlight(previouslySelectedValue, pageSize, context)\n      });\n    case 'ArrowUp':\n      if (orientation !== 'vertical') {\n        break;\n      }\n      return _extends({}, state, {\n        highlightedValue: moveHighlight(previouslySelectedValue, -1, context)\n      });\n    case 'ArrowDown':\n      if (orientation !== 'vertical') {\n        break;\n      }\n      return _extends({}, state, {\n        highlightedValue: moveHighlight(previouslySelectedValue, 1, context)\n      });\n    case 'ArrowLeft':\n      {\n        if (orientation === 'vertical') {\n          break;\n        }\n        const offset = orientation === 'horizontal-ltr' ? -1 : 1;\n        return _extends({}, state, {\n          highlightedValue: moveHighlight(previouslySelectedValue, offset, context)\n        });\n      }\n    case 'ArrowRight':\n      {\n        if (orientation === 'vertical') {\n          break;\n        }\n        const offset = orientation === 'horizontal-ltr' ? 1 : -1;\n        return _extends({}, state, {\n          highlightedValue: moveHighlight(previouslySelectedValue, offset, context)\n        });\n      }\n    case 'Enter':\n    case ' ':\n      if (state.highlightedValue === null) {\n        return state;\n      }\n      return handleItemSelection(state.highlightedValue, state, context);\n    default:\n      break;\n  }\n  return state;\n}\nfunction handleBlur(state, context) {\n  if (context.focusManagement === 'DOM') {\n    return state;\n  }\n  return _extends({}, state, {\n    highlightedValue: null\n  });\n}\nfunction textCriteriaMatches(nextFocus, searchString, stringifyItem) {\n  var _stringifyItem;\n  const text = (_stringifyItem = stringifyItem(nextFocus)) == null ? void 0 : _stringifyItem.trim().toLowerCase();\n  if (!text || text.length === 0) {\n    // Make item not navigable if stringification fails or results in empty string.\n    return false;\n  }\n  return text.indexOf(searchString) === 0;\n}\nfunction handleTextNavigation(state, searchString, context) {\n  const {\n    items,\n    isItemDisabled,\n    disabledItemsFocusable,\n    getItemAsString\n  } = context;\n  const startWithCurrentItem = searchString.length > 1;\n  let nextItem = startWithCurrentItem ? state.highlightedValue : moveHighlight(state.highlightedValue, 1, context);\n  for (let index = 0; index < items.length; index += 1) {\n    // Return un-mutated state if looped back to the currently highlighted value\n    if (!nextItem || !startWithCurrentItem && state.highlightedValue === nextItem) {\n      return state;\n    }\n    if (textCriteriaMatches(nextItem, searchString, getItemAsString) && (!isItemDisabled(nextItem, items.indexOf(nextItem)) || disabledItemsFocusable)) {\n      // The nextItem is the element to be highlighted\n      return _extends({}, state, {\n        highlightedValue: nextItem\n      });\n    }\n    // Move to the next element.\n    nextItem = moveHighlight(nextItem, 1, context);\n  }\n\n  // No item matches the text search criteria\n  return state;\n}\nfunction handleItemsChange(items, previousItems, state, context) {\n  var _state$selectedValues;\n  const {\n    itemComparer,\n    focusManagement\n  } = context;\n  let newHighlightedValue = null;\n  if (state.highlightedValue != null) {\n    var _items$find;\n    newHighlightedValue = (_items$find = items.find(item => itemComparer(item, state.highlightedValue))) != null ? _items$find : null;\n  } else if (focusManagement === 'DOM' && previousItems.length === 0) {\n    newHighlightedValue = moveHighlight(null, 'reset', context);\n  }\n\n  // exclude selected values that are no longer in the items list\n  const selectedValues = (_state$selectedValues = state.selectedValues) != null ? _state$selectedValues : [];\n  const newSelectedValues = selectedValues.filter(selectedValue => items.some(item => itemComparer(item, selectedValue)));\n  return _extends({}, state, {\n    highlightedValue: newHighlightedValue,\n    selectedValues: newSelectedValues\n  });\n}\nfunction handleResetHighlight(state, context) {\n  return _extends({}, state, {\n    highlightedValue: moveHighlight(null, 'reset', context)\n  });\n}\nfunction handleClearSelection(state, context) {\n  return _extends({}, state, {\n    selectedValues: [],\n    highlightedValue: moveHighlight(null, 'reset', context)\n  });\n}\nexport function listReducer(state, action) {\n  const {\n    type,\n    context\n  } = action;\n  switch (type) {\n    case ListActionTypes.keyDown:\n      return handleKeyDown(action.key, state, context);\n    case ListActionTypes.itemClick:\n      return handleItemSelection(action.item, state, context);\n    case ListActionTypes.blur:\n      return handleBlur(state, context);\n    case ListActionTypes.textNavigation:\n      return handleTextNavigation(state, action.searchString, context);\n    case ListActionTypes.itemsChange:\n      return handleItemsChange(action.items, action.previousItems, state, context);\n    case ListActionTypes.resetHighlight:\n      return handleResetHighlight(state, context);\n    case ListActionTypes.clearSelection:\n      return handleClearSelection(state, context);\n    default:\n      return state;\n  }\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,SAASC,eAAe,QAAQ,qBAAqB;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,wBAAwBA,CAACC,YAAY,EAAEC,eAAe,EAAEC,KAAK,EAAEC,oBAAoB,EAAEC,cAAc,EAAEC,UAAU,EAAE;EACxH,IAAIH,KAAK,CAACI,MAAM,KAAK,CAAC,IAAI,CAACH,oBAAoB,IAAID,KAAK,CAACK,KAAK,CAAC,CAACC,IAAI,EAAEC,SAAS,KAAKL,cAAc,CAACI,IAAI,EAAEC,SAAS,CAAC,CAAC,EAAE;IACpH,OAAO,CAAC,CAAC;EACX;EACA,IAAIC,SAAS,GAAGV,YAAY;EAC5B,SAAS;IACP;IACA,IAAI,CAACK,UAAU,IAAIJ,eAAe,KAAK,MAAM,IAAIS,SAAS,KAAKR,KAAK,CAACI,MAAM,IAAI,CAACD,UAAU,IAAIJ,eAAe,KAAK,UAAU,IAAIS,SAAS,KAAK,CAAC,CAAC,EAAE;MAChJ,OAAO,CAAC,CAAC;IACX;IACA,MAAMC,iBAAiB,GAAGR,oBAAoB,GAAG,KAAK,GAAGC,cAAc,CAACF,KAAK,CAACQ,SAAS,CAAC,EAAEA,SAAS,CAAC;IACpG,IAAIC,iBAAiB,EAAE;MACrBD,SAAS,IAAIT,eAAe,KAAK,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;MAChD,IAAII,UAAU,EAAE;QACdK,SAAS,GAAG,CAACA,SAAS,GAAGR,KAAK,CAACI,MAAM,IAAIJ,KAAK,CAACI,MAAM;MACvD;IACF,CAAC,MAAM;MACL,OAAOI,SAAS;IAClB;EACF;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASE,aAAaA,CAACC,0BAA0B,EAAEC,MAAM,EAAEC,OAAO,EAAE;EACzE,IAAIC,gBAAgB;EACpB,MAAM;IACJd,KAAK;IACLE,cAAc;IACda,eAAe;IACfC,sBAAsB;IACtBC,YAAY;IACZC;EACF,CAAC,GAAGL,OAAO;;EAEX;EACA;EACA;EACA,MAAMM,uBAAuB,GAAGD,eAAe,KAAK,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;EAClE,MAAME,QAAQ,GAAGpB,KAAK,CAACI,MAAM,GAAG,CAAC;EACjC,MAAMiB,0BAA0B,GAAGV,0BAA0B,IAAI,IAAI,GAAG,CAAC,CAAC,GAAGX,KAAK,CAACsB,SAAS,CAAChB,IAAI,IAAIW,YAAY,CAACX,IAAI,EAAEK,0BAA0B,CAAC,CAAC;EACpJ,IAAIY,kBAAkB;EACtB,IAAIxB,eAAe;EACnB,IAAII,UAAU,GAAG,CAACY,eAAe;EACjC,QAAQH,MAAM;IACZ,KAAK,OAAO;MACV,IAAIO,uBAAuB,KAAK,CAAC,CAAC,EAAE;QAClC,OAAO,IAAI;MACb;MACAI,kBAAkB,GAAG,CAAC;MACtBxB,eAAe,GAAG,MAAM;MACxBI,UAAU,GAAG,KAAK;MAClB;IACF,KAAK,OAAO;MACVoB,kBAAkB,GAAG,CAAC;MACtBxB,eAAe,GAAG,MAAM;MACxBI,UAAU,GAAG,KAAK;MAClB;IACF,KAAK,KAAK;MACRoB,kBAAkB,GAAGH,QAAQ;MAC7BrB,eAAe,GAAG,UAAU;MAC5BI,UAAU,GAAG,KAAK;MAClB;IACF;MACE;QACE,MAAMqB,QAAQ,GAAGH,0BAA0B,GAAGT,MAAM;QACpD,IAAIY,QAAQ,GAAG,CAAC,EAAE;UAChB,IAAI,CAACrB,UAAU,IAAIkB,0BAA0B,KAAK,CAAC,CAAC,IAAII,IAAI,CAACC,GAAG,CAACd,MAAM,CAAC,GAAG,CAAC,EAAE;YAC5EW,kBAAkB,GAAG,CAAC;YACtBxB,eAAe,GAAG,MAAM;UAC1B,CAAC,MAAM;YACLwB,kBAAkB,GAAGH,QAAQ;YAC7BrB,eAAe,GAAG,UAAU;UAC9B;QACF,CAAC,MAAM,IAAIyB,QAAQ,GAAGJ,QAAQ,EAAE;UAC9B,IAAI,CAACjB,UAAU,IAAIsB,IAAI,CAACC,GAAG,CAACd,MAAM,CAAC,GAAG,CAAC,EAAE;YACvCW,kBAAkB,GAAGH,QAAQ;YAC7BrB,eAAe,GAAG,UAAU;UAC9B,CAAC,MAAM;YACLwB,kBAAkB,GAAG,CAAC;YACtBxB,eAAe,GAAG,MAAM;UAC1B;QACF,CAAC,MAAM;UACLwB,kBAAkB,GAAGC,QAAQ;UAC7BzB,eAAe,GAAGa,MAAM,IAAI,CAAC,GAAG,MAAM,GAAG,UAAU;QACrD;MACF;EACJ;EACA,MAAMe,SAAS,GAAG9B,wBAAwB,CAAC0B,kBAAkB,EAAExB,eAAe,EAAEC,KAAK,EAAEgB,sBAAsB,EAAEd,cAAc,EAAEC,UAAU,CAAC;;EAE1I;EACA,IAAIwB,SAAS,KAAK,CAAC,CAAC,IAAIhB,0BAA0B,KAAK,IAAI,IAAI,CAACT,cAAc,CAACS,0BAA0B,EAAEU,0BAA0B,CAAC,EAAE;IACtI,OAAOV,0BAA0B;EACnC;EACA,OAAO,CAACG,gBAAgB,GAAGd,KAAK,CAAC2B,SAAS,CAAC,KAAK,IAAI,GAAGb,gBAAgB,GAAG,IAAI;AAChF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASc,eAAeA,CAACtB,IAAI,EAAEuB,cAAc,EAAEC,aAAa,EAAEb,YAAY,EAAE;EACjF,IAAIa,aAAa,KAAK,MAAM,EAAE;IAC5B,OAAO,EAAE;EACX;EACA,IAAIA,aAAa,KAAK,QAAQ,EAAE;IAC9B;IACA,IAAIb,YAAY,CAACY,cAAc,CAAC,CAAC,CAAC,EAAEvB,IAAI,CAAC,EAAE;MACzC,OAAOuB,cAAc;IACvB;IACA,OAAO,CAACvB,IAAI,CAAC;EACf;;EAEA;EACA,IAAIuB,cAAc,CAACE,IAAI,CAACC,EAAE,IAAIf,YAAY,CAACe,EAAE,EAAE1B,IAAI,CAAC,CAAC,EAAE;IACrD,OAAOuB,cAAc,CAACI,MAAM,CAACD,EAAE,IAAI,CAACf,YAAY,CAACe,EAAE,EAAE1B,IAAI,CAAC,CAAC;EAC7D;;EAEA;EACA,OAAO,CAAC,GAAGuB,cAAc,EAAEvB,IAAI,CAAC;AAClC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS4B,mBAAmBA,CAAC5B,IAAI,EAAE6B,KAAK,EAAEtB,OAAO,EAAE;EACxD,MAAM;IACJI,YAAY;IACZf,cAAc;IACd4B,aAAa;IACb9B;EACF,CAAC,GAAGa,OAAO;EACX,MAAM;IACJgB;EACF,CAAC,GAAGM,KAAK;EACT,MAAM5B,SAAS,GAAGP,KAAK,CAACsB,SAAS,CAACc,CAAC,IAAInB,YAAY,CAACX,IAAI,EAAE8B,CAAC,CAAC,CAAC;EAC7D,IAAIlC,cAAc,CAACI,IAAI,EAAEC,SAAS,CAAC,EAAE;IACnC,OAAO4B,KAAK;EACd;;EAEA;EACA,MAAME,iBAAiB,GAAGT,eAAe,CAACtB,IAAI,EAAEuB,cAAc,EAAEC,aAAa,EAAEb,YAAY,CAAC;EAC5F,OAAOtB,QAAQ,CAAC,CAAC,CAAC,EAAEwC,KAAK,EAAE;IACzBN,cAAc,EAAEQ,iBAAiB;IACjCC,gBAAgB,EAAEhC;EACpB,CAAC,CAAC;AACJ;AACA,SAASiC,aAAaA,CAACC,GAAG,EAAEL,KAAK,EAAEtB,OAAO,EAAE;EAC1C,MAAM4B,uBAAuB,GAAGN,KAAK,CAACG,gBAAgB;EACtD,MAAM;IACJI,WAAW;IACXC;EACF,CAAC,GAAG9B,OAAO;EACX,QAAQ2B,GAAG;IACT,KAAK,MAAM;MACT,OAAO7C,QAAQ,CAAC,CAAC,CAAC,EAAEwC,KAAK,EAAE;QACzBG,gBAAgB,EAAE5B,aAAa,CAAC+B,uBAAuB,EAAE,OAAO,EAAE5B,OAAO;MAC3E,CAAC,CAAC;IACJ,KAAK,KAAK;MACR,OAAOlB,QAAQ,CAAC,CAAC,CAAC,EAAEwC,KAAK,EAAE;QACzBG,gBAAgB,EAAE5B,aAAa,CAAC+B,uBAAuB,EAAE,KAAK,EAAE5B,OAAO;MACzE,CAAC,CAAC;IACJ,KAAK,QAAQ;MACX,OAAOlB,QAAQ,CAAC,CAAC,CAAC,EAAEwC,KAAK,EAAE;QACzBG,gBAAgB,EAAE5B,aAAa,CAAC+B,uBAAuB,EAAE,CAACE,QAAQ,EAAE9B,OAAO;MAC7E,CAAC,CAAC;IACJ,KAAK,UAAU;MACb,OAAOlB,QAAQ,CAAC,CAAC,CAAC,EAAEwC,KAAK,EAAE;QACzBG,gBAAgB,EAAE5B,aAAa,CAAC+B,uBAAuB,EAAEE,QAAQ,EAAE9B,OAAO;MAC5E,CAAC,CAAC;IACJ,KAAK,SAAS;MACZ,IAAI6B,WAAW,KAAK,UAAU,EAAE;QAC9B;MACF;MACA,OAAO/C,QAAQ,CAAC,CAAC,CAAC,EAAEwC,KAAK,EAAE;QACzBG,gBAAgB,EAAE5B,aAAa,CAAC+B,uBAAuB,EAAE,CAAC,CAAC,EAAE5B,OAAO;MACtE,CAAC,CAAC;IACJ,KAAK,WAAW;MACd,IAAI6B,WAAW,KAAK,UAAU,EAAE;QAC9B;MACF;MACA,OAAO/C,QAAQ,CAAC,CAAC,CAAC,EAAEwC,KAAK,EAAE;QACzBG,gBAAgB,EAAE5B,aAAa,CAAC+B,uBAAuB,EAAE,CAAC,EAAE5B,OAAO;MACrE,CAAC,CAAC;IACJ,KAAK,WAAW;MACd;QACE,IAAI6B,WAAW,KAAK,UAAU,EAAE;UAC9B;QACF;QACA,MAAM9B,MAAM,GAAG8B,WAAW,KAAK,gBAAgB,GAAG,CAAC,CAAC,GAAG,CAAC;QACxD,OAAO/C,QAAQ,CAAC,CAAC,CAAC,EAAEwC,KAAK,EAAE;UACzBG,gBAAgB,EAAE5B,aAAa,CAAC+B,uBAAuB,EAAE7B,MAAM,EAAEC,OAAO;QAC1E,CAAC,CAAC;MACJ;IACF,KAAK,YAAY;MACf;QACE,IAAI6B,WAAW,KAAK,UAAU,EAAE;UAC9B;QACF;QACA,MAAM9B,MAAM,GAAG8B,WAAW,KAAK,gBAAgB,GAAG,CAAC,GAAG,CAAC,CAAC;QACxD,OAAO/C,QAAQ,CAAC,CAAC,CAAC,EAAEwC,KAAK,EAAE;UACzBG,gBAAgB,EAAE5B,aAAa,CAAC+B,uBAAuB,EAAE7B,MAAM,EAAEC,OAAO;QAC1E,CAAC,CAAC;MACJ;IACF,KAAK,OAAO;IACZ,KAAK,GAAG;MACN,IAAIsB,KAAK,CAACG,gBAAgB,KAAK,IAAI,EAAE;QACnC,OAAOH,KAAK;MACd;MACA,OAAOD,mBAAmB,CAACC,KAAK,CAACG,gBAAgB,EAAEH,KAAK,EAAEtB,OAAO,CAAC;IACpE;MACE;EACJ;EACA,OAAOsB,KAAK;AACd;AACA,SAASS,UAAUA,CAACT,KAAK,EAAEtB,OAAO,EAAE;EAClC,IAAIA,OAAO,CAACK,eAAe,KAAK,KAAK,EAAE;IACrC,OAAOiB,KAAK;EACd;EACA,OAAOxC,QAAQ,CAAC,CAAC,CAAC,EAAEwC,KAAK,EAAE;IACzBG,gBAAgB,EAAE;EACpB,CAAC,CAAC;AACJ;AACA,SAASO,mBAAmBA,CAACrC,SAAS,EAAEsC,YAAY,EAAEC,aAAa,EAAE;EACnE,IAAIC,cAAc;EAClB,MAAMC,IAAI,GAAG,CAACD,cAAc,GAAGD,aAAa,CAACvC,SAAS,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGwC,cAAc,CAACE,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;EAC/G,IAAI,CAACF,IAAI,IAAIA,IAAI,CAAC7C,MAAM,KAAK,CAAC,EAAE;IAC9B;IACA,OAAO,KAAK;EACd;EACA,OAAO6C,IAAI,CAACG,OAAO,CAACN,YAAY,CAAC,KAAK,CAAC;AACzC;AACA,SAASO,oBAAoBA,CAAClB,KAAK,EAAEW,YAAY,EAAEjC,OAAO,EAAE;EAC1D,MAAM;IACJb,KAAK;IACLE,cAAc;IACdc,sBAAsB;IACtBsC;EACF,CAAC,GAAGzC,OAAO;EACX,MAAM0C,oBAAoB,GAAGT,YAAY,CAAC1C,MAAM,GAAG,CAAC;EACpD,IAAIoD,QAAQ,GAAGD,oBAAoB,GAAGpB,KAAK,CAACG,gBAAgB,GAAG5B,aAAa,CAACyB,KAAK,CAACG,gBAAgB,EAAE,CAAC,EAAEzB,OAAO,CAAC;EAChH,KAAK,IAAI4C,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGzD,KAAK,CAACI,MAAM,EAAEqD,KAAK,IAAI,CAAC,EAAE;IACpD;IACA,IAAI,CAACD,QAAQ,IAAI,CAACD,oBAAoB,IAAIpB,KAAK,CAACG,gBAAgB,KAAKkB,QAAQ,EAAE;MAC7E,OAAOrB,KAAK;IACd;IACA,IAAIU,mBAAmB,CAACW,QAAQ,EAAEV,YAAY,EAAEQ,eAAe,CAAC,KAAK,CAACpD,cAAc,CAACsD,QAAQ,EAAExD,KAAK,CAACoD,OAAO,CAACI,QAAQ,CAAC,CAAC,IAAIxC,sBAAsB,CAAC,EAAE;MAClJ;MACA,OAAOrB,QAAQ,CAAC,CAAC,CAAC,EAAEwC,KAAK,EAAE;QACzBG,gBAAgB,EAAEkB;MACpB,CAAC,CAAC;IACJ;IACA;IACAA,QAAQ,GAAG9C,aAAa,CAAC8C,QAAQ,EAAE,CAAC,EAAE3C,OAAO,CAAC;EAChD;;EAEA;EACA,OAAOsB,KAAK;AACd;AACA,SAASuB,iBAAiBA,CAAC1D,KAAK,EAAE2D,aAAa,EAAExB,KAAK,EAAEtB,OAAO,EAAE;EAC/D,IAAI+C,qBAAqB;EACzB,MAAM;IACJ3C,YAAY;IACZC;EACF,CAAC,GAAGL,OAAO;EACX,IAAIgD,mBAAmB,GAAG,IAAI;EAC9B,IAAI1B,KAAK,CAACG,gBAAgB,IAAI,IAAI,EAAE;IAClC,IAAIwB,WAAW;IACfD,mBAAmB,GAAG,CAACC,WAAW,GAAG9D,KAAK,CAAC+D,IAAI,CAACzD,IAAI,IAAIW,YAAY,CAACX,IAAI,EAAE6B,KAAK,CAACG,gBAAgB,CAAC,CAAC,KAAK,IAAI,GAAGwB,WAAW,GAAG,IAAI;EACnI,CAAC,MAAM,IAAI5C,eAAe,KAAK,KAAK,IAAIyC,aAAa,CAACvD,MAAM,KAAK,CAAC,EAAE;IAClEyD,mBAAmB,GAAGnD,aAAa,CAAC,IAAI,EAAE,OAAO,EAAEG,OAAO,CAAC;EAC7D;;EAEA;EACA,MAAMgB,cAAc,GAAG,CAAC+B,qBAAqB,GAAGzB,KAAK,CAACN,cAAc,KAAK,IAAI,GAAG+B,qBAAqB,GAAG,EAAE;EAC1G,MAAMvB,iBAAiB,GAAGR,cAAc,CAACI,MAAM,CAAC+B,aAAa,IAAIhE,KAAK,CAAC+B,IAAI,CAACzB,IAAI,IAAIW,YAAY,CAACX,IAAI,EAAE0D,aAAa,CAAC,CAAC,CAAC;EACvH,OAAOrE,QAAQ,CAAC,CAAC,CAAC,EAAEwC,KAAK,EAAE;IACzBG,gBAAgB,EAAEuB,mBAAmB;IACrChC,cAAc,EAAEQ;EAClB,CAAC,CAAC;AACJ;AACA,SAAS4B,oBAAoBA,CAAC9B,KAAK,EAAEtB,OAAO,EAAE;EAC5C,OAAOlB,QAAQ,CAAC,CAAC,CAAC,EAAEwC,KAAK,EAAE;IACzBG,gBAAgB,EAAE5B,aAAa,CAAC,IAAI,EAAE,OAAO,EAAEG,OAAO;EACxD,CAAC,CAAC;AACJ;AACA,SAASqD,oBAAoBA,CAAC/B,KAAK,EAAEtB,OAAO,EAAE;EAC5C,OAAOlB,QAAQ,CAAC,CAAC,CAAC,EAAEwC,KAAK,EAAE;IACzBN,cAAc,EAAE,EAAE;IAClBS,gBAAgB,EAAE5B,aAAa,CAAC,IAAI,EAAE,OAAO,EAAEG,OAAO;EACxD,CAAC,CAAC;AACJ;AACA,OAAO,SAASsD,WAAWA,CAAChC,KAAK,EAAEiC,MAAM,EAAE;EACzC,MAAM;IACJC,IAAI;IACJxD;EACF,CAAC,GAAGuD,MAAM;EACV,QAAQC,IAAI;IACV,KAAKzE,eAAe,CAAC0E,OAAO;MAC1B,OAAO/B,aAAa,CAAC6B,MAAM,CAAC5B,GAAG,EAAEL,KAAK,EAAEtB,OAAO,CAAC;IAClD,KAAKjB,eAAe,CAAC2E,SAAS;MAC5B,OAAOrC,mBAAmB,CAACkC,MAAM,CAAC9D,IAAI,EAAE6B,KAAK,EAAEtB,OAAO,CAAC;IACzD,KAAKjB,eAAe,CAAC4E,IAAI;MACvB,OAAO5B,UAAU,CAACT,KAAK,EAAEtB,OAAO,CAAC;IACnC,KAAKjB,eAAe,CAAC6E,cAAc;MACjC,OAAOpB,oBAAoB,CAAClB,KAAK,EAAEiC,MAAM,CAACtB,YAAY,EAAEjC,OAAO,CAAC;IAClE,KAAKjB,eAAe,CAAC8E,WAAW;MAC9B,OAAOhB,iBAAiB,CAACU,MAAM,CAACpE,KAAK,EAAEoE,MAAM,CAACT,aAAa,EAAExB,KAAK,EAAEtB,OAAO,CAAC;IAC9E,KAAKjB,eAAe,CAAC+E,cAAc;MACjC,OAAOV,oBAAoB,CAAC9B,KAAK,EAAEtB,OAAO,CAAC;IAC7C,KAAKjB,eAAe,CAACgF,cAAc;MACjC,OAAOV,oBAAoB,CAAC/B,KAAK,EAAEtB,OAAO,CAAC;IAC7C;MACE,OAAOsB,KAAK;EAChB;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}