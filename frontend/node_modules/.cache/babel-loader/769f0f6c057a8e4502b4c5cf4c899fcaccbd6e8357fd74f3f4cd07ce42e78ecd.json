{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nexport function isPlainObject(item) {\n  return item !== null && typeof item === 'object' && item.constructor === Object;\n}\nfunction deepClone(source) {\n  if (!isPlainObject(source)) {\n    return source;\n  }\n  const output = {};\n  Object.keys(source).forEach(key => {\n    output[key] = deepClone(source[key]);\n  });\n  return output;\n}\nexport default function deepmerge(target, source, options = {\n  clone: true\n}) {\n  const output = options.clone ? _extends({}, target) : target;\n  if (isPlainObject(target) && isPlainObject(source)) {\n    Object.keys(source).forEach(key => {\n      // Avoid prototype pollution\n      if (key === '__proto__') {\n        return;\n      }\n      if (isPlainObject(source[key]) && key in target && isPlainObject(target[key])) {\n        // Since `output` is a clone of `target` and we have narrowed `target` in this block we can cast to the same type.\n        output[key] = deepmerge(target[key], source[key], options);\n      } else if (options.clone) {\n        output[key] = isPlainObject(source[key]) ? deepClone(source[key]) : source[key];\n      } else {\n        output[key] = source[key];\n      }\n    });\n  }\n  return output;\n}", "map": {"version": 3, "names": ["_extends", "isPlainObject", "item", "constructor", "Object", "deepClone", "source", "output", "keys", "for<PERSON>ach", "key", "deepmerge", "target", "options", "clone"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/utils/esm/deepmerge.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nexport function isPlainObject(item) {\n  return item !== null && typeof item === 'object' && item.constructor === Object;\n}\nfunction deepClone(source) {\n  if (!isPlainObject(source)) {\n    return source;\n  }\n  const output = {};\n  Object.keys(source).forEach(key => {\n    output[key] = deepClone(source[key]);\n  });\n  return output;\n}\nexport default function deepmerge(target, source, options = {\n  clone: true\n}) {\n  const output = options.clone ? _extends({}, target) : target;\n  if (isPlainObject(target) && isPlainObject(source)) {\n    Object.keys(source).forEach(key => {\n      // Avoid prototype pollution\n      if (key === '__proto__') {\n        return;\n      }\n      if (isPlainObject(source[key]) && key in target && isPlainObject(target[key])) {\n        // Since `output` is a clone of `target` and we have narrowed `target` in this block we can cast to the same type.\n        output[key] = deepmerge(target[key], source[key], options);\n      } else if (options.clone) {\n        output[key] = isPlainObject(source[key]) ? deepClone(source[key]) : source[key];\n      } else {\n        output[key] = source[key];\n      }\n    });\n  }\n  return output;\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,SAASC,aAAaA,CAACC,IAAI,EAAE;EAClC,OAAOA,IAAI,KAAK,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAIA,IAAI,CAACC,WAAW,KAAKC,MAAM;AACjF;AACA,SAASC,SAASA,CAACC,MAAM,EAAE;EACzB,IAAI,CAACL,aAAa,CAACK,MAAM,CAAC,EAAE;IAC1B,OAAOA,MAAM;EACf;EACA,MAAMC,MAAM,GAAG,CAAC,CAAC;EACjBH,MAAM,CAACI,IAAI,CAACF,MAAM,CAAC,CAACG,OAAO,CAACC,GAAG,IAAI;IACjCH,MAAM,CAACG,GAAG,CAAC,GAAGL,SAAS,CAACC,MAAM,CAACI,GAAG,CAAC,CAAC;EACtC,CAAC,CAAC;EACF,OAAOH,MAAM;AACf;AACA,eAAe,SAASI,SAASA,CAACC,MAAM,EAAEN,MAAM,EAAEO,OAAO,GAAG;EAC1DC,KAAK,EAAE;AACT,CAAC,EAAE;EACD,MAAMP,MAAM,GAAGM,OAAO,CAACC,KAAK,GAAGd,QAAQ,CAAC,CAAC,CAAC,EAAEY,MAAM,CAAC,GAAGA,MAAM;EAC5D,IAAIX,aAAa,CAACW,MAAM,CAAC,IAAIX,aAAa,CAACK,MAAM,CAAC,EAAE;IAClDF,MAAM,CAACI,IAAI,CAACF,MAAM,CAAC,CAACG,OAAO,CAACC,GAAG,IAAI;MACjC;MACA,IAAIA,GAAG,KAAK,WAAW,EAAE;QACvB;MACF;MACA,IAAIT,aAAa,CAACK,MAAM,CAACI,GAAG,CAAC,CAAC,IAAIA,GAAG,IAAIE,MAAM,IAAIX,aAAa,CAACW,MAAM,CAACF,GAAG,CAAC,CAAC,EAAE;QAC7E;QACAH,MAAM,CAACG,GAAG,CAAC,GAAGC,SAAS,CAACC,MAAM,CAACF,GAAG,CAAC,EAAEJ,MAAM,CAACI,GAAG,CAAC,EAAEG,OAAO,CAAC;MAC5D,CAAC,MAAM,IAAIA,OAAO,CAACC,KAAK,EAAE;QACxBP,MAAM,CAACG,GAAG,CAAC,GAAGT,aAAa,CAACK,MAAM,CAACI,GAAG,CAAC,CAAC,GAAGL,SAAS,CAACC,MAAM,CAACI,GAAG,CAAC,CAAC,GAAGJ,MAAM,CAACI,GAAG,CAAC;MACjF,CAAC,MAAM;QACLH,MAAM,CAACG,GAAG,CAAC,GAAGJ,MAAM,CAACI,GAAG,CAAC;MAC3B;IACF,CAAC,CAAC;EACJ;EACA,OAAOH,MAAM;AACf"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}