{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"anchorOrigin\", \"className\", \"classes\", \"component\", \"components\", \"componentsProps\", \"children\", \"overlap\", \"color\", \"invisible\", \"max\", \"badgeContent\", \"slots\", \"slotProps\", \"showZero\", \"variant\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { usePreviousProps } from '@mui/utils';\nimport { unstable_composeClasses as composeClasses } from '@mui/base/composeClasses';\nimport { useBadge } from '@mui/base/useBadge';\nimport { useSlotProps } from '@mui/base';\nimport styled from '../styles/styled';\nimport useThemeProps from '../styles/useThemeProps';\nimport capitalize from '../utils/capitalize';\nimport badgeClasses, { getBadgeUtilityClass } from './badgeClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst RADIUS_STANDARD = 10;\nconst RADIUS_DOT = 4;\nconst useUtilityClasses = ownerState => {\n  const {\n    color,\n    anchorOrigin,\n    invisible,\n    overlap,\n    variant,\n    classes = {}\n  } = ownerState;\n  const slots = {\n    root: ['root'],\n    badge: ['badge', variant, invisible && 'invisible', `anchorOrigin${capitalize(anchorOrigin.vertical)}${capitalize(anchorOrigin.horizontal)}`, `anchorOrigin${capitalize(anchorOrigin.vertical)}${capitalize(anchorOrigin.horizontal)}${capitalize(overlap)}`, `overlap${capitalize(overlap)}`, color !== 'default' && `color${capitalize(color)}`]\n  };\n  return composeClasses(slots, getBadgeUtilityClass, classes);\n};\nconst BadgeRoot = styled('span', {\n  name: 'MuiBadge',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})({\n  position: 'relative',\n  display: 'inline-flex',\n  // For correct alignment with the text.\n  verticalAlign: 'middle',\n  flexShrink: 0\n});\nconst BadgeBadge = styled('span', {\n  name: 'MuiBadge',\n  slot: 'Badge',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.badge, styles[ownerState.variant], styles[`anchorOrigin${capitalize(ownerState.anchorOrigin.vertical)}${capitalize(ownerState.anchorOrigin.horizontal)}${capitalize(ownerState.overlap)}`], ownerState.color !== 'default' && styles[`color${capitalize(ownerState.color)}`], ownerState.invisible && styles.invisible];\n  }\n})(({\n  theme,\n  ownerState\n}) => _extends({\n  display: 'flex',\n  flexDirection: 'row',\n  flexWrap: 'wrap',\n  justifyContent: 'center',\n  alignContent: 'center',\n  alignItems: 'center',\n  position: 'absolute',\n  boxSizing: 'border-box',\n  fontFamily: theme.typography.fontFamily,\n  fontWeight: theme.typography.fontWeightMedium,\n  fontSize: theme.typography.pxToRem(12),\n  minWidth: RADIUS_STANDARD * 2,\n  lineHeight: 1,\n  padding: '0 6px',\n  height: RADIUS_STANDARD * 2,\n  borderRadius: RADIUS_STANDARD,\n  zIndex: 1,\n  // Render the badge on top of potential ripples.\n  transition: theme.transitions.create('transform', {\n    easing: theme.transitions.easing.easeInOut,\n    duration: theme.transitions.duration.enteringScreen\n  })\n}, ownerState.color !== 'default' && {\n  backgroundColor: (theme.vars || theme).palette[ownerState.color].main,\n  color: (theme.vars || theme).palette[ownerState.color].contrastText\n}, ownerState.variant === 'dot' && {\n  borderRadius: RADIUS_DOT,\n  height: RADIUS_DOT * 2,\n  minWidth: RADIUS_DOT * 2,\n  padding: 0\n}, ownerState.anchorOrigin.vertical === 'top' && ownerState.anchorOrigin.horizontal === 'right' && ownerState.overlap === 'rectangular' && {\n  top: 0,\n  right: 0,\n  transform: 'scale(1) translate(50%, -50%)',\n  transformOrigin: '100% 0%',\n  [`&.${badgeClasses.invisible}`]: {\n    transform: 'scale(0) translate(50%, -50%)'\n  }\n}, ownerState.anchorOrigin.vertical === 'bottom' && ownerState.anchorOrigin.horizontal === 'right' && ownerState.overlap === 'rectangular' && {\n  bottom: 0,\n  right: 0,\n  transform: 'scale(1) translate(50%, 50%)',\n  transformOrigin: '100% 100%',\n  [`&.${badgeClasses.invisible}`]: {\n    transform: 'scale(0) translate(50%, 50%)'\n  }\n}, ownerState.anchorOrigin.vertical === 'top' && ownerState.anchorOrigin.horizontal === 'left' && ownerState.overlap === 'rectangular' && {\n  top: 0,\n  left: 0,\n  transform: 'scale(1) translate(-50%, -50%)',\n  transformOrigin: '0% 0%',\n  [`&.${badgeClasses.invisible}`]: {\n    transform: 'scale(0) translate(-50%, -50%)'\n  }\n}, ownerState.anchorOrigin.vertical === 'bottom' && ownerState.anchorOrigin.horizontal === 'left' && ownerState.overlap === 'rectangular' && {\n  bottom: 0,\n  left: 0,\n  transform: 'scale(1) translate(-50%, 50%)',\n  transformOrigin: '0% 100%',\n  [`&.${badgeClasses.invisible}`]: {\n    transform: 'scale(0) translate(-50%, 50%)'\n  }\n}, ownerState.anchorOrigin.vertical === 'top' && ownerState.anchorOrigin.horizontal === 'right' && ownerState.overlap === 'circular' && {\n  top: '14%',\n  right: '14%',\n  transform: 'scale(1) translate(50%, -50%)',\n  transformOrigin: '100% 0%',\n  [`&.${badgeClasses.invisible}`]: {\n    transform: 'scale(0) translate(50%, -50%)'\n  }\n}, ownerState.anchorOrigin.vertical === 'bottom' && ownerState.anchorOrigin.horizontal === 'right' && ownerState.overlap === 'circular' && {\n  bottom: '14%',\n  right: '14%',\n  transform: 'scale(1) translate(50%, 50%)',\n  transformOrigin: '100% 100%',\n  [`&.${badgeClasses.invisible}`]: {\n    transform: 'scale(0) translate(50%, 50%)'\n  }\n}, ownerState.anchorOrigin.vertical === 'top' && ownerState.anchorOrigin.horizontal === 'left' && ownerState.overlap === 'circular' && {\n  top: '14%',\n  left: '14%',\n  transform: 'scale(1) translate(-50%, -50%)',\n  transformOrigin: '0% 0%',\n  [`&.${badgeClasses.invisible}`]: {\n    transform: 'scale(0) translate(-50%, -50%)'\n  }\n}, ownerState.anchorOrigin.vertical === 'bottom' && ownerState.anchorOrigin.horizontal === 'left' && ownerState.overlap === 'circular' && {\n  bottom: '14%',\n  left: '14%',\n  transform: 'scale(1) translate(-50%, 50%)',\n  transformOrigin: '0% 100%',\n  [`&.${badgeClasses.invisible}`]: {\n    transform: 'scale(0) translate(-50%, 50%)'\n  }\n}, ownerState.invisible && {\n  transition: theme.transitions.create('transform', {\n    easing: theme.transitions.easing.easeInOut,\n    duration: theme.transitions.duration.leavingScreen\n  })\n}));\nconst Badge = /*#__PURE__*/React.forwardRef(function Badge(inProps, ref) {\n  var _ref, _slots$root, _ref2, _slots$badge, _slotProps$root, _slotProps$badge;\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiBadge'\n  });\n  const {\n      anchorOrigin: anchorOriginProp = {\n        vertical: 'top',\n        horizontal: 'right'\n      },\n      className,\n      component,\n      components = {},\n      componentsProps = {},\n      children,\n      overlap: overlapProp = 'rectangular',\n      color: colorProp = 'default',\n      invisible: invisibleProp = false,\n      max: maxProp = 99,\n      badgeContent: badgeContentProp,\n      slots,\n      slotProps,\n      showZero = false,\n      variant: variantProp = 'standard'\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    badgeContent,\n    invisible: invisibleFromHook,\n    max,\n    displayValue: displayValueFromHook\n  } = useBadge({\n    max: maxProp,\n    invisible: invisibleProp,\n    badgeContent: badgeContentProp,\n    showZero\n  });\n  const prevProps = usePreviousProps({\n    anchorOrigin: anchorOriginProp,\n    color: colorProp,\n    overlap: overlapProp,\n    variant: variantProp,\n    badgeContent: badgeContentProp\n  });\n  const invisible = invisibleFromHook || badgeContent == null && variantProp !== 'dot';\n  const {\n    color = colorProp,\n    overlap = overlapProp,\n    anchorOrigin = anchorOriginProp,\n    variant = variantProp\n  } = invisible ? prevProps : props;\n  const displayValue = variant !== 'dot' ? displayValueFromHook : undefined;\n  const ownerState = _extends({}, props, {\n    badgeContent,\n    invisible,\n    max,\n    displayValue,\n    showZero,\n    anchorOrigin,\n    color,\n    overlap,\n    variant\n  });\n  const classes = useUtilityClasses(ownerState);\n\n  // support both `slots` and `components` for backward compatibility\n  const RootSlot = (_ref = (_slots$root = slots == null ? void 0 : slots.root) != null ? _slots$root : components.Root) != null ? _ref : BadgeRoot;\n  const BadgeSlot = (_ref2 = (_slots$badge = slots == null ? void 0 : slots.badge) != null ? _slots$badge : components.Badge) != null ? _ref2 : BadgeBadge;\n  const rootSlotProps = (_slotProps$root = slotProps == null ? void 0 : slotProps.root) != null ? _slotProps$root : componentsProps.root;\n  const badgeSlotProps = (_slotProps$badge = slotProps == null ? void 0 : slotProps.badge) != null ? _slotProps$badge : componentsProps.badge;\n  const rootProps = useSlotProps({\n    elementType: RootSlot,\n    externalSlotProps: rootSlotProps,\n    externalForwardedProps: other,\n    additionalProps: {\n      ref,\n      as: component\n    },\n    ownerState,\n    className: clsx(rootSlotProps == null ? void 0 : rootSlotProps.className, classes.root, className)\n  });\n  const badgeProps = useSlotProps({\n    elementType: BadgeSlot,\n    externalSlotProps: badgeSlotProps,\n    ownerState,\n    className: clsx(classes.badge, badgeSlotProps == null ? void 0 : badgeSlotProps.className)\n  });\n  return /*#__PURE__*/_jsxs(RootSlot, _extends({}, rootProps, {\n    children: [children, /*#__PURE__*/_jsx(BadgeSlot, _extends({}, badgeProps, {\n      children: displayValue\n    }))]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? Badge.propTypes /* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit the d.ts file and run \"yarn proptypes\"     |\n  // ----------------------------------------------------------------------\n  /**\n   * The anchor of the badge.\n   * @default {\n   *   vertical: 'top',\n   *   horizontal: 'right',\n   * }\n   */\n  anchorOrigin: PropTypes.shape({\n    horizontal: PropTypes.oneOf(['left', 'right']).isRequired,\n    vertical: PropTypes.oneOf(['bottom', 'top']).isRequired\n  }),\n  /**\n   * The content rendered within the badge.\n   */\n  badgeContent: PropTypes.node,\n  /**\n   * The badge will be added relative to this node.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The color of the component.\n   * It supports both default and custom theme colors, which can be added as shown in the\n   * [palette customization guide](https://mui.com/material-ui/customization/palette/#custom-colors).\n   * @default 'default'\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['default', 'primary', 'secondary', 'error', 'info', 'success', 'warning']), PropTypes.string]),\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The components used for each slot inside.\n   *\n   * This prop is an alias for the `slots` prop.\n   * It's recommended to use the `slots` prop instead.\n   *\n   * @default {}\n   */\n  components: PropTypes.shape({\n    Badge: PropTypes.elementType,\n    Root: PropTypes.elementType\n  }),\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * This prop is an alias for the `slotProps` prop.\n   * It's recommended to use the `slotProps` prop instead, as `componentsProps` will be deprecated in the future.\n   *\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    badge: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * If `true`, the badge is invisible.\n   * @default false\n   */\n  invisible: PropTypes.bool,\n  /**\n   * Max count to show.\n   * @default 99\n   */\n  max: PropTypes.number,\n  /**\n   * Wrapped shape the badge should overlap.\n   * @default 'rectangular'\n   */\n  overlap: PropTypes.oneOf(['circular', 'rectangular']),\n  /**\n   * Controls whether the badge is hidden when `badgeContent` is zero.\n   * @default false\n   */\n  showZero: PropTypes.bool,\n  /**\n   * The props used for each slot inside the Badge.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    badge: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside the Badge.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    badge: PropTypes.elementType,\n    root: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The variant to use.\n   * @default 'standard'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['dot', 'standard']), PropTypes.string])\n} : void 0;\nexport default Badge;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "React", "PropTypes", "clsx", "usePreviousProps", "unstable_composeClasses", "composeClasses", "useBadge", "useSlotProps", "styled", "useThemeProps", "capitalize", "badgeClasses", "getBadgeUtilityClass", "jsx", "_jsx", "jsxs", "_jsxs", "RADIUS_STANDARD", "RADIUS_DOT", "useUtilityClasses", "ownerState", "color", "anchor<PERSON><PERSON><PERSON>", "invisible", "overlap", "variant", "classes", "slots", "root", "badge", "vertical", "horizontal", "BadgeRoot", "name", "slot", "overridesResolver", "props", "styles", "position", "display", "verticalAlign", "flexShrink", "BadgeBadge", "theme", "flexDirection", "flexWrap", "justifyContent", "align<PERSON><PERSON><PERSON>", "alignItems", "boxSizing", "fontFamily", "typography", "fontWeight", "fontWeightMedium", "fontSize", "pxToRem", "min<PERSON><PERSON><PERSON>", "lineHeight", "padding", "height", "borderRadius", "zIndex", "transition", "transitions", "create", "easing", "easeInOut", "duration", "enteringScreen", "backgroundColor", "vars", "palette", "main", "contrastText", "top", "right", "transform", "transform<PERSON><PERSON>in", "bottom", "left", "leavingScreen", "Badge", "forwardRef", "inProps", "ref", "_ref", "_slots$root", "_ref2", "_slots$badge", "_slotProps$root", "_slotProps$badge", "anchorOriginProp", "className", "component", "components", "componentsProps", "children", "overlapProp", "colorProp", "invisibleProp", "max", "maxProp", "badgeContent", "badgeContentProp", "slotProps", "showZero", "variantProp", "other", "invisibleFromHook", "displayValue", "displayValueFromHook", "prevProps", "undefined", "RootSlot", "Root", "BadgeSlot", "rootSlotProps", "badgeSlotProps", "rootProps", "elementType", "externalSlotProps", "externalForwardedProps", "additionalProps", "as", "badgeProps", "process", "env", "NODE_ENV", "propTypes", "shape", "oneOf", "isRequired", "node", "object", "string", "oneOfType", "func", "bool", "number", "sx", "arrayOf"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/material/Badge/Badge.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"anchorOrigin\", \"className\", \"classes\", \"component\", \"components\", \"componentsProps\", \"children\", \"overlap\", \"color\", \"invisible\", \"max\", \"badgeContent\", \"slots\", \"slotProps\", \"showZero\", \"variant\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { usePreviousProps } from '@mui/utils';\nimport { unstable_composeClasses as composeClasses } from '@mui/base/composeClasses';\nimport { useBadge } from '@mui/base/useBadge';\nimport { useSlotProps } from '@mui/base';\nimport styled from '../styles/styled';\nimport useThemeProps from '../styles/useThemeProps';\nimport capitalize from '../utils/capitalize';\nimport badgeClasses, { getBadgeUtilityClass } from './badgeClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst RADIUS_STANDARD = 10;\nconst RADIUS_DOT = 4;\nconst useUtilityClasses = ownerState => {\n  const {\n    color,\n    anchorOrigin,\n    invisible,\n    overlap,\n    variant,\n    classes = {}\n  } = ownerState;\n  const slots = {\n    root: ['root'],\n    badge: ['badge', variant, invisible && 'invisible', `anchorOrigin${capitalize(anchorOrigin.vertical)}${capitalize(anchorOrigin.horizontal)}`, `anchorOrigin${capitalize(anchorOrigin.vertical)}${capitalize(anchorOrigin.horizontal)}${capitalize(overlap)}`, `overlap${capitalize(overlap)}`, color !== 'default' && `color${capitalize(color)}`]\n  };\n  return composeClasses(slots, getBadgeUtilityClass, classes);\n};\nconst BadgeRoot = styled('span', {\n  name: 'MuiBadge',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})({\n  position: 'relative',\n  display: 'inline-flex',\n  // For correct alignment with the text.\n  verticalAlign: 'middle',\n  flexShrink: 0\n});\nconst BadgeBadge = styled('span', {\n  name: 'MuiBadge',\n  slot: 'Badge',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.badge, styles[ownerState.variant], styles[`anchorOrigin${capitalize(ownerState.anchorOrigin.vertical)}${capitalize(ownerState.anchorOrigin.horizontal)}${capitalize(ownerState.overlap)}`], ownerState.color !== 'default' && styles[`color${capitalize(ownerState.color)}`], ownerState.invisible && styles.invisible];\n  }\n})(({\n  theme,\n  ownerState\n}) => _extends({\n  display: 'flex',\n  flexDirection: 'row',\n  flexWrap: 'wrap',\n  justifyContent: 'center',\n  alignContent: 'center',\n  alignItems: 'center',\n  position: 'absolute',\n  boxSizing: 'border-box',\n  fontFamily: theme.typography.fontFamily,\n  fontWeight: theme.typography.fontWeightMedium,\n  fontSize: theme.typography.pxToRem(12),\n  minWidth: RADIUS_STANDARD * 2,\n  lineHeight: 1,\n  padding: '0 6px',\n  height: RADIUS_STANDARD * 2,\n  borderRadius: RADIUS_STANDARD,\n  zIndex: 1,\n  // Render the badge on top of potential ripples.\n  transition: theme.transitions.create('transform', {\n    easing: theme.transitions.easing.easeInOut,\n    duration: theme.transitions.duration.enteringScreen\n  })\n}, ownerState.color !== 'default' && {\n  backgroundColor: (theme.vars || theme).palette[ownerState.color].main,\n  color: (theme.vars || theme).palette[ownerState.color].contrastText\n}, ownerState.variant === 'dot' && {\n  borderRadius: RADIUS_DOT,\n  height: RADIUS_DOT * 2,\n  minWidth: RADIUS_DOT * 2,\n  padding: 0\n}, ownerState.anchorOrigin.vertical === 'top' && ownerState.anchorOrigin.horizontal === 'right' && ownerState.overlap === 'rectangular' && {\n  top: 0,\n  right: 0,\n  transform: 'scale(1) translate(50%, -50%)',\n  transformOrigin: '100% 0%',\n  [`&.${badgeClasses.invisible}`]: {\n    transform: 'scale(0) translate(50%, -50%)'\n  }\n}, ownerState.anchorOrigin.vertical === 'bottom' && ownerState.anchorOrigin.horizontal === 'right' && ownerState.overlap === 'rectangular' && {\n  bottom: 0,\n  right: 0,\n  transform: 'scale(1) translate(50%, 50%)',\n  transformOrigin: '100% 100%',\n  [`&.${badgeClasses.invisible}`]: {\n    transform: 'scale(0) translate(50%, 50%)'\n  }\n}, ownerState.anchorOrigin.vertical === 'top' && ownerState.anchorOrigin.horizontal === 'left' && ownerState.overlap === 'rectangular' && {\n  top: 0,\n  left: 0,\n  transform: 'scale(1) translate(-50%, -50%)',\n  transformOrigin: '0% 0%',\n  [`&.${badgeClasses.invisible}`]: {\n    transform: 'scale(0) translate(-50%, -50%)'\n  }\n}, ownerState.anchorOrigin.vertical === 'bottom' && ownerState.anchorOrigin.horizontal === 'left' && ownerState.overlap === 'rectangular' && {\n  bottom: 0,\n  left: 0,\n  transform: 'scale(1) translate(-50%, 50%)',\n  transformOrigin: '0% 100%',\n  [`&.${badgeClasses.invisible}`]: {\n    transform: 'scale(0) translate(-50%, 50%)'\n  }\n}, ownerState.anchorOrigin.vertical === 'top' && ownerState.anchorOrigin.horizontal === 'right' && ownerState.overlap === 'circular' && {\n  top: '14%',\n  right: '14%',\n  transform: 'scale(1) translate(50%, -50%)',\n  transformOrigin: '100% 0%',\n  [`&.${badgeClasses.invisible}`]: {\n    transform: 'scale(0) translate(50%, -50%)'\n  }\n}, ownerState.anchorOrigin.vertical === 'bottom' && ownerState.anchorOrigin.horizontal === 'right' && ownerState.overlap === 'circular' && {\n  bottom: '14%',\n  right: '14%',\n  transform: 'scale(1) translate(50%, 50%)',\n  transformOrigin: '100% 100%',\n  [`&.${badgeClasses.invisible}`]: {\n    transform: 'scale(0) translate(50%, 50%)'\n  }\n}, ownerState.anchorOrigin.vertical === 'top' && ownerState.anchorOrigin.horizontal === 'left' && ownerState.overlap === 'circular' && {\n  top: '14%',\n  left: '14%',\n  transform: 'scale(1) translate(-50%, -50%)',\n  transformOrigin: '0% 0%',\n  [`&.${badgeClasses.invisible}`]: {\n    transform: 'scale(0) translate(-50%, -50%)'\n  }\n}, ownerState.anchorOrigin.vertical === 'bottom' && ownerState.anchorOrigin.horizontal === 'left' && ownerState.overlap === 'circular' && {\n  bottom: '14%',\n  left: '14%',\n  transform: 'scale(1) translate(-50%, 50%)',\n  transformOrigin: '0% 100%',\n  [`&.${badgeClasses.invisible}`]: {\n    transform: 'scale(0) translate(-50%, 50%)'\n  }\n}, ownerState.invisible && {\n  transition: theme.transitions.create('transform', {\n    easing: theme.transitions.easing.easeInOut,\n    duration: theme.transitions.duration.leavingScreen\n  })\n}));\nconst Badge = /*#__PURE__*/React.forwardRef(function Badge(inProps, ref) {\n  var _ref, _slots$root, _ref2, _slots$badge, _slotProps$root, _slotProps$badge;\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiBadge'\n  });\n  const {\n      anchorOrigin: anchorOriginProp = {\n        vertical: 'top',\n        horizontal: 'right'\n      },\n      className,\n      component,\n      components = {},\n      componentsProps = {},\n      children,\n      overlap: overlapProp = 'rectangular',\n      color: colorProp = 'default',\n      invisible: invisibleProp = false,\n      max: maxProp = 99,\n      badgeContent: badgeContentProp,\n      slots,\n      slotProps,\n      showZero = false,\n      variant: variantProp = 'standard'\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    badgeContent,\n    invisible: invisibleFromHook,\n    max,\n    displayValue: displayValueFromHook\n  } = useBadge({\n    max: maxProp,\n    invisible: invisibleProp,\n    badgeContent: badgeContentProp,\n    showZero\n  });\n  const prevProps = usePreviousProps({\n    anchorOrigin: anchorOriginProp,\n    color: colorProp,\n    overlap: overlapProp,\n    variant: variantProp,\n    badgeContent: badgeContentProp\n  });\n  const invisible = invisibleFromHook || badgeContent == null && variantProp !== 'dot';\n  const {\n    color = colorProp,\n    overlap = overlapProp,\n    anchorOrigin = anchorOriginProp,\n    variant = variantProp\n  } = invisible ? prevProps : props;\n  const displayValue = variant !== 'dot' ? displayValueFromHook : undefined;\n  const ownerState = _extends({}, props, {\n    badgeContent,\n    invisible,\n    max,\n    displayValue,\n    showZero,\n    anchorOrigin,\n    color,\n    overlap,\n    variant\n  });\n  const classes = useUtilityClasses(ownerState);\n\n  // support both `slots` and `components` for backward compatibility\n  const RootSlot = (_ref = (_slots$root = slots == null ? void 0 : slots.root) != null ? _slots$root : components.Root) != null ? _ref : BadgeRoot;\n  const BadgeSlot = (_ref2 = (_slots$badge = slots == null ? void 0 : slots.badge) != null ? _slots$badge : components.Badge) != null ? _ref2 : BadgeBadge;\n  const rootSlotProps = (_slotProps$root = slotProps == null ? void 0 : slotProps.root) != null ? _slotProps$root : componentsProps.root;\n  const badgeSlotProps = (_slotProps$badge = slotProps == null ? void 0 : slotProps.badge) != null ? _slotProps$badge : componentsProps.badge;\n  const rootProps = useSlotProps({\n    elementType: RootSlot,\n    externalSlotProps: rootSlotProps,\n    externalForwardedProps: other,\n    additionalProps: {\n      ref,\n      as: component\n    },\n    ownerState,\n    className: clsx(rootSlotProps == null ? void 0 : rootSlotProps.className, classes.root, className)\n  });\n  const badgeProps = useSlotProps({\n    elementType: BadgeSlot,\n    externalSlotProps: badgeSlotProps,\n    ownerState,\n    className: clsx(classes.badge, badgeSlotProps == null ? void 0 : badgeSlotProps.className)\n  });\n  return /*#__PURE__*/_jsxs(RootSlot, _extends({}, rootProps, {\n    children: [children, /*#__PURE__*/_jsx(BadgeSlot, _extends({}, badgeProps, {\n      children: displayValue\n    }))]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? Badge.propTypes /* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit the d.ts file and run \"yarn proptypes\"     |\n  // ----------------------------------------------------------------------\n  /**\n   * The anchor of the badge.\n   * @default {\n   *   vertical: 'top',\n   *   horizontal: 'right',\n   * }\n   */\n  anchorOrigin: PropTypes.shape({\n    horizontal: PropTypes.oneOf(['left', 'right']).isRequired,\n    vertical: PropTypes.oneOf(['bottom', 'top']).isRequired\n  }),\n  /**\n   * The content rendered within the badge.\n   */\n  badgeContent: PropTypes.node,\n  /**\n   * The badge will be added relative to this node.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The color of the component.\n   * It supports both default and custom theme colors, which can be added as shown in the\n   * [palette customization guide](https://mui.com/material-ui/customization/palette/#custom-colors).\n   * @default 'default'\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['default', 'primary', 'secondary', 'error', 'info', 'success', 'warning']), PropTypes.string]),\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The components used for each slot inside.\n   *\n   * This prop is an alias for the `slots` prop.\n   * It's recommended to use the `slots` prop instead.\n   *\n   * @default {}\n   */\n  components: PropTypes.shape({\n    Badge: PropTypes.elementType,\n    Root: PropTypes.elementType\n  }),\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * This prop is an alias for the `slotProps` prop.\n   * It's recommended to use the `slotProps` prop instead, as `componentsProps` will be deprecated in the future.\n   *\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    badge: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * If `true`, the badge is invisible.\n   * @default false\n   */\n  invisible: PropTypes.bool,\n  /**\n   * Max count to show.\n   * @default 99\n   */\n  max: PropTypes.number,\n  /**\n   * Wrapped shape the badge should overlap.\n   * @default 'rectangular'\n   */\n  overlap: PropTypes.oneOf(['circular', 'rectangular']),\n  /**\n   * Controls whether the badge is hidden when `badgeContent` is zero.\n   * @default false\n   */\n  showZero: PropTypes.bool,\n  /**\n   * The props used for each slot inside the Badge.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    badge: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside the Badge.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    badge: PropTypes.elementType,\n    root: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The variant to use.\n   * @default 'standard'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['dot', 'standard']), PropTypes.string])\n} : void 0;\nexport default Badge;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,cAAc,EAAE,WAAW,EAAE,SAAS,EAAE,WAAW,EAAE,YAAY,EAAE,iBAAiB,EAAE,UAAU,EAAE,SAAS,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,cAAc,EAAE,OAAO,EAAE,WAAW,EAAE,UAAU,EAAE,SAAS,CAAC;AACzN,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,gBAAgB,QAAQ,YAAY;AAC7C,SAASC,uBAAuB,IAAIC,cAAc,QAAQ,0BAA0B;AACpF,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,SAASC,YAAY,QAAQ,WAAW;AACxC,OAAOC,MAAM,MAAM,kBAAkB;AACrC,OAAOC,aAAa,MAAM,yBAAyB;AACnD,OAAOC,UAAU,MAAM,qBAAqB;AAC5C,OAAOC,YAAY,IAAIC,oBAAoB,QAAQ,gBAAgB;AACnE,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,MAAMC,eAAe,GAAG,EAAE;AAC1B,MAAMC,UAAU,GAAG,CAAC;AACpB,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,KAAK;IACLC,YAAY;IACZC,SAAS;IACTC,OAAO;IACPC,OAAO;IACPC,OAAO,GAAG,CAAC;EACb,CAAC,GAAGN,UAAU;EACd,MAAMO,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,CAAC;IACdC,KAAK,EAAE,CAAC,OAAO,EAAEJ,OAAO,EAAEF,SAAS,IAAI,WAAW,EAAG,eAAcb,UAAU,CAACY,YAAY,CAACQ,QAAQ,CAAE,GAAEpB,UAAU,CAACY,YAAY,CAACS,UAAU,CAAE,EAAC,EAAG,eAAcrB,UAAU,CAACY,YAAY,CAACQ,QAAQ,CAAE,GAAEpB,UAAU,CAACY,YAAY,CAACS,UAAU,CAAE,GAAErB,UAAU,CAACc,OAAO,CAAE,EAAC,EAAG,UAASd,UAAU,CAACc,OAAO,CAAE,EAAC,EAAEH,KAAK,KAAK,SAAS,IAAK,QAAOX,UAAU,CAACW,KAAK,CAAE,EAAC;EACnV,CAAC;EACD,OAAOhB,cAAc,CAACsB,KAAK,EAAEf,oBAAoB,EAAEc,OAAO,CAAC;AAC7D,CAAC;AACD,MAAMM,SAAS,GAAGxB,MAAM,CAAC,MAAM,EAAE;EAC/ByB,IAAI,EAAE,UAAU;EAChBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACT;AAC/C,CAAC,CAAC,CAAC;EACDU,QAAQ,EAAE,UAAU;EACpBC,OAAO,EAAE,aAAa;EACtB;EACAC,aAAa,EAAE,QAAQ;EACvBC,UAAU,EAAE;AACd,CAAC,CAAC;AACF,MAAMC,UAAU,GAAGlC,MAAM,CAAC,MAAM,EAAE;EAChCyB,IAAI,EAAE,UAAU;EAChBC,IAAI,EAAE,OAAO;EACbC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJjB;IACF,CAAC,GAAGgB,KAAK;IACT,OAAO,CAACC,MAAM,CAACR,KAAK,EAAEQ,MAAM,CAACjB,UAAU,CAACK,OAAO,CAAC,EAAEY,MAAM,CAAE,eAAc3B,UAAU,CAACU,UAAU,CAACE,YAAY,CAACQ,QAAQ,CAAE,GAAEpB,UAAU,CAACU,UAAU,CAACE,YAAY,CAACS,UAAU,CAAE,GAAErB,UAAU,CAACU,UAAU,CAACI,OAAO,CAAE,EAAC,CAAC,EAAEJ,UAAU,CAACC,KAAK,KAAK,SAAS,IAAIgB,MAAM,CAAE,QAAO3B,UAAU,CAACU,UAAU,CAACC,KAAK,CAAE,EAAC,CAAC,EAAED,UAAU,CAACG,SAAS,IAAIc,MAAM,CAACd,SAAS,CAAC;EACxU;AACF,CAAC,CAAC,CAAC,CAAC;EACFoB,KAAK;EACLvB;AACF,CAAC,KAAKtB,QAAQ,CAAC;EACbyC,OAAO,EAAE,MAAM;EACfK,aAAa,EAAE,KAAK;EACpBC,QAAQ,EAAE,MAAM;EAChBC,cAAc,EAAE,QAAQ;EACxBC,YAAY,EAAE,QAAQ;EACtBC,UAAU,EAAE,QAAQ;EACpBV,QAAQ,EAAE,UAAU;EACpBW,SAAS,EAAE,YAAY;EACvBC,UAAU,EAAEP,KAAK,CAACQ,UAAU,CAACD,UAAU;EACvCE,UAAU,EAAET,KAAK,CAACQ,UAAU,CAACE,gBAAgB;EAC7CC,QAAQ,EAAEX,KAAK,CAACQ,UAAU,CAACI,OAAO,CAAC,EAAE,CAAC;EACtCC,QAAQ,EAAEvC,eAAe,GAAG,CAAC;EAC7BwC,UAAU,EAAE,CAAC;EACbC,OAAO,EAAE,OAAO;EAChBC,MAAM,EAAE1C,eAAe,GAAG,CAAC;EAC3B2C,YAAY,EAAE3C,eAAe;EAC7B4C,MAAM,EAAE,CAAC;EACT;EACAC,UAAU,EAAEnB,KAAK,CAACoB,WAAW,CAACC,MAAM,CAAC,WAAW,EAAE;IAChDC,MAAM,EAAEtB,KAAK,CAACoB,WAAW,CAACE,MAAM,CAACC,SAAS;IAC1CC,QAAQ,EAAExB,KAAK,CAACoB,WAAW,CAACI,QAAQ,CAACC;EACvC,CAAC;AACH,CAAC,EAAEhD,UAAU,CAACC,KAAK,KAAK,SAAS,IAAI;EACnCgD,eAAe,EAAE,CAAC1B,KAAK,CAAC2B,IAAI,IAAI3B,KAAK,EAAE4B,OAAO,CAACnD,UAAU,CAACC,KAAK,CAAC,CAACmD,IAAI;EACrEnD,KAAK,EAAE,CAACsB,KAAK,CAAC2B,IAAI,IAAI3B,KAAK,EAAE4B,OAAO,CAACnD,UAAU,CAACC,KAAK,CAAC,CAACoD;AACzD,CAAC,EAAErD,UAAU,CAACK,OAAO,KAAK,KAAK,IAAI;EACjCmC,YAAY,EAAE1C,UAAU;EACxByC,MAAM,EAAEzC,UAAU,GAAG,CAAC;EACtBsC,QAAQ,EAAEtC,UAAU,GAAG,CAAC;EACxBwC,OAAO,EAAE;AACX,CAAC,EAAEtC,UAAU,CAACE,YAAY,CAACQ,QAAQ,KAAK,KAAK,IAAIV,UAAU,CAACE,YAAY,CAACS,UAAU,KAAK,OAAO,IAAIX,UAAU,CAACI,OAAO,KAAK,aAAa,IAAI;EACzIkD,GAAG,EAAE,CAAC;EACNC,KAAK,EAAE,CAAC;EACRC,SAAS,EAAE,+BAA+B;EAC1CC,eAAe,EAAE,SAAS;EAC1B,CAAE,KAAIlE,YAAY,CAACY,SAAU,EAAC,GAAG;IAC/BqD,SAAS,EAAE;EACb;AACF,CAAC,EAAExD,UAAU,CAACE,YAAY,CAACQ,QAAQ,KAAK,QAAQ,IAAIV,UAAU,CAACE,YAAY,CAACS,UAAU,KAAK,OAAO,IAAIX,UAAU,CAACI,OAAO,KAAK,aAAa,IAAI;EAC5IsD,MAAM,EAAE,CAAC;EACTH,KAAK,EAAE,CAAC;EACRC,SAAS,EAAE,8BAA8B;EACzCC,eAAe,EAAE,WAAW;EAC5B,CAAE,KAAIlE,YAAY,CAACY,SAAU,EAAC,GAAG;IAC/BqD,SAAS,EAAE;EACb;AACF,CAAC,EAAExD,UAAU,CAACE,YAAY,CAACQ,QAAQ,KAAK,KAAK,IAAIV,UAAU,CAACE,YAAY,CAACS,UAAU,KAAK,MAAM,IAAIX,UAAU,CAACI,OAAO,KAAK,aAAa,IAAI;EACxIkD,GAAG,EAAE,CAAC;EACNK,IAAI,EAAE,CAAC;EACPH,SAAS,EAAE,gCAAgC;EAC3CC,eAAe,EAAE,OAAO;EACxB,CAAE,KAAIlE,YAAY,CAACY,SAAU,EAAC,GAAG;IAC/BqD,SAAS,EAAE;EACb;AACF,CAAC,EAAExD,UAAU,CAACE,YAAY,CAACQ,QAAQ,KAAK,QAAQ,IAAIV,UAAU,CAACE,YAAY,CAACS,UAAU,KAAK,MAAM,IAAIX,UAAU,CAACI,OAAO,KAAK,aAAa,IAAI;EAC3IsD,MAAM,EAAE,CAAC;EACTC,IAAI,EAAE,CAAC;EACPH,SAAS,EAAE,+BAA+B;EAC1CC,eAAe,EAAE,SAAS;EAC1B,CAAE,KAAIlE,YAAY,CAACY,SAAU,EAAC,GAAG;IAC/BqD,SAAS,EAAE;EACb;AACF,CAAC,EAAExD,UAAU,CAACE,YAAY,CAACQ,QAAQ,KAAK,KAAK,IAAIV,UAAU,CAACE,YAAY,CAACS,UAAU,KAAK,OAAO,IAAIX,UAAU,CAACI,OAAO,KAAK,UAAU,IAAI;EACtIkD,GAAG,EAAE,KAAK;EACVC,KAAK,EAAE,KAAK;EACZC,SAAS,EAAE,+BAA+B;EAC1CC,eAAe,EAAE,SAAS;EAC1B,CAAE,KAAIlE,YAAY,CAACY,SAAU,EAAC,GAAG;IAC/BqD,SAAS,EAAE;EACb;AACF,CAAC,EAAExD,UAAU,CAACE,YAAY,CAACQ,QAAQ,KAAK,QAAQ,IAAIV,UAAU,CAACE,YAAY,CAACS,UAAU,KAAK,OAAO,IAAIX,UAAU,CAACI,OAAO,KAAK,UAAU,IAAI;EACzIsD,MAAM,EAAE,KAAK;EACbH,KAAK,EAAE,KAAK;EACZC,SAAS,EAAE,8BAA8B;EACzCC,eAAe,EAAE,WAAW;EAC5B,CAAE,KAAIlE,YAAY,CAACY,SAAU,EAAC,GAAG;IAC/BqD,SAAS,EAAE;EACb;AACF,CAAC,EAAExD,UAAU,CAACE,YAAY,CAACQ,QAAQ,KAAK,KAAK,IAAIV,UAAU,CAACE,YAAY,CAACS,UAAU,KAAK,MAAM,IAAIX,UAAU,CAACI,OAAO,KAAK,UAAU,IAAI;EACrIkD,GAAG,EAAE,KAAK;EACVK,IAAI,EAAE,KAAK;EACXH,SAAS,EAAE,gCAAgC;EAC3CC,eAAe,EAAE,OAAO;EACxB,CAAE,KAAIlE,YAAY,CAACY,SAAU,EAAC,GAAG;IAC/BqD,SAAS,EAAE;EACb;AACF,CAAC,EAAExD,UAAU,CAACE,YAAY,CAACQ,QAAQ,KAAK,QAAQ,IAAIV,UAAU,CAACE,YAAY,CAACS,UAAU,KAAK,MAAM,IAAIX,UAAU,CAACI,OAAO,KAAK,UAAU,IAAI;EACxIsD,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,KAAK;EACXH,SAAS,EAAE,+BAA+B;EAC1CC,eAAe,EAAE,SAAS;EAC1B,CAAE,KAAIlE,YAAY,CAACY,SAAU,EAAC,GAAG;IAC/BqD,SAAS,EAAE;EACb;AACF,CAAC,EAAExD,UAAU,CAACG,SAAS,IAAI;EACzBuC,UAAU,EAAEnB,KAAK,CAACoB,WAAW,CAACC,MAAM,CAAC,WAAW,EAAE;IAChDC,MAAM,EAAEtB,KAAK,CAACoB,WAAW,CAACE,MAAM,CAACC,SAAS;IAC1CC,QAAQ,EAAExB,KAAK,CAACoB,WAAW,CAACI,QAAQ,CAACa;EACvC,CAAC;AACH,CAAC,CAAC,CAAC;AACH,MAAMC,KAAK,GAAG,aAAajF,KAAK,CAACkF,UAAU,CAAC,SAASD,KAAKA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACvE,IAAIC,IAAI,EAAEC,WAAW,EAAEC,KAAK,EAAEC,YAAY,EAAEC,eAAe,EAAEC,gBAAgB;EAC7E,MAAMtD,KAAK,GAAG3B,aAAa,CAAC;IAC1B2B,KAAK,EAAE+C,OAAO;IACdlD,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFX,YAAY,EAAEqE,gBAAgB,GAAG;QAC/B7D,QAAQ,EAAE,KAAK;QACfC,UAAU,EAAE;MACd,CAAC;MACD6D,SAAS;MACTC,SAAS;MACTC,UAAU,GAAG,CAAC,CAAC;MACfC,eAAe,GAAG,CAAC,CAAC;MACpBC,QAAQ;MACRxE,OAAO,EAAEyE,WAAW,GAAG,aAAa;MACpC5E,KAAK,EAAE6E,SAAS,GAAG,SAAS;MAC5B3E,SAAS,EAAE4E,aAAa,GAAG,KAAK;MAChCC,GAAG,EAAEC,OAAO,GAAG,EAAE;MACjBC,YAAY,EAAEC,gBAAgB;MAC9B5E,KAAK;MACL6E,SAAS;MACTC,QAAQ,GAAG,KAAK;MAChBhF,OAAO,EAAEiF,WAAW,GAAG;IACzB,CAAC,GAAGtE,KAAK;IACTuE,KAAK,GAAG9G,6BAA6B,CAACuC,KAAK,EAAErC,SAAS,CAAC;EACzD,MAAM;IACJuG,YAAY;IACZ/E,SAAS,EAAEqF,iBAAiB;IAC5BR,GAAG;IACHS,YAAY,EAAEC;EAChB,CAAC,GAAGxG,QAAQ,CAAC;IACX8F,GAAG,EAAEC,OAAO;IACZ9E,SAAS,EAAE4E,aAAa;IACxBG,YAAY,EAAEC,gBAAgB;IAC9BE;EACF,CAAC,CAAC;EACF,MAAMM,SAAS,GAAG5G,gBAAgB,CAAC;IACjCmB,YAAY,EAAEqE,gBAAgB;IAC9BtE,KAAK,EAAE6E,SAAS;IAChB1E,OAAO,EAAEyE,WAAW;IACpBxE,OAAO,EAAEiF,WAAW;IACpBJ,YAAY,EAAEC;EAChB,CAAC,CAAC;EACF,MAAMhF,SAAS,GAAGqF,iBAAiB,IAAIN,YAAY,IAAI,IAAI,IAAII,WAAW,KAAK,KAAK;EACpF,MAAM;IACJrF,KAAK,GAAG6E,SAAS;IACjB1E,OAAO,GAAGyE,WAAW;IACrB3E,YAAY,GAAGqE,gBAAgB;IAC/BlE,OAAO,GAAGiF;EACZ,CAAC,GAAGnF,SAAS,GAAGwF,SAAS,GAAG3E,KAAK;EACjC,MAAMyE,YAAY,GAAGpF,OAAO,KAAK,KAAK,GAAGqF,oBAAoB,GAAGE,SAAS;EACzE,MAAM5F,UAAU,GAAGtB,QAAQ,CAAC,CAAC,CAAC,EAAEsC,KAAK,EAAE;IACrCkE,YAAY;IACZ/E,SAAS;IACT6E,GAAG;IACHS,YAAY;IACZJ,QAAQ;IACRnF,YAAY;IACZD,KAAK;IACLG,OAAO;IACPC;EACF,CAAC,CAAC;EACF,MAAMC,OAAO,GAAGP,iBAAiB,CAACC,UAAU,CAAC;;EAE7C;EACA,MAAM6F,QAAQ,GAAG,CAAC5B,IAAI,GAAG,CAACC,WAAW,GAAG3D,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACC,IAAI,KAAK,IAAI,GAAG0D,WAAW,GAAGQ,UAAU,CAACoB,IAAI,KAAK,IAAI,GAAG7B,IAAI,GAAGrD,SAAS;EAChJ,MAAMmF,SAAS,GAAG,CAAC5B,KAAK,GAAG,CAACC,YAAY,GAAG7D,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACE,KAAK,KAAK,IAAI,GAAG2D,YAAY,GAAGM,UAAU,CAACb,KAAK,KAAK,IAAI,GAAGM,KAAK,GAAG7C,UAAU;EACxJ,MAAM0E,aAAa,GAAG,CAAC3B,eAAe,GAAGe,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,SAAS,CAAC5E,IAAI,KAAK,IAAI,GAAG6D,eAAe,GAAGM,eAAe,CAACnE,IAAI;EACtI,MAAMyF,cAAc,GAAG,CAAC3B,gBAAgB,GAAGc,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,SAAS,CAAC3E,KAAK,KAAK,IAAI,GAAG6D,gBAAgB,GAAGK,eAAe,CAAClE,KAAK;EAC3I,MAAMyF,SAAS,GAAG/G,YAAY,CAAC;IAC7BgH,WAAW,EAAEN,QAAQ;IACrBO,iBAAiB,EAAEJ,aAAa;IAChCK,sBAAsB,EAAEd,KAAK;IAC7Be,eAAe,EAAE;MACftC,GAAG;MACHuC,EAAE,EAAE9B;IACN,CAAC;IACDzE,UAAU;IACVwE,SAAS,EAAE1F,IAAI,CAACkH,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACxB,SAAS,EAAElE,OAAO,CAACE,IAAI,EAAEgE,SAAS;EACnG,CAAC,CAAC;EACF,MAAMgC,UAAU,GAAGrH,YAAY,CAAC;IAC9BgH,WAAW,EAAEJ,SAAS;IACtBK,iBAAiB,EAAEH,cAAc;IACjCjG,UAAU;IACVwE,SAAS,EAAE1F,IAAI,CAACwB,OAAO,CAACG,KAAK,EAAEwF,cAAc,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACzB,SAAS;EAC3F,CAAC,CAAC;EACF,OAAO,aAAa5E,KAAK,CAACiG,QAAQ,EAAEnH,QAAQ,CAAC,CAAC,CAAC,EAAEwH,SAAS,EAAE;IAC1DtB,QAAQ,EAAE,CAACA,QAAQ,EAAE,aAAalF,IAAI,CAACqG,SAAS,EAAErH,QAAQ,CAAC,CAAC,CAAC,EAAE8H,UAAU,EAAE;MACzE5B,QAAQ,EAAEa;IACZ,CAAC,CAAC,CAAC;EACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFgB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG9C,KAAK,CAAC+C,SAAS,CAAC,yBAAyB;EAC/E;EACA;EACA;EACA;EACA;AACF;AACA;AACA;AACA;AACA;AACA;EACE1G,YAAY,EAAErB,SAAS,CAACgI,KAAK,CAAC;IAC5BlG,UAAU,EAAE9B,SAAS,CAACiI,KAAK,CAAC,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,CAACC,UAAU;IACzDrG,QAAQ,EAAE7B,SAAS,CAACiI,KAAK,CAAC,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,CAACC;EAC/C,CAAC,CAAC;EACF;AACF;AACA;EACE7B,YAAY,EAAErG,SAAS,CAACmI,IAAI;EAC5B;AACF;AACA;EACEpC,QAAQ,EAAE/F,SAAS,CAACmI,IAAI;EACxB;AACF;AACA;EACE1G,OAAO,EAAEzB,SAAS,CAACoI,MAAM;EACzB;AACF;AACA;EACEzC,SAAS,EAAE3F,SAAS,CAACqI,MAAM;EAC3B;AACF;AACA;AACA;AACA;AACA;EACEjH,KAAK,EAAEpB,SAAS,CAAC,sCAAsCsI,SAAS,CAAC,CAACtI,SAAS,CAACiI,KAAK,CAAC,CAAC,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,EAAEjI,SAAS,CAACqI,MAAM,CAAC,CAAC;EACjL;AACF;AACA;AACA;EACEzC,SAAS,EAAE5F,SAAS,CAACsH,WAAW;EAChC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEzB,UAAU,EAAE7F,SAAS,CAACgI,KAAK,CAAC;IAC1BhD,KAAK,EAAEhF,SAAS,CAACsH,WAAW;IAC5BL,IAAI,EAAEjH,SAAS,CAACsH;EAClB,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACExB,eAAe,EAAE9F,SAAS,CAACgI,KAAK,CAAC;IAC/BpG,KAAK,EAAE5B,SAAS,CAACsI,SAAS,CAAC,CAACtI,SAAS,CAACuI,IAAI,EAAEvI,SAAS,CAACoI,MAAM,CAAC,CAAC;IAC9DzG,IAAI,EAAE3B,SAAS,CAACsI,SAAS,CAAC,CAACtI,SAAS,CAACuI,IAAI,EAAEvI,SAAS,CAACoI,MAAM,CAAC;EAC9D,CAAC,CAAC;EACF;AACF;AACA;AACA;EACE9G,SAAS,EAAEtB,SAAS,CAACwI,IAAI;EACzB;AACF;AACA;AACA;EACErC,GAAG,EAAEnG,SAAS,CAACyI,MAAM;EACrB;AACF;AACA;AACA;EACElH,OAAO,EAAEvB,SAAS,CAACiI,KAAK,CAAC,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC;EACrD;AACF;AACA;AACA;EACEzB,QAAQ,EAAExG,SAAS,CAACwI,IAAI;EACxB;AACF;AACA;AACA;EACEjC,SAAS,EAAEvG,SAAS,CAACgI,KAAK,CAAC;IACzBpG,KAAK,EAAE5B,SAAS,CAACsI,SAAS,CAAC,CAACtI,SAAS,CAACuI,IAAI,EAAEvI,SAAS,CAACoI,MAAM,CAAC,CAAC;IAC9DzG,IAAI,EAAE3B,SAAS,CAACsI,SAAS,CAAC,CAACtI,SAAS,CAACuI,IAAI,EAAEvI,SAAS,CAACoI,MAAM,CAAC;EAC9D,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;EACE1G,KAAK,EAAE1B,SAAS,CAACgI,KAAK,CAAC;IACrBpG,KAAK,EAAE5B,SAAS,CAACsH,WAAW;IAC5B3F,IAAI,EAAE3B,SAAS,CAACsH;EAClB,CAAC,CAAC;EACF;AACF;AACA;EACEoB,EAAE,EAAE1I,SAAS,CAACsI,SAAS,CAAC,CAACtI,SAAS,CAAC2I,OAAO,CAAC3I,SAAS,CAACsI,SAAS,CAAC,CAACtI,SAAS,CAACuI,IAAI,EAAEvI,SAAS,CAACoI,MAAM,EAAEpI,SAAS,CAACwI,IAAI,CAAC,CAAC,CAAC,EAAExI,SAAS,CAACuI,IAAI,EAAEvI,SAAS,CAACoI,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;AACA;EACE5G,OAAO,EAAExB,SAAS,CAAC,sCAAsCsI,SAAS,CAAC,CAACtI,SAAS,CAACiI,KAAK,CAAC,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC,EAAEjI,SAAS,CAACqI,MAAM,CAAC;AAC7H,CAAC,GAAG,KAAK,CAAC;AACV,eAAerD,KAAK"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}