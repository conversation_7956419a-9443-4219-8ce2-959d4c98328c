{"ast": null, "code": "export { FormControl } from './FormControl';\nexport { FormControlContext } from './FormControlContext';\nexport * from './formControlClasses';\nexport { useFormControlContext } from './useFormControlContext';", "map": {"version": 3, "names": ["FormControl", "FormControlContext", "useFormControlContext"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/base/FormControl/index.js"], "sourcesContent": ["export { FormControl } from './FormControl';\nexport { FormControlContext } from './FormControlContext';\nexport * from './formControlClasses';\nexport { useFormControlContext } from './useFormControlContext';"], "mappings": "AAAA,SAASA,WAAW,QAAQ,eAAe;AAC3C,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,cAAc,sBAAsB;AACpC,SAASC,qBAAqB,QAAQ,yBAAyB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}