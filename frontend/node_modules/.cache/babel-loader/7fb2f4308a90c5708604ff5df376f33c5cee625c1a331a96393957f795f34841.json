{"ast": null, "code": "export { default } from './styleFunctionSx';\nexport { unstable_createStyleFunctionSx } from './styleFunctionSx';\nexport { default as extendSxProp } from './extendSxProp';\nexport { default as unstable_defaultSxConfig } from './defaultSxConfig';", "map": {"version": 3, "names": ["default", "unstable_createStyleFunctionSx", "extendSxProp", "unstable_defaultSxConfig"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/system/esm/styleFunctionSx/index.js"], "sourcesContent": ["export { default } from './styleFunctionSx';\nexport { unstable_createStyleFunctionSx } from './styleFunctionSx';\nexport { default as extendSxProp } from './extendSxProp';\nexport { default as unstable_defaultSxConfig } from './defaultSxConfig';"], "mappings": "AAAA,SAASA,OAAO,QAAQ,mBAAmB;AAC3C,SAASC,8BAA8B,QAAQ,mBAAmB;AAClE,SAASD,OAAO,IAAIE,YAAY,QAAQ,gBAAgB;AACxD,SAASF,OAAO,IAAIG,wBAAwB,QAAQ,mBAAmB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}