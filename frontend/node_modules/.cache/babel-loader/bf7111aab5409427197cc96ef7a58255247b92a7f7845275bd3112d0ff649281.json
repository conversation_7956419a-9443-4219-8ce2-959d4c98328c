{"ast": null, "code": "'use client';\n\nexport { MenuButton } from './MenuButton';\nexport * from './MenuButton.types';\nexport * from './menuButtonClasses';", "map": {"version": 3, "names": ["MenuButton"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/base/MenuButton/index.js"], "sourcesContent": ["'use client';\n\nexport { MenuButton } from './MenuButton';\nexport * from './MenuButton.types';\nexport * from './menuButtonClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,UAAU,QAAQ,cAAc;AACzC,cAAc,oBAAoB;AAClC,cAAc,qBAAqB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}