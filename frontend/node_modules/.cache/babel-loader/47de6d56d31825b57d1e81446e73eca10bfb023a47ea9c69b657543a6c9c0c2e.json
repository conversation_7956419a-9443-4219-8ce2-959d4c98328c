{"ast": null, "code": "'use client';\n\nexport { Slider } from './Slider';\nexport * from './Slider.types';\nexport * from './sliderClasses';", "map": {"version": 3, "names": ["Slide<PERSON>"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/base/Slider/index.js"], "sourcesContent": ["'use client';\n\nexport { Slider } from './Slider';\nexport * from './Slider.types';\nexport * from './sliderClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,MAAM,QAAQ,UAAU;AACjC,cAAc,gBAAgB;AAC9B,cAAc,iBAAiB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}