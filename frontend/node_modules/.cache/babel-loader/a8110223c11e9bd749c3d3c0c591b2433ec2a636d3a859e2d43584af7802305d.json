{"ast": null, "code": "import { unstable_generateUtilityClasses as generateUtilityClasses } from '@mui/utils';\nimport generateUtilityClass from '../generateUtilityClass';\nexport function getSnackbarUtilityClass(slot) {\n  return generateUtilityClass('MuiSnackbar', slot);\n}\nconst snackbarClasses = generateUtilityClasses('MuiSnackbar', ['root', 'anchorOriginTopCenter', 'anchorOriginBottomCenter', 'anchorOriginTopRight', 'anchorOriginBottomRight', 'anchorOriginTopLeft', 'anchorOriginBottomLeft']);\nexport default snackbarClasses;", "map": {"version": 3, "names": ["unstable_generateUtilityClasses", "generateUtilityClasses", "generateUtilityClass", "getSnackbarUtilityClass", "slot", "snackbarClasses"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/material/Snackbar/snackbarClasses.js"], "sourcesContent": ["import { unstable_generateUtilityClasses as generateUtilityClasses } from '@mui/utils';\nimport generateUtilityClass from '../generateUtilityClass';\nexport function getSnackbarUtilityClass(slot) {\n  return generateUtilityClass('MuiSnackbar', slot);\n}\nconst snackbarClasses = generateUtilityClasses('MuiSnackbar', ['root', 'anchorOriginTopCenter', 'anchorOriginBottomCenter', 'anchorOriginTopRight', 'anchorOriginBottomRight', 'anchorOriginTopLeft', 'anchorOriginBottomLeft']);\nexport default snackbarClasses;"], "mappings": "AAAA,SAASA,+BAA+B,IAAIC,sBAAsB,QAAQ,YAAY;AACtF,OAAOC,oBAAoB,MAAM,yBAAyB;AAC1D,OAAO,SAASC,uBAAuBA,CAACC,IAAI,EAAE;EAC5C,OAAOF,oBAAoB,CAAC,aAAa,EAAEE,IAAI,CAAC;AAClD;AACA,MAAMC,eAAe,GAAGJ,sBAAsB,CAAC,aAAa,EAAE,CAAC,MAAM,EAAE,uBAAuB,EAAE,0BAA0B,EAAE,sBAAsB,EAAE,yBAAyB,EAAE,qBAAqB,EAAE,wBAAwB,CAAC,CAAC;AAChO,eAAeI,eAAe"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}