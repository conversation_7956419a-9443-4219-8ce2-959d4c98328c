{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"action\", \"children\", \"className\", \"closeText\", \"color\", \"components\", \"componentsProps\", \"icon\", \"iconMapping\", \"onClose\", \"role\", \"severity\", \"slotProps\", \"slots\", \"variant\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { unstable_composeClasses as composeClasses } from '@mui/base/composeClasses';\nimport { darken, lighten } from '@mui/system';\nimport styled from '../styles/styled';\nimport useThemeProps from '../styles/useThemeProps';\nimport capitalize from '../utils/capitalize';\nimport Paper from '../Paper';\nimport alertClasses, { getAlertUtilityClass } from './alertClasses';\nimport IconButton from '../IconButton';\nimport SuccessOutlinedIcon from '../internal/svg-icons/SuccessOutlined';\nimport ReportProblemOutlinedIcon from '../internal/svg-icons/ReportProblemOutlined';\nimport ErrorOutlineIcon from '../internal/svg-icons/ErrorOutline';\nimport InfoOutlinedIcon from '../internal/svg-icons/InfoOutlined';\nimport CloseIcon from '../internal/svg-icons/Close';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    variant,\n    color,\n    severity,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', `${variant}${capitalize(color || severity)}`, `${variant}`],\n    icon: ['icon'],\n    message: ['message'],\n    action: ['action']\n  };\n  return composeClasses(slots, getAlertUtilityClass, classes);\n};\nconst AlertRoot = styled(Paper, {\n  name: 'MuiAlert',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[ownerState.variant], styles[`${ownerState.variant}${capitalize(ownerState.color || ownerState.severity)}`]];\n  }\n})(({\n  theme,\n  ownerState\n}) => {\n  const getColor = theme.palette.mode === 'light' ? darken : lighten;\n  const getBackgroundColor = theme.palette.mode === 'light' ? lighten : darken;\n  const color = ownerState.color || ownerState.severity;\n  return _extends({}, theme.typography.body2, {\n    backgroundColor: 'transparent',\n    display: 'flex',\n    padding: '6px 16px'\n  }, color && ownerState.variant === 'standard' && {\n    color: theme.vars ? theme.vars.palette.Alert[`${color}Color`] : getColor(theme.palette[color].light, 0.6),\n    backgroundColor: theme.vars ? theme.vars.palette.Alert[`${color}StandardBg`] : getBackgroundColor(theme.palette[color].light, 0.9),\n    [`& .${alertClasses.icon}`]: theme.vars ? {\n      color: theme.vars.palette.Alert[`${color}IconColor`]\n    } : {\n      color: theme.palette[color].main\n    }\n  }, color && ownerState.variant === 'outlined' && {\n    color: theme.vars ? theme.vars.palette.Alert[`${color}Color`] : getColor(theme.palette[color].light, 0.6),\n    border: `1px solid ${(theme.vars || theme).palette[color].light}`,\n    [`& .${alertClasses.icon}`]: theme.vars ? {\n      color: theme.vars.palette.Alert[`${color}IconColor`]\n    } : {\n      color: theme.palette[color].main\n    }\n  }, color && ownerState.variant === 'filled' && _extends({\n    fontWeight: theme.typography.fontWeightMedium\n  }, theme.vars ? {\n    color: theme.vars.palette.Alert[`${color}FilledColor`],\n    backgroundColor: theme.vars.palette.Alert[`${color}FilledBg`]\n  } : {\n    backgroundColor: theme.palette.mode === 'dark' ? theme.palette[color].dark : theme.palette[color].main,\n    color: theme.palette.getContrastText(theme.palette[color].main)\n  }));\n});\nconst AlertIcon = styled('div', {\n  name: 'MuiAlert',\n  slot: 'Icon',\n  overridesResolver: (props, styles) => styles.icon\n})({\n  marginRight: 12,\n  padding: '7px 0',\n  display: 'flex',\n  fontSize: 22,\n  opacity: 0.9\n});\nconst AlertMessage = styled('div', {\n  name: 'MuiAlert',\n  slot: 'Message',\n  overridesResolver: (props, styles) => styles.message\n})({\n  padding: '8px 0',\n  minWidth: 0,\n  overflow: 'auto'\n});\nconst AlertAction = styled('div', {\n  name: 'MuiAlert',\n  slot: 'Action',\n  overridesResolver: (props, styles) => styles.action\n})({\n  display: 'flex',\n  alignItems: 'flex-start',\n  padding: '4px 0 0 16px',\n  marginLeft: 'auto',\n  marginRight: -8\n});\nconst defaultIconMapping = {\n  success: /*#__PURE__*/_jsx(SuccessOutlinedIcon, {\n    fontSize: \"inherit\"\n  }),\n  warning: /*#__PURE__*/_jsx(ReportProblemOutlinedIcon, {\n    fontSize: \"inherit\"\n  }),\n  error: /*#__PURE__*/_jsx(ErrorOutlineIcon, {\n    fontSize: \"inherit\"\n  }),\n  info: /*#__PURE__*/_jsx(InfoOutlinedIcon, {\n    fontSize: \"inherit\"\n  })\n};\nconst Alert = /*#__PURE__*/React.forwardRef(function Alert(inProps, ref) {\n  var _ref, _slots$closeButton, _ref2, _slots$closeIcon, _slotProps$closeButto, _slotProps$closeIcon;\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiAlert'\n  });\n  const {\n      action,\n      children,\n      className,\n      closeText = 'Close',\n      color,\n      components = {},\n      componentsProps = {},\n      icon,\n      iconMapping = defaultIconMapping,\n      onClose,\n      role = 'alert',\n      severity = 'success',\n      slotProps = {},\n      slots = {},\n      variant = 'standard'\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    color,\n    severity,\n    variant\n  });\n  const classes = useUtilityClasses(ownerState);\n  const AlertCloseButton = (_ref = (_slots$closeButton = slots.closeButton) != null ? _slots$closeButton : components.CloseButton) != null ? _ref : IconButton;\n  const AlertCloseIcon = (_ref2 = (_slots$closeIcon = slots.closeIcon) != null ? _slots$closeIcon : components.CloseIcon) != null ? _ref2 : CloseIcon;\n  const closeButtonProps = (_slotProps$closeButto = slotProps.closeButton) != null ? _slotProps$closeButto : componentsProps.closeButton;\n  const closeIconProps = (_slotProps$closeIcon = slotProps.closeIcon) != null ? _slotProps$closeIcon : componentsProps.closeIcon;\n  return /*#__PURE__*/_jsxs(AlertRoot, _extends({\n    role: role,\n    elevation: 0,\n    ownerState: ownerState,\n    className: clsx(classes.root, className),\n    ref: ref\n  }, other, {\n    children: [icon !== false ? /*#__PURE__*/_jsx(AlertIcon, {\n      ownerState: ownerState,\n      className: classes.icon,\n      children: icon || iconMapping[severity] || defaultIconMapping[severity]\n    }) : null, /*#__PURE__*/_jsx(AlertMessage, {\n      ownerState: ownerState,\n      className: classes.message,\n      children: children\n    }), action != null ? /*#__PURE__*/_jsx(AlertAction, {\n      ownerState: ownerState,\n      className: classes.action,\n      children: action\n    }) : null, action == null && onClose ? /*#__PURE__*/_jsx(AlertAction, {\n      ownerState: ownerState,\n      className: classes.action,\n      children: /*#__PURE__*/_jsx(AlertCloseButton, _extends({\n        size: \"small\",\n        \"aria-label\": closeText,\n        title: closeText,\n        color: \"inherit\",\n        onClick: onClose\n      }, closeButtonProps, {\n        children: /*#__PURE__*/_jsx(AlertCloseIcon, _extends({\n          fontSize: \"small\"\n        }, closeIconProps))\n      }))\n    }) : null]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? Alert.propTypes /* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit the d.ts file and run \"yarn proptypes\"     |\n  // ----------------------------------------------------------------------\n  /**\n   * The action to display. It renders after the message, at the end of the alert.\n   */\n  action: PropTypes.node,\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * Override the default label for the *close popup* icon button.\n   *\n   * For localization purposes, you can use the provided [translations](/material-ui/guides/localization/).\n   * @default 'Close'\n   */\n  closeText: PropTypes.string,\n  /**\n   * The color of the component. Unless provided, the value is taken from the `severity` prop.\n   * It supports both default and custom theme colors, which can be added as shown in the\n   * [palette customization guide](https://mui.com/material-ui/customization/palette/#custom-colors).\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['error', 'info', 'success', 'warning']), PropTypes.string]),\n  /**\n   * The components used for each slot inside.\n   *\n   * This prop is an alias for the `slots` prop.\n   * It's recommended to use the `slots` prop instead.\n   *\n   * @default {}\n   */\n  components: PropTypes.shape({\n    CloseButton: PropTypes.elementType,\n    CloseIcon: PropTypes.elementType\n  }),\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * This prop is an alias for the `slotProps` prop.\n   * It's recommended to use the `slotProps` prop instead, as `componentsProps` will be deprecated in the future.\n   *\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    closeButton: PropTypes.object,\n    closeIcon: PropTypes.object\n  }),\n  /**\n   * Override the icon displayed before the children.\n   * Unless provided, the icon is mapped to the value of the `severity` prop.\n   * Set to `false` to remove the `icon`.\n   */\n  icon: PropTypes.node,\n  /**\n   * The component maps the `severity` prop to a range of different icons,\n   * for instance success to `<SuccessOutlined>`.\n   * If you wish to change this mapping, you can provide your own.\n   * Alternatively, you can use the `icon` prop to override the icon displayed.\n   */\n  iconMapping: PropTypes.shape({\n    error: PropTypes.node,\n    info: PropTypes.node,\n    success: PropTypes.node,\n    warning: PropTypes.node\n  }),\n  /**\n   * Callback fired when the component requests to be closed.\n   * When provided and no `action` prop is set, a close icon button is displayed that triggers the callback when clicked.\n   * @param {React.SyntheticEvent} event The event source of the callback.\n   */\n  onClose: PropTypes.func,\n  /**\n   * The ARIA role attribute of the element.\n   * @default 'alert'\n   */\n  role: PropTypes.string,\n  /**\n   * The severity of the alert. This defines the color and icon used.\n   * @default 'success'\n   */\n  severity: PropTypes.oneOf(['error', 'info', 'success', 'warning']),\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * This prop is an alias for the `componentsProps` prop, which will be deprecated in the future.\n   *\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    closeButton: PropTypes.object,\n    closeIcon: PropTypes.object\n  }),\n  /**\n   * The components used for each slot inside.\n   *\n   * This prop is an alias for the `components` prop, which will be deprecated in the future.\n   *\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    closeButton: PropTypes.elementType,\n    closeIcon: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The variant to use.\n   * @default 'standard'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['filled', 'outlined', 'standard']), PropTypes.string])\n} : void 0;\nexport default Alert;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "React", "PropTypes", "clsx", "unstable_composeClasses", "composeClasses", "darken", "lighten", "styled", "useThemeProps", "capitalize", "Paper", "alertClasses", "getAlertUtilityClass", "IconButton", "SuccessOutlinedIcon", "ReportProblemOutlinedIcon", "ErrorOutlineIcon", "InfoOutlinedIcon", "CloseIcon", "jsx", "_jsx", "jsxs", "_jsxs", "useUtilityClasses", "ownerState", "variant", "color", "severity", "classes", "slots", "root", "icon", "message", "action", "AlertRoot", "name", "slot", "overridesResolver", "props", "styles", "theme", "getColor", "palette", "mode", "getBackgroundColor", "typography", "body2", "backgroundColor", "display", "padding", "vars", "<PERSON><PERSON>", "light", "main", "border", "fontWeight", "fontWeightMedium", "dark", "getContrastText", "AlertIcon", "marginRight", "fontSize", "opacity", "AlertM<PERSON>age", "min<PERSON><PERSON><PERSON>", "overflow", "AlertAction", "alignItems", "marginLeft", "defaultIconMapping", "success", "warning", "error", "info", "forwardRef", "inProps", "ref", "_ref", "_slots$closeButton", "_ref2", "_slots$closeIcon", "_slotProps$closeButto", "_slotProps$closeIcon", "children", "className", "closeText", "components", "componentsProps", "iconMapping", "onClose", "role", "slotProps", "other", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "closeButton", "CloseButton", "AlertCloseIcon", "closeIcon", "closeButtonProps", "closeIconProps", "elevation", "size", "title", "onClick", "process", "env", "NODE_ENV", "propTypes", "node", "object", "string", "oneOfType", "oneOf", "shape", "elementType", "func", "sx", "arrayOf", "bool"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/material/Alert/Alert.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"action\", \"children\", \"className\", \"closeText\", \"color\", \"components\", \"componentsProps\", \"icon\", \"iconMapping\", \"onClose\", \"role\", \"severity\", \"slotProps\", \"slots\", \"variant\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { unstable_composeClasses as composeClasses } from '@mui/base/composeClasses';\nimport { darken, lighten } from '@mui/system';\nimport styled from '../styles/styled';\nimport useThemeProps from '../styles/useThemeProps';\nimport capitalize from '../utils/capitalize';\nimport Paper from '../Paper';\nimport alertClasses, { getAlertUtilityClass } from './alertClasses';\nimport IconButton from '../IconButton';\nimport SuccessOutlinedIcon from '../internal/svg-icons/SuccessOutlined';\nimport ReportProblemOutlinedIcon from '../internal/svg-icons/ReportProblemOutlined';\nimport ErrorOutlineIcon from '../internal/svg-icons/ErrorOutline';\nimport InfoOutlinedIcon from '../internal/svg-icons/InfoOutlined';\nimport CloseIcon from '../internal/svg-icons/Close';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    variant,\n    color,\n    severity,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', `${variant}${capitalize(color || severity)}`, `${variant}`],\n    icon: ['icon'],\n    message: ['message'],\n    action: ['action']\n  };\n  return composeClasses(slots, getAlertUtilityClass, classes);\n};\nconst AlertRoot = styled(Paper, {\n  name: 'MuiAlert',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[ownerState.variant], styles[`${ownerState.variant}${capitalize(ownerState.color || ownerState.severity)}`]];\n  }\n})(({\n  theme,\n  ownerState\n}) => {\n  const getColor = theme.palette.mode === 'light' ? darken : lighten;\n  const getBackgroundColor = theme.palette.mode === 'light' ? lighten : darken;\n  const color = ownerState.color || ownerState.severity;\n  return _extends({}, theme.typography.body2, {\n    backgroundColor: 'transparent',\n    display: 'flex',\n    padding: '6px 16px'\n  }, color && ownerState.variant === 'standard' && {\n    color: theme.vars ? theme.vars.palette.Alert[`${color}Color`] : getColor(theme.palette[color].light, 0.6),\n    backgroundColor: theme.vars ? theme.vars.palette.Alert[`${color}StandardBg`] : getBackgroundColor(theme.palette[color].light, 0.9),\n    [`& .${alertClasses.icon}`]: theme.vars ? {\n      color: theme.vars.palette.Alert[`${color}IconColor`]\n    } : {\n      color: theme.palette[color].main\n    }\n  }, color && ownerState.variant === 'outlined' && {\n    color: theme.vars ? theme.vars.palette.Alert[`${color}Color`] : getColor(theme.palette[color].light, 0.6),\n    border: `1px solid ${(theme.vars || theme).palette[color].light}`,\n    [`& .${alertClasses.icon}`]: theme.vars ? {\n      color: theme.vars.palette.Alert[`${color}IconColor`]\n    } : {\n      color: theme.palette[color].main\n    }\n  }, color && ownerState.variant === 'filled' && _extends({\n    fontWeight: theme.typography.fontWeightMedium\n  }, theme.vars ? {\n    color: theme.vars.palette.Alert[`${color}FilledColor`],\n    backgroundColor: theme.vars.palette.Alert[`${color}FilledBg`]\n  } : {\n    backgroundColor: theme.palette.mode === 'dark' ? theme.palette[color].dark : theme.palette[color].main,\n    color: theme.palette.getContrastText(theme.palette[color].main)\n  }));\n});\nconst AlertIcon = styled('div', {\n  name: 'MuiAlert',\n  slot: 'Icon',\n  overridesResolver: (props, styles) => styles.icon\n})({\n  marginRight: 12,\n  padding: '7px 0',\n  display: 'flex',\n  fontSize: 22,\n  opacity: 0.9\n});\nconst AlertMessage = styled('div', {\n  name: 'MuiAlert',\n  slot: 'Message',\n  overridesResolver: (props, styles) => styles.message\n})({\n  padding: '8px 0',\n  minWidth: 0,\n  overflow: 'auto'\n});\nconst AlertAction = styled('div', {\n  name: 'MuiAlert',\n  slot: 'Action',\n  overridesResolver: (props, styles) => styles.action\n})({\n  display: 'flex',\n  alignItems: 'flex-start',\n  padding: '4px 0 0 16px',\n  marginLeft: 'auto',\n  marginRight: -8\n});\nconst defaultIconMapping = {\n  success: /*#__PURE__*/_jsx(SuccessOutlinedIcon, {\n    fontSize: \"inherit\"\n  }),\n  warning: /*#__PURE__*/_jsx(ReportProblemOutlinedIcon, {\n    fontSize: \"inherit\"\n  }),\n  error: /*#__PURE__*/_jsx(ErrorOutlineIcon, {\n    fontSize: \"inherit\"\n  }),\n  info: /*#__PURE__*/_jsx(InfoOutlinedIcon, {\n    fontSize: \"inherit\"\n  })\n};\nconst Alert = /*#__PURE__*/React.forwardRef(function Alert(inProps, ref) {\n  var _ref, _slots$closeButton, _ref2, _slots$closeIcon, _slotProps$closeButto, _slotProps$closeIcon;\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiAlert'\n  });\n  const {\n      action,\n      children,\n      className,\n      closeText = 'Close',\n      color,\n      components = {},\n      componentsProps = {},\n      icon,\n      iconMapping = defaultIconMapping,\n      onClose,\n      role = 'alert',\n      severity = 'success',\n      slotProps = {},\n      slots = {},\n      variant = 'standard'\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    color,\n    severity,\n    variant\n  });\n  const classes = useUtilityClasses(ownerState);\n  const AlertCloseButton = (_ref = (_slots$closeButton = slots.closeButton) != null ? _slots$closeButton : components.CloseButton) != null ? _ref : IconButton;\n  const AlertCloseIcon = (_ref2 = (_slots$closeIcon = slots.closeIcon) != null ? _slots$closeIcon : components.CloseIcon) != null ? _ref2 : CloseIcon;\n  const closeButtonProps = (_slotProps$closeButto = slotProps.closeButton) != null ? _slotProps$closeButto : componentsProps.closeButton;\n  const closeIconProps = (_slotProps$closeIcon = slotProps.closeIcon) != null ? _slotProps$closeIcon : componentsProps.closeIcon;\n  return /*#__PURE__*/_jsxs(AlertRoot, _extends({\n    role: role,\n    elevation: 0,\n    ownerState: ownerState,\n    className: clsx(classes.root, className),\n    ref: ref\n  }, other, {\n    children: [icon !== false ? /*#__PURE__*/_jsx(AlertIcon, {\n      ownerState: ownerState,\n      className: classes.icon,\n      children: icon || iconMapping[severity] || defaultIconMapping[severity]\n    }) : null, /*#__PURE__*/_jsx(AlertMessage, {\n      ownerState: ownerState,\n      className: classes.message,\n      children: children\n    }), action != null ? /*#__PURE__*/_jsx(AlertAction, {\n      ownerState: ownerState,\n      className: classes.action,\n      children: action\n    }) : null, action == null && onClose ? /*#__PURE__*/_jsx(AlertAction, {\n      ownerState: ownerState,\n      className: classes.action,\n      children: /*#__PURE__*/_jsx(AlertCloseButton, _extends({\n        size: \"small\",\n        \"aria-label\": closeText,\n        title: closeText,\n        color: \"inherit\",\n        onClick: onClose\n      }, closeButtonProps, {\n        children: /*#__PURE__*/_jsx(AlertCloseIcon, _extends({\n          fontSize: \"small\"\n        }, closeIconProps))\n      }))\n    }) : null]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? Alert.propTypes /* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit the d.ts file and run \"yarn proptypes\"     |\n  // ----------------------------------------------------------------------\n  /**\n   * The action to display. It renders after the message, at the end of the alert.\n   */\n  action: PropTypes.node,\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * Override the default label for the *close popup* icon button.\n   *\n   * For localization purposes, you can use the provided [translations](/material-ui/guides/localization/).\n   * @default 'Close'\n   */\n  closeText: PropTypes.string,\n  /**\n   * The color of the component. Unless provided, the value is taken from the `severity` prop.\n   * It supports both default and custom theme colors, which can be added as shown in the\n   * [palette customization guide](https://mui.com/material-ui/customization/palette/#custom-colors).\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['error', 'info', 'success', 'warning']), PropTypes.string]),\n  /**\n   * The components used for each slot inside.\n   *\n   * This prop is an alias for the `slots` prop.\n   * It's recommended to use the `slots` prop instead.\n   *\n   * @default {}\n   */\n  components: PropTypes.shape({\n    CloseButton: PropTypes.elementType,\n    CloseIcon: PropTypes.elementType\n  }),\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * This prop is an alias for the `slotProps` prop.\n   * It's recommended to use the `slotProps` prop instead, as `componentsProps` will be deprecated in the future.\n   *\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    closeButton: PropTypes.object,\n    closeIcon: PropTypes.object\n  }),\n  /**\n   * Override the icon displayed before the children.\n   * Unless provided, the icon is mapped to the value of the `severity` prop.\n   * Set to `false` to remove the `icon`.\n   */\n  icon: PropTypes.node,\n  /**\n   * The component maps the `severity` prop to a range of different icons,\n   * for instance success to `<SuccessOutlined>`.\n   * If you wish to change this mapping, you can provide your own.\n   * Alternatively, you can use the `icon` prop to override the icon displayed.\n   */\n  iconMapping: PropTypes.shape({\n    error: PropTypes.node,\n    info: PropTypes.node,\n    success: PropTypes.node,\n    warning: PropTypes.node\n  }),\n  /**\n   * Callback fired when the component requests to be closed.\n   * When provided and no `action` prop is set, a close icon button is displayed that triggers the callback when clicked.\n   * @param {React.SyntheticEvent} event The event source of the callback.\n   */\n  onClose: PropTypes.func,\n  /**\n   * The ARIA role attribute of the element.\n   * @default 'alert'\n   */\n  role: PropTypes.string,\n  /**\n   * The severity of the alert. This defines the color and icon used.\n   * @default 'success'\n   */\n  severity: PropTypes.oneOf(['error', 'info', 'success', 'warning']),\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * This prop is an alias for the `componentsProps` prop, which will be deprecated in the future.\n   *\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    closeButton: PropTypes.object,\n    closeIcon: PropTypes.object\n  }),\n  /**\n   * The components used for each slot inside.\n   *\n   * This prop is an alias for the `components` prop, which will be deprecated in the future.\n   *\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    closeButton: PropTypes.elementType,\n    closeIcon: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The variant to use.\n   * @default 'standard'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['filled', 'outlined', 'standard']), PropTypes.string])\n} : void 0;\nexport default Alert;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,QAAQ,EAAE,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,YAAY,EAAE,iBAAiB,EAAE,MAAM,EAAE,aAAa,EAAE,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,OAAO,EAAE,SAAS,CAAC;AACnM,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,uBAAuB,IAAIC,cAAc,QAAQ,0BAA0B;AACpF,SAASC,MAAM,EAAEC,OAAO,QAAQ,aAAa;AAC7C,OAAOC,MAAM,MAAM,kBAAkB;AACrC,OAAOC,aAAa,MAAM,yBAAyB;AACnD,OAAOC,UAAU,MAAM,qBAAqB;AAC5C,OAAOC,KAAK,MAAM,UAAU;AAC5B,OAAOC,YAAY,IAAIC,oBAAoB,QAAQ,gBAAgB;AACnE,OAAOC,UAAU,MAAM,eAAe;AACtC,OAAOC,mBAAmB,MAAM,uCAAuC;AACvE,OAAOC,yBAAyB,MAAM,6CAA6C;AACnF,OAAOC,gBAAgB,MAAM,oCAAoC;AACjE,OAAOC,gBAAgB,MAAM,oCAAoC;AACjE,OAAOC,SAAS,MAAM,6BAA6B;AACnD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,OAAO;IACPC,KAAK;IACLC,QAAQ;IACRC;EACF,CAAC,GAAGJ,UAAU;EACd,MAAMK,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAG,GAAEL,OAAQ,GAAEhB,UAAU,CAACiB,KAAK,IAAIC,QAAQ,CAAE,EAAC,EAAG,GAAEF,OAAQ,EAAC,CAAC;IAC1EM,IAAI,EAAE,CAAC,MAAM,CAAC;IACdC,OAAO,EAAE,CAAC,SAAS,CAAC;IACpBC,MAAM,EAAE,CAAC,QAAQ;EACnB,CAAC;EACD,OAAO7B,cAAc,CAACyB,KAAK,EAAEjB,oBAAoB,EAAEgB,OAAO,CAAC;AAC7D,CAAC;AACD,MAAMM,SAAS,GAAG3B,MAAM,CAACG,KAAK,EAAE;EAC9ByB,IAAI,EAAE,UAAU;EAChBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJf;IACF,CAAC,GAAGc,KAAK;IACT,OAAO,CAACC,MAAM,CAACT,IAAI,EAAES,MAAM,CAACf,UAAU,CAACC,OAAO,CAAC,EAAEc,MAAM,CAAE,GAAEf,UAAU,CAACC,OAAQ,GAAEhB,UAAU,CAACe,UAAU,CAACE,KAAK,IAAIF,UAAU,CAACG,QAAQ,CAAE,EAAC,CAAC,CAAC;EACzI;AACF,CAAC,CAAC,CAAC,CAAC;EACFa,KAAK;EACLhB;AACF,CAAC,KAAK;EACJ,MAAMiB,QAAQ,GAAGD,KAAK,CAACE,OAAO,CAACC,IAAI,KAAK,OAAO,GAAGtC,MAAM,GAAGC,OAAO;EAClE,MAAMsC,kBAAkB,GAAGJ,KAAK,CAACE,OAAO,CAACC,IAAI,KAAK,OAAO,GAAGrC,OAAO,GAAGD,MAAM;EAC5E,MAAMqB,KAAK,GAAGF,UAAU,CAACE,KAAK,IAAIF,UAAU,CAACG,QAAQ;EACrD,OAAO7B,QAAQ,CAAC,CAAC,CAAC,EAAE0C,KAAK,CAACK,UAAU,CAACC,KAAK,EAAE;IAC1CC,eAAe,EAAE,aAAa;IAC9BC,OAAO,EAAE,MAAM;IACfC,OAAO,EAAE;EACX,CAAC,EAAEvB,KAAK,IAAIF,UAAU,CAACC,OAAO,KAAK,UAAU,IAAI;IAC/CC,KAAK,EAAEc,KAAK,CAACU,IAAI,GAAGV,KAAK,CAACU,IAAI,CAACR,OAAO,CAACS,KAAK,CAAE,GAAEzB,KAAM,OAAM,CAAC,GAAGe,QAAQ,CAACD,KAAK,CAACE,OAAO,CAAChB,KAAK,CAAC,CAAC0B,KAAK,EAAE,GAAG,CAAC;IACzGL,eAAe,EAAEP,KAAK,CAACU,IAAI,GAAGV,KAAK,CAACU,IAAI,CAACR,OAAO,CAACS,KAAK,CAAE,GAAEzB,KAAM,YAAW,CAAC,GAAGkB,kBAAkB,CAACJ,KAAK,CAACE,OAAO,CAAChB,KAAK,CAAC,CAAC0B,KAAK,EAAE,GAAG,CAAC;IAClI,CAAE,MAAKzC,YAAY,CAACoB,IAAK,EAAC,GAAGS,KAAK,CAACU,IAAI,GAAG;MACxCxB,KAAK,EAAEc,KAAK,CAACU,IAAI,CAACR,OAAO,CAACS,KAAK,CAAE,GAAEzB,KAAM,WAAU;IACrD,CAAC,GAAG;MACFA,KAAK,EAAEc,KAAK,CAACE,OAAO,CAAChB,KAAK,CAAC,CAAC2B;IAC9B;EACF,CAAC,EAAE3B,KAAK,IAAIF,UAAU,CAACC,OAAO,KAAK,UAAU,IAAI;IAC/CC,KAAK,EAAEc,KAAK,CAACU,IAAI,GAAGV,KAAK,CAACU,IAAI,CAACR,OAAO,CAACS,KAAK,CAAE,GAAEzB,KAAM,OAAM,CAAC,GAAGe,QAAQ,CAACD,KAAK,CAACE,OAAO,CAAChB,KAAK,CAAC,CAAC0B,KAAK,EAAE,GAAG,CAAC;IACzGE,MAAM,EAAG,aAAY,CAACd,KAAK,CAACU,IAAI,IAAIV,KAAK,EAAEE,OAAO,CAAChB,KAAK,CAAC,CAAC0B,KAAM,EAAC;IACjE,CAAE,MAAKzC,YAAY,CAACoB,IAAK,EAAC,GAAGS,KAAK,CAACU,IAAI,GAAG;MACxCxB,KAAK,EAAEc,KAAK,CAACU,IAAI,CAACR,OAAO,CAACS,KAAK,CAAE,GAAEzB,KAAM,WAAU;IACrD,CAAC,GAAG;MACFA,KAAK,EAAEc,KAAK,CAACE,OAAO,CAAChB,KAAK,CAAC,CAAC2B;IAC9B;EACF,CAAC,EAAE3B,KAAK,IAAIF,UAAU,CAACC,OAAO,KAAK,QAAQ,IAAI3B,QAAQ,CAAC;IACtDyD,UAAU,EAAEf,KAAK,CAACK,UAAU,CAACW;EAC/B,CAAC,EAAEhB,KAAK,CAACU,IAAI,GAAG;IACdxB,KAAK,EAAEc,KAAK,CAACU,IAAI,CAACR,OAAO,CAACS,KAAK,CAAE,GAAEzB,KAAM,aAAY,CAAC;IACtDqB,eAAe,EAAEP,KAAK,CAACU,IAAI,CAACR,OAAO,CAACS,KAAK,CAAE,GAAEzB,KAAM,UAAS;EAC9D,CAAC,GAAG;IACFqB,eAAe,EAAEP,KAAK,CAACE,OAAO,CAACC,IAAI,KAAK,MAAM,GAAGH,KAAK,CAACE,OAAO,CAAChB,KAAK,CAAC,CAAC+B,IAAI,GAAGjB,KAAK,CAACE,OAAO,CAAChB,KAAK,CAAC,CAAC2B,IAAI;IACtG3B,KAAK,EAAEc,KAAK,CAACE,OAAO,CAACgB,eAAe,CAAClB,KAAK,CAACE,OAAO,CAAChB,KAAK,CAAC,CAAC2B,IAAI;EAChE,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACF,MAAMM,SAAS,GAAGpD,MAAM,CAAC,KAAK,EAAE;EAC9B4B,IAAI,EAAE,UAAU;EAChBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACR;AAC/C,CAAC,CAAC,CAAC;EACD6B,WAAW,EAAE,EAAE;EACfX,OAAO,EAAE,OAAO;EAChBD,OAAO,EAAE,MAAM;EACfa,QAAQ,EAAE,EAAE;EACZC,OAAO,EAAE;AACX,CAAC,CAAC;AACF,MAAMC,YAAY,GAAGxD,MAAM,CAAC,KAAK,EAAE;EACjC4B,IAAI,EAAE,UAAU;EAChBC,IAAI,EAAE,SAAS;EACfC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACP;AAC/C,CAAC,CAAC,CAAC;EACDiB,OAAO,EAAE,OAAO;EAChBe,QAAQ,EAAE,CAAC;EACXC,QAAQ,EAAE;AACZ,CAAC,CAAC;AACF,MAAMC,WAAW,GAAG3D,MAAM,CAAC,KAAK,EAAE;EAChC4B,IAAI,EAAE,UAAU;EAChBC,IAAI,EAAE,QAAQ;EACdC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACN;AAC/C,CAAC,CAAC,CAAC;EACDe,OAAO,EAAE,MAAM;EACfmB,UAAU,EAAE,YAAY;EACxBlB,OAAO,EAAE,cAAc;EACvBmB,UAAU,EAAE,MAAM;EAClBR,WAAW,EAAE,CAAC;AAChB,CAAC,CAAC;AACF,MAAMS,kBAAkB,GAAG;EACzBC,OAAO,EAAE,aAAalD,IAAI,CAACN,mBAAmB,EAAE;IAC9C+C,QAAQ,EAAE;EACZ,CAAC,CAAC;EACFU,OAAO,EAAE,aAAanD,IAAI,CAACL,yBAAyB,EAAE;IACpD8C,QAAQ,EAAE;EACZ,CAAC,CAAC;EACFW,KAAK,EAAE,aAAapD,IAAI,CAACJ,gBAAgB,EAAE;IACzC6C,QAAQ,EAAE;EACZ,CAAC,CAAC;EACFY,IAAI,EAAE,aAAarD,IAAI,CAACH,gBAAgB,EAAE;IACxC4C,QAAQ,EAAE;EACZ,CAAC;AACH,CAAC;AACD,MAAMV,KAAK,GAAG,aAAanD,KAAK,CAAC0E,UAAU,CAAC,SAASvB,KAAKA,CAACwB,OAAO,EAAEC,GAAG,EAAE;EACvE,IAAIC,IAAI,EAAEC,kBAAkB,EAAEC,KAAK,EAAEC,gBAAgB,EAAEC,qBAAqB,EAAEC,oBAAoB;EAClG,MAAM5C,KAAK,GAAG9B,aAAa,CAAC;IAC1B8B,KAAK,EAAEqC,OAAO;IACdxC,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFF,MAAM;MACNkD,QAAQ;MACRC,SAAS;MACTC,SAAS,GAAG,OAAO;MACnB3D,KAAK;MACL4D,UAAU,GAAG,CAAC,CAAC;MACfC,eAAe,GAAG,CAAC,CAAC;MACpBxD,IAAI;MACJyD,WAAW,GAAGnB,kBAAkB;MAChCoB,OAAO;MACPC,IAAI,GAAG,OAAO;MACd/D,QAAQ,GAAG,SAAS;MACpBgE,SAAS,GAAG,CAAC,CAAC;MACd9D,KAAK,GAAG,CAAC,CAAC;MACVJ,OAAO,GAAG;IACZ,CAAC,GAAGa,KAAK;IACTsD,KAAK,GAAG/F,6BAA6B,CAACyC,KAAK,EAAEvC,SAAS,CAAC;EACzD,MAAMyB,UAAU,GAAG1B,QAAQ,CAAC,CAAC,CAAC,EAAEwC,KAAK,EAAE;IACrCZ,KAAK;IACLC,QAAQ;IACRF;EACF,CAAC,CAAC;EACF,MAAMG,OAAO,GAAGL,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAMqE,gBAAgB,GAAG,CAAChB,IAAI,GAAG,CAACC,kBAAkB,GAAGjD,KAAK,CAACiE,WAAW,KAAK,IAAI,GAAGhB,kBAAkB,GAAGQ,UAAU,CAACS,WAAW,KAAK,IAAI,GAAGlB,IAAI,GAAGhE,UAAU;EAC5J,MAAMmF,cAAc,GAAG,CAACjB,KAAK,GAAG,CAACC,gBAAgB,GAAGnD,KAAK,CAACoE,SAAS,KAAK,IAAI,GAAGjB,gBAAgB,GAAGM,UAAU,CAACpE,SAAS,KAAK,IAAI,GAAG6D,KAAK,GAAG7D,SAAS;EACnJ,MAAMgF,gBAAgB,GAAG,CAACjB,qBAAqB,GAAGU,SAAS,CAACG,WAAW,KAAK,IAAI,GAAGb,qBAAqB,GAAGM,eAAe,CAACO,WAAW;EACtI,MAAMK,cAAc,GAAG,CAACjB,oBAAoB,GAAGS,SAAS,CAACM,SAAS,KAAK,IAAI,GAAGf,oBAAoB,GAAGK,eAAe,CAACU,SAAS;EAC9H,OAAO,aAAa3E,KAAK,CAACY,SAAS,EAAEpC,QAAQ,CAAC;IAC5C4F,IAAI,EAAEA,IAAI;IACVU,SAAS,EAAE,CAAC;IACZ5E,UAAU,EAAEA,UAAU;IACtB4D,SAAS,EAAElF,IAAI,CAAC0B,OAAO,CAACE,IAAI,EAAEsD,SAAS,CAAC;IACxCR,GAAG,EAAEA;EACP,CAAC,EAAEgB,KAAK,EAAE;IACRT,QAAQ,EAAE,CAACpD,IAAI,KAAK,KAAK,GAAG,aAAaX,IAAI,CAACuC,SAAS,EAAE;MACvDnC,UAAU,EAAEA,UAAU;MACtB4D,SAAS,EAAExD,OAAO,CAACG,IAAI;MACvBoD,QAAQ,EAAEpD,IAAI,IAAIyD,WAAW,CAAC7D,QAAQ,CAAC,IAAI0C,kBAAkB,CAAC1C,QAAQ;IACxE,CAAC,CAAC,GAAG,IAAI,EAAE,aAAaP,IAAI,CAAC2C,YAAY,EAAE;MACzCvC,UAAU,EAAEA,UAAU;MACtB4D,SAAS,EAAExD,OAAO,CAACI,OAAO;MAC1BmD,QAAQ,EAAEA;IACZ,CAAC,CAAC,EAAElD,MAAM,IAAI,IAAI,GAAG,aAAab,IAAI,CAAC8C,WAAW,EAAE;MAClD1C,UAAU,EAAEA,UAAU;MACtB4D,SAAS,EAAExD,OAAO,CAACK,MAAM;MACzBkD,QAAQ,EAAElD;IACZ,CAAC,CAAC,GAAG,IAAI,EAAEA,MAAM,IAAI,IAAI,IAAIwD,OAAO,GAAG,aAAarE,IAAI,CAAC8C,WAAW,EAAE;MACpE1C,UAAU,EAAEA,UAAU;MACtB4D,SAAS,EAAExD,OAAO,CAACK,MAAM;MACzBkD,QAAQ,EAAE,aAAa/D,IAAI,CAACyE,gBAAgB,EAAE/F,QAAQ,CAAC;QACrDuG,IAAI,EAAE,OAAO;QACb,YAAY,EAAEhB,SAAS;QACvBiB,KAAK,EAAEjB,SAAS;QAChB3D,KAAK,EAAE,SAAS;QAChB6E,OAAO,EAAEd;MACX,CAAC,EAAES,gBAAgB,EAAE;QACnBf,QAAQ,EAAE,aAAa/D,IAAI,CAAC4E,cAAc,EAAElG,QAAQ,CAAC;UACnD+D,QAAQ,EAAE;QACZ,CAAC,EAAEsC,cAAc,CAAC;MACpB,CAAC,CAAC;IACJ,CAAC,CAAC,GAAG,IAAI;EACX,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFK,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGvD,KAAK,CAACwD,SAAS,CAAC,yBAAyB;EAC/E;EACA;EACA;EACA;EACA;AACF;AACA;EACE1E,MAAM,EAAEhC,SAAS,CAAC2G,IAAI;EACtB;AACF;AACA;EACEzB,QAAQ,EAAElF,SAAS,CAAC2G,IAAI;EACxB;AACF;AACA;EACEhF,OAAO,EAAE3B,SAAS,CAAC4G,MAAM;EACzB;AACF;AACA;EACEzB,SAAS,EAAEnF,SAAS,CAAC6G,MAAM;EAC3B;AACF;AACA;AACA;AACA;AACA;EACEzB,SAAS,EAAEpF,SAAS,CAAC6G,MAAM;EAC3B;AACF;AACA;AACA;AACA;EACEpF,KAAK,EAAEzB,SAAS,CAAC,sCAAsC8G,SAAS,CAAC,CAAC9G,SAAS,CAAC+G,KAAK,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,EAAE/G,SAAS,CAAC6G,MAAM,CAAC,CAAC;EAC9I;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACExB,UAAU,EAAErF,SAAS,CAACgH,KAAK,CAAC;IAC1BlB,WAAW,EAAE9F,SAAS,CAACiH,WAAW;IAClChG,SAAS,EAAEjB,SAAS,CAACiH;EACvB,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE3B,eAAe,EAAEtF,SAAS,CAACgH,KAAK,CAAC;IAC/BnB,WAAW,EAAE7F,SAAS,CAAC4G,MAAM;IAC7BZ,SAAS,EAAEhG,SAAS,CAAC4G;EACvB,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;EACE9E,IAAI,EAAE9B,SAAS,CAAC2G,IAAI;EACpB;AACF;AACA;AACA;AACA;AACA;EACEpB,WAAW,EAAEvF,SAAS,CAACgH,KAAK,CAAC;IAC3BzC,KAAK,EAAEvE,SAAS,CAAC2G,IAAI;IACrBnC,IAAI,EAAExE,SAAS,CAAC2G,IAAI;IACpBtC,OAAO,EAAErE,SAAS,CAAC2G,IAAI;IACvBrC,OAAO,EAAEtE,SAAS,CAAC2G;EACrB,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;EACEnB,OAAO,EAAExF,SAAS,CAACkH,IAAI;EACvB;AACF;AACA;AACA;EACEzB,IAAI,EAAEzF,SAAS,CAAC6G,MAAM;EACtB;AACF;AACA;AACA;EACEnF,QAAQ,EAAE1B,SAAS,CAAC+G,KAAK,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;EAClE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACErB,SAAS,EAAE1F,SAAS,CAACgH,KAAK,CAAC;IACzBnB,WAAW,EAAE7F,SAAS,CAAC4G,MAAM;IAC7BZ,SAAS,EAAEhG,SAAS,CAAC4G;EACvB,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;AACA;AACA;EACEhF,KAAK,EAAE5B,SAAS,CAACgH,KAAK,CAAC;IACrBnB,WAAW,EAAE7F,SAAS,CAACiH,WAAW;IAClCjB,SAAS,EAAEhG,SAAS,CAACiH;EACvB,CAAC,CAAC;EACF;AACF;AACA;EACEE,EAAE,EAAEnH,SAAS,CAAC8G,SAAS,CAAC,CAAC9G,SAAS,CAACoH,OAAO,CAACpH,SAAS,CAAC8G,SAAS,CAAC,CAAC9G,SAAS,CAACkH,IAAI,EAAElH,SAAS,CAAC4G,MAAM,EAAE5G,SAAS,CAACqH,IAAI,CAAC,CAAC,CAAC,EAAErH,SAAS,CAACkH,IAAI,EAAElH,SAAS,CAAC4G,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;AACA;EACEpF,OAAO,EAAExB,SAAS,CAAC,sCAAsC8G,SAAS,CAAC,CAAC9G,SAAS,CAAC+G,KAAK,CAAC,CAAC,QAAQ,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC,EAAE/G,SAAS,CAAC6G,MAAM,CAAC;AAC5I,CAAC,GAAG,KAAK,CAAC;AACV,eAAe3D,KAAK"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}