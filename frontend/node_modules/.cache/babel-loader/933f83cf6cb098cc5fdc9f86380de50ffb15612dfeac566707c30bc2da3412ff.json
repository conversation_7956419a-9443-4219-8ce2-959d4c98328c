{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { Global } from '@emotion/react';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction isEmpty(obj) {\n  return obj === undefined || obj === null || Object.keys(obj).length === 0;\n}\nexport default function GlobalStyles(props) {\n  const {\n    styles,\n    defaultTheme = {}\n  } = props;\n  const globalStyles = typeof styles === 'function' ? themeInput => styles(isEmpty(themeInput) ? defaultTheme : themeInput) : styles;\n  return /*#__PURE__*/_jsx(Global, {\n    styles: globalStyles\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? GlobalStyles.propTypes = {\n  defaultTheme: PropTypes.object,\n  styles: PropTypes.oneOfType([PropTypes.array, PropTypes.string, PropTypes.object, PropTypes.func])\n} : void 0;", "map": {"version": 3, "names": ["React", "PropTypes", "Global", "jsx", "_jsx", "isEmpty", "obj", "undefined", "Object", "keys", "length", "GlobalStyles", "props", "styles", "defaultTheme", "globalStyles", "themeInput", "process", "env", "NODE_ENV", "propTypes", "object", "oneOfType", "array", "string", "func"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/styled-engine/GlobalStyles/GlobalStyles.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { Global } from '@emotion/react';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction isEmpty(obj) {\n  return obj === undefined || obj === null || Object.keys(obj).length === 0;\n}\nexport default function GlobalStyles(props) {\n  const {\n    styles,\n    defaultTheme = {}\n  } = props;\n  const globalStyles = typeof styles === 'function' ? themeInput => styles(isEmpty(themeInput) ? defaultTheme : themeInput) : styles;\n  return /*#__PURE__*/_jsx(Global, {\n    styles: globalStyles\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? GlobalStyles.propTypes = {\n  defaultTheme: PropTypes.object,\n  styles: PropTypes.oneOfType([PropTypes.array, PropTypes.string, PropTypes.object, PropTypes.func])\n} : void 0;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,MAAM,QAAQ,gBAAgB;AACvC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,OAAOA,CAACC,GAAG,EAAE;EACpB,OAAOA,GAAG,KAAKC,SAAS,IAAID,GAAG,KAAK,IAAI,IAAIE,MAAM,CAACC,IAAI,CAACH,GAAG,CAAC,CAACI,MAAM,KAAK,CAAC;AAC3E;AACA,eAAe,SAASC,YAAYA,CAACC,KAAK,EAAE;EAC1C,MAAM;IACJC,MAAM;IACNC,YAAY,GAAG,CAAC;EAClB,CAAC,GAAGF,KAAK;EACT,MAAMG,YAAY,GAAG,OAAOF,MAAM,KAAK,UAAU,GAAGG,UAAU,IAAIH,MAAM,CAACR,OAAO,CAACW,UAAU,CAAC,GAAGF,YAAY,GAAGE,UAAU,CAAC,GAAGH,MAAM;EAClI,OAAO,aAAaT,IAAI,CAACF,MAAM,EAAE;IAC/BW,MAAM,EAAEE;EACV,CAAC,CAAC;AACJ;AACAE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGR,YAAY,CAACS,SAAS,GAAG;EAC/DN,YAAY,EAAEb,SAAS,CAACoB,MAAM;EAC9BR,MAAM,EAAEZ,SAAS,CAACqB,SAAS,CAAC,CAACrB,SAAS,CAACsB,KAAK,EAAEtB,SAAS,CAACuB,MAAM,EAAEvB,SAAS,CAACoB,MAAM,EAAEpB,SAAS,CAACwB,IAAI,CAAC;AACnG,CAAC,GAAG,KAAK,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}