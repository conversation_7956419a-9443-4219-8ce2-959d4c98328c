{"ast": null, "code": "'use client';\n\nexport * from './MenuItem';\nexport * from './MenuItem.types';\nexport * from './menuItemClasses';", "map": {"version": 3, "names": [], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/base/MenuItem/index.js"], "sourcesContent": ["'use client';\n\nexport * from './MenuItem';\nexport * from './MenuItem.types';\nexport * from './menuItemClasses';"], "mappings": "AAAA,YAAY;;AAEZ,cAAc,YAAY;AAC1B,cAAc,kBAAkB;AAChC,cAAc,mBAAmB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}