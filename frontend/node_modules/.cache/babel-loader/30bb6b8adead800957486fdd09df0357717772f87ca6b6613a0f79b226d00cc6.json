{"ast": null, "code": "'use client';\n\nimport { createStyled, shouldForwardProp } from '@mui/system';\nimport defaultTheme from './defaultTheme';\nimport THEME_ID from './identifier';\nexport const rootShouldForwardProp = prop => shouldForwardProp(prop) && prop !== 'classes';\nexport const slotShouldForwardProp = shouldForwardProp;\nconst styled = createStyled({\n  themeId: THEME_ID,\n  defaultTheme,\n  rootShouldForwardProp\n});\nexport default styled;", "map": {"version": 3, "names": ["createStyled", "shouldForwardProp", "defaultTheme", "THEME_ID", "rootShouldForwardProp", "prop", "slotShouldForwardProp", "styled", "themeId"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/material/styles/styled.js"], "sourcesContent": ["'use client';\n\nimport { createStyled, shouldForwardProp } from '@mui/system';\nimport defaultTheme from './defaultTheme';\nimport THEME_ID from './identifier';\nexport const rootShouldForwardProp = prop => shouldForwardProp(prop) && prop !== 'classes';\nexport const slotShouldForwardProp = shouldForwardProp;\nconst styled = createStyled({\n  themeId: THEME_ID,\n  defaultTheme,\n  rootShouldForwardProp\n});\nexport default styled;"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,YAAY,EAAEC,iBAAiB,QAAQ,aAAa;AAC7D,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,QAAQ,MAAM,cAAc;AACnC,OAAO,MAAMC,qBAAqB,GAAGC,IAAI,IAAIJ,iBAAiB,CAACI,IAAI,CAAC,IAAIA,IAAI,KAAK,SAAS;AAC1F,OAAO,MAAMC,qBAAqB,GAAGL,iBAAiB;AACtD,MAAMM,MAAM,GAAGP,YAAY,CAAC;EAC1BQ,OAAO,EAAEL,QAAQ;EACjBD,YAAY;EACZE;AACF,CAAC,CAAC;AACF,eAAeG,MAAM"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}