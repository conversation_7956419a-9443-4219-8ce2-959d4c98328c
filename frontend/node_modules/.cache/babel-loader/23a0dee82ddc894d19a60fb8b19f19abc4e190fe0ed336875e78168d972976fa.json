{"ast": null, "code": "'use client';\n\nexport { useInput } from './useInput';\nexport * from './useInput.types';", "map": {"version": 3, "names": ["useInput"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/base/useInput/index.js"], "sourcesContent": ["'use client';\n\nexport { useInput } from './useInput';\nexport * from './useInput.types';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,QAAQ,QAAQ,YAAY;AACrC,cAAc,kBAAkB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}