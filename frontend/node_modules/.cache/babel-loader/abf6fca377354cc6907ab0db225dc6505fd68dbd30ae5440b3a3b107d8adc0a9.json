{"ast": null, "code": "'use client';\n\nimport { useThemeProps as systemUseThemeProps } from '@mui/system';\nimport defaultTheme from './defaultTheme';\nimport THEME_ID from './identifier';\nexport default function useThemeProps({\n  props,\n  name\n}) {\n  return systemUseThemeProps({\n    props,\n    name,\n    defaultTheme,\n    themeId: THEME_ID\n  });\n}", "map": {"version": 3, "names": ["useThemeProps", "systemUseThemeProps", "defaultTheme", "THEME_ID", "props", "name", "themeId"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/material/styles/useThemeProps.js"], "sourcesContent": ["'use client';\n\nimport { useThemeProps as systemUseThemeProps } from '@mui/system';\nimport defaultTheme from './defaultTheme';\nimport THEME_ID from './identifier';\nexport default function useThemeProps({\n  props,\n  name\n}) {\n  return systemUseThemeProps({\n    props,\n    name,\n    defaultTheme,\n    themeId: THEME_ID\n  });\n}"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,aAAa,IAAIC,mBAAmB,QAAQ,aAAa;AAClE,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,QAAQ,MAAM,cAAc;AACnC,eAAe,SAASH,aAAaA,CAAC;EACpCI,KAAK;EACLC;AACF,CAAC,EAAE;EACD,OAAOJ,mBAAmB,CAAC;IACzBG,KAAK;IACLC,IAAI;IACJH,YAAY;IACZI,OAAO,EAAEH;EACX,CAAC,CAAC;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}