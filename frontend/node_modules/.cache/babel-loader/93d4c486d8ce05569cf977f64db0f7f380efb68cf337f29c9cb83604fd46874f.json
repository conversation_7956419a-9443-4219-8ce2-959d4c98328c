{"ast": null, "code": "import { unstable_generateUtilityClass as generateUtilityClass, unstable_generateUtilityClasses as generateUtilityClasses } from '@mui/utils';\nexport function getStackUtilityClass(slot) {\n  return generateUtilityClass('MuiStack', slot);\n}\nconst stackClasses = generateUtilityClasses('MuiStack', ['root']);\nexport default stackClasses;", "map": {"version": 3, "names": ["unstable_generateUtilityClass", "generateUtilityClass", "unstable_generateUtilityClasses", "generateUtilityClasses", "getStackUtilityClass", "slot", "stackClasses"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/system/esm/Stack/stackClasses.js"], "sourcesContent": ["import { unstable_generateUtilityClass as generateUtilityClass, unstable_generateUtilityClasses as generateUtilityClasses } from '@mui/utils';\nexport function getStackUtilityClass(slot) {\n  return generateUtilityClass('MuiStack', slot);\n}\nconst stackClasses = generateUtilityClasses('MuiStack', ['root']);\nexport default stackClasses;"], "mappings": "AAAA,SAASA,6BAA6B,IAAIC,oBAAoB,EAAEC,+BAA+B,IAAIC,sBAAsB,QAAQ,YAAY;AAC7I,OAAO,SAASC,oBAAoBA,CAACC,IAAI,EAAE;EACzC,OAAOJ,oBAAoB,CAAC,UAAU,EAAEI,IAAI,CAAC;AAC/C;AACA,MAAMC,YAAY,GAAGH,sBAAsB,CAAC,UAAU,EAAE,CAAC,MAAM,CAAC,CAAC;AACjE,eAAeG,YAAY"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}