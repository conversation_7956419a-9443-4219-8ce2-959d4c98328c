{"ast": null, "code": "'use client';\n\nimport { unstable_useEventCallback as useEventCallback } from '@mui/utils';\nexport default useEventCallback;", "map": {"version": 3, "names": ["unstable_useEventCallback", "useEventCallback"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/material/utils/useEventCallback.js"], "sourcesContent": ["'use client';\n\nimport { unstable_useEventCallback as useEventCallback } from '@mui/utils';\nexport default useEventCallback;"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,yBAAyB,IAAIC,gBAAgB,QAAQ,YAAY;AAC1E,eAAeA,gBAAgB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}