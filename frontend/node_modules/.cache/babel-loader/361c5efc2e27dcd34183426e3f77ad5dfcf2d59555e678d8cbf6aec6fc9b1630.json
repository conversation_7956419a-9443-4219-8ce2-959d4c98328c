{"ast": null, "code": "'use client';\n\nexport { default } from './StyledEngineProvider';", "map": {"version": 3, "names": ["default"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/styled-engine/StyledEngineProvider/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './StyledEngineProvider';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,wBAAwB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}