{"ast": null, "code": "'use client';\n\nexport { useSwitch } from './useSwitch';\nexport * from './useSwitch.types';", "map": {"version": 3, "names": ["useSwitch"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/base/useSwitch/index.js"], "sourcesContent": ["'use client';\n\nexport { useSwitch } from './useSwitch';\nexport * from './useSwitch.types';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,SAAS,QAAQ,aAAa;AACvC,cAAc,mBAAmB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}