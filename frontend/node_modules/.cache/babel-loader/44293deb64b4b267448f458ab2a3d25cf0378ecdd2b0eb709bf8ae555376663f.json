{"ast": null, "code": "import { formatMuiErrorMessage as _formatMuiErrorMessage } from \"@mui/utils\";\nexport default function makeStyles() {\n  throw new Error(process.env.NODE_ENV !== \"production\" ? `MUI: makeStyles is no longer exported from @mui/material/styles.\nYou have to import it from @mui/styles.\nSee https://mui.com/r/migration-v4/#mui-material-styles for more details.` : _formatMuiErrorMessage(14));\n}", "map": {"version": 3, "names": ["formatMuiErrorMessage", "_formatMuiErrorMessage", "makeStyles", "Error", "process", "env", "NODE_ENV"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/material/styles/makeStyles.js"], "sourcesContent": ["import { formatMuiErrorMessage as _formatMuiErrorMessage } from \"@mui/utils\";\nexport default function makeStyles() {\n  throw new Error(process.env.NODE_ENV !== \"production\" ? `MUI: makeStyles is no longer exported from @mui/material/styles.\nYou have to import it from @mui/styles.\nSee https://mui.com/r/migration-v4/#mui-material-styles for more details.` : _formatMuiErrorMessage(14));\n}"], "mappings": "AAAA,SAASA,qBAAqB,IAAIC,sBAAsB,QAAQ,YAAY;AAC5E,eAAe,SAASC,UAAUA,CAAA,EAAG;EACnC,MAAM,IAAIC,KAAK,CAACC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAI;AAC3D;AACA,0EAA0E,GAAGL,sBAAsB,CAAC,EAAE,CAAC,CAAC;AACxG"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}