{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"colorSchemes\", \"components\"],\n  _excluded2 = [\"light\"];\nimport { deepmerge } from '@mui/utils';\nimport cssVarsParser from './cssVarsParser';\nfunction prepareCssVars(theme, parserConfig) {\n  // @ts-ignore - ignore components do not exist\n  const {\n      colorSchemes = {}\n    } = theme,\n    otherTheme = _objectWithoutPropertiesLoose(theme, _excluded);\n  const {\n    vars: rootVars,\n    css: rootCss,\n    varsWithDefaults: rootVarsWithDefaults\n  } = cssVarsParser(otherTheme, parserConfig);\n  let themeVars = rootVarsWithDefaults;\n  const colorSchemesMap = {};\n  const {\n      light\n    } = colorSchemes,\n    otherColorSchemes = _objectWithoutPropertiesLoose(colorSchemes, _excluded2);\n  Object.entries(otherColorSchemes || {}).forEach(([key, scheme]) => {\n    const {\n      vars,\n      css,\n      varsWithDefaults\n    } = cssVarsParser(scheme, parserConfig);\n    themeVars = deepmerge(themeVars, varsWithDefaults);\n    colorSchemesMap[key] = {\n      css,\n      vars\n    };\n  });\n  if (light) {\n    // light color scheme vars should be merged last to set as default\n    const {\n      css,\n      vars,\n      varsWithDefaults\n    } = cssVarsParser(light, parserConfig);\n    themeVars = deepmerge(themeVars, varsWithDefaults);\n    colorSchemesMap.light = {\n      css,\n      vars\n    };\n  }\n  const generateCssVars = colorScheme => {\n    if (!colorScheme) {\n      return {\n        css: _extends({}, rootCss),\n        vars: rootVars\n      };\n    }\n    return {\n      css: _extends({}, colorSchemesMap[colorScheme].css),\n      vars: colorSchemesMap[colorScheme].vars\n    };\n  };\n  return {\n    vars: themeVars,\n    generateCssVars\n  };\n}\nexport default prepareCssVars;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "_excluded2", "deepmerge", "cssVarsParser", "prepareCssVars", "theme", "parserConfig", "colorSchemes", "otherTheme", "vars", "rootVars", "css", "rootCss", "varsWithDefaults", "rootVarsWithDefaults", "themeVars", "colorSchemesMap", "light", "otherColorSchemes", "Object", "entries", "for<PERSON>ach", "key", "scheme", "generateCssVars", "colorScheme"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/system/esm/cssVars/prepareCssVars.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"colorSchemes\", \"components\"],\n  _excluded2 = [\"light\"];\nimport { deepmerge } from '@mui/utils';\nimport cssVarsParser from './cssVarsParser';\nfunction prepareCssVars(theme, parserConfig) {\n  // @ts-ignore - ignore components do not exist\n  const {\n      colorSchemes = {}\n    } = theme,\n    otherTheme = _objectWithoutPropertiesLoose(theme, _excluded);\n  const {\n    vars: rootVars,\n    css: rootCss,\n    varsWithDefaults: rootVarsWithDefaults\n  } = cssVarsParser(otherTheme, parserConfig);\n  let themeVars = rootVarsWithDefaults;\n  const colorSchemesMap = {};\n  const {\n      light\n    } = colorSchemes,\n    otherColorSchemes = _objectWithoutPropertiesLoose(colorSchemes, _excluded2);\n  Object.entries(otherColorSchemes || {}).forEach(([key, scheme]) => {\n    const {\n      vars,\n      css,\n      varsWithDefaults\n    } = cssVarsParser(scheme, parserConfig);\n    themeVars = deepmerge(themeVars, varsWithDefaults);\n    colorSchemesMap[key] = {\n      css,\n      vars\n    };\n  });\n  if (light) {\n    // light color scheme vars should be merged last to set as default\n    const {\n      css,\n      vars,\n      varsWithDefaults\n    } = cssVarsParser(light, parserConfig);\n    themeVars = deepmerge(themeVars, varsWithDefaults);\n    colorSchemesMap.light = {\n      css,\n      vars\n    };\n  }\n  const generateCssVars = colorScheme => {\n    if (!colorScheme) {\n      return {\n        css: _extends({}, rootCss),\n        vars: rootVars\n      };\n    }\n    return {\n      css: _extends({}, colorSchemesMap[colorScheme].css),\n      vars: colorSchemesMap[colorScheme].vars\n    };\n  };\n  return {\n    vars: themeVars,\n    generateCssVars\n  };\n}\nexport default prepareCssVars;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,cAAc,EAAE,YAAY,CAAC;EAC9CC,UAAU,GAAG,CAAC,OAAO,CAAC;AACxB,SAASC,SAAS,QAAQ,YAAY;AACtC,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,SAASC,cAAcA,CAACC,KAAK,EAAEC,YAAY,EAAE;EAC3C;EACA,MAAM;MACFC,YAAY,GAAG,CAAC;IAClB,CAAC,GAAGF,KAAK;IACTG,UAAU,GAAGT,6BAA6B,CAACM,KAAK,EAAEL,SAAS,CAAC;EAC9D,MAAM;IACJS,IAAI,EAAEC,QAAQ;IACdC,GAAG,EAAEC,OAAO;IACZC,gBAAgB,EAAEC;EACpB,CAAC,GAAGX,aAAa,CAACK,UAAU,EAAEF,YAAY,CAAC;EAC3C,IAAIS,SAAS,GAAGD,oBAAoB;EACpC,MAAME,eAAe,GAAG,CAAC,CAAC;EAC1B,MAAM;MACFC;IACF,CAAC,GAAGV,YAAY;IAChBW,iBAAiB,GAAGnB,6BAA6B,CAACQ,YAAY,EAAEN,UAAU,CAAC;EAC7EkB,MAAM,CAACC,OAAO,CAACF,iBAAiB,IAAI,CAAC,CAAC,CAAC,CAACG,OAAO,CAAC,CAAC,CAACC,GAAG,EAAEC,MAAM,CAAC,KAAK;IACjE,MAAM;MACJd,IAAI;MACJE,GAAG;MACHE;IACF,CAAC,GAAGV,aAAa,CAACoB,MAAM,EAAEjB,YAAY,CAAC;IACvCS,SAAS,GAAGb,SAAS,CAACa,SAAS,EAAEF,gBAAgB,CAAC;IAClDG,eAAe,CAACM,GAAG,CAAC,GAAG;MACrBX,GAAG;MACHF;IACF,CAAC;EACH,CAAC,CAAC;EACF,IAAIQ,KAAK,EAAE;IACT;IACA,MAAM;MACJN,GAAG;MACHF,IAAI;MACJI;IACF,CAAC,GAAGV,aAAa,CAACc,KAAK,EAAEX,YAAY,CAAC;IACtCS,SAAS,GAAGb,SAAS,CAACa,SAAS,EAAEF,gBAAgB,CAAC;IAClDG,eAAe,CAACC,KAAK,GAAG;MACtBN,GAAG;MACHF;IACF,CAAC;EACH;EACA,MAAMe,eAAe,GAAGC,WAAW,IAAI;IACrC,IAAI,CAACA,WAAW,EAAE;MAChB,OAAO;QACLd,GAAG,EAAEb,QAAQ,CAAC,CAAC,CAAC,EAAEc,OAAO,CAAC;QAC1BH,IAAI,EAAEC;MACR,CAAC;IACH;IACA,OAAO;MACLC,GAAG,EAAEb,QAAQ,CAAC,CAAC,CAAC,EAAEkB,eAAe,CAACS,WAAW,CAAC,CAACd,GAAG,CAAC;MACnDF,IAAI,EAAEO,eAAe,CAACS,WAAW,CAAC,CAAChB;IACrC,CAAC;EACH,CAAC;EACD,OAAO;IACLA,IAAI,EAAEM,SAAS;IACfS;EACF,CAAC;AACH;AACA,eAAepB,cAAc"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}