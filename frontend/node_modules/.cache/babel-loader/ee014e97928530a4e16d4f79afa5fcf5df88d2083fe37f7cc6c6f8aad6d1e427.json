{"ast": null, "code": "'use client';\n\nexport { useBadge } from './useBadge';\nexport * from './useBadge.types';", "map": {"version": 3, "names": ["useBadge"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/base/useBadge/index.js"], "sourcesContent": ["'use client';\n\nexport { useBadge } from './useBadge';\nexport * from './useBadge.types';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,QAAQ,QAAQ,YAAY;AACrC,cAAc,kBAAkB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}