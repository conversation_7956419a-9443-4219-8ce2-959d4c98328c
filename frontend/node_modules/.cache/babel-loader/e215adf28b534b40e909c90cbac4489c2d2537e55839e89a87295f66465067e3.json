{"ast": null, "code": "import { generateUtilityClass } from '../generateUtilityClass';\nimport { generateUtilityClasses } from '../generateUtilityClasses';\nexport function getMenuUtilityClass(slot) {\n  return generateUtilityClass('MuiMenu', slot);\n}\nexport const menuClasses = generateUtilityClasses('MuiMenu', ['root', 'listbox', 'expanded']);", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getMenuUtilityClass", "slot", "menuClasses"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/base/Menu/menuClasses.js"], "sourcesContent": ["import { generateUtilityClass } from '../generateUtilityClass';\nimport { generateUtilityClasses } from '../generateUtilityClasses';\nexport function getMenuUtilityClass(slot) {\n  return generateUtilityClass('MuiMenu', slot);\n}\nexport const menuClasses = generateUtilityClasses('MuiMenu', ['root', 'listbox', 'expanded']);"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,yBAAyB;AAC9D,SAASC,sBAAsB,QAAQ,2BAA2B;AAClE,OAAO,SAASC,mBAAmBA,CAACC,IAAI,EAAE;EACxC,OAAOH,oBAAoB,CAAC,SAAS,EAAEG,IAAI,CAAC;AAC9C;AACA,OAAO,MAAMC,WAAW,GAAGH,sBAAsB,CAAC,SAAS,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}