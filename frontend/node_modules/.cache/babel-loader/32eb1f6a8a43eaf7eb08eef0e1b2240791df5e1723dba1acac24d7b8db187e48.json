{"ast": null, "code": "/**\n * WARNING: Don't import this directly.\n * Use `MuiError` from `@mui/utils/macros/MuiError.macro` instead.\n * @param {number} code\n */\nexport default function formatMuiErrorMessage(code) {\n  // Apply babel-plugin-transform-template-literals in loose mode\n  // loose mode is safe iff we're concatenating primitives\n  // see https://babeljs.io/docs/en/babel-plugin-transform-template-literals#loose\n  /* eslint-disable prefer-template */\n  let url = 'https://mui.com/production-error/?code=' + code;\n  for (let i = 1; i < arguments.length; i += 1) {\n    // rest params over-transpile for this case\n    // eslint-disable-next-line prefer-rest-params\n    url += '&args[]=' + encodeURIComponent(arguments[i]);\n  }\n  return 'Minified MUI error #' + code + '; visit ' + url + ' for the full message.';\n  /* eslint-enable prefer-template */\n}", "map": {"version": 3, "names": ["formatMuiErrorMessage", "code", "url", "i", "arguments", "length", "encodeURIComponent"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/utils/esm/formatMuiErrorMessage.js"], "sourcesContent": ["/**\n * WARNING: Don't import this directly.\n * Use `MuiError` from `@mui/utils/macros/MuiError.macro` instead.\n * @param {number} code\n */\nexport default function formatMuiErrorMessage(code) {\n  // Apply babel-plugin-transform-template-literals in loose mode\n  // loose mode is safe iff we're concatenating primitives\n  // see https://babeljs.io/docs/en/babel-plugin-transform-template-literals#loose\n  /* eslint-disable prefer-template */\n  let url = 'https://mui.com/production-error/?code=' + code;\n  for (let i = 1; i < arguments.length; i += 1) {\n    // rest params over-transpile for this case\n    // eslint-disable-next-line prefer-rest-params\n    url += '&args[]=' + encodeURIComponent(arguments[i]);\n  }\n  return 'Minified MUI error #' + code + '; visit ' + url + ' for the full message.';\n  /* eslint-enable prefer-template */\n}"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASA,qBAAqBA,CAACC,IAAI,EAAE;EAClD;EACA;EACA;EACA;EACA,IAAIC,GAAG,GAAG,yCAAyC,GAAGD,IAAI;EAC1D,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,IAAI,CAAC,EAAE;IAC5C;IACA;IACAD,GAAG,IAAI,UAAU,GAAGI,kBAAkB,CAACF,SAAS,CAACD,CAAC,CAAC,CAAC;EACtD;EACA,OAAO,sBAAsB,GAAGF,IAAI,GAAG,UAAU,GAAGC,GAAG,GAAG,wBAAwB;EAClF;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}