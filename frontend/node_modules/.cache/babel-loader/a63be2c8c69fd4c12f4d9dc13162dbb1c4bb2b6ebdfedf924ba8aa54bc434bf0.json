{"ast": null, "code": "import { unstable_generateUtilityClasses as generateUtilityClasses } from '@mui/utils';\nimport generateUtilityClass from '../generateUtilityClass';\nexport function getStepLabelUtilityClass(slot) {\n  return generateUtilityClass('MuiStepLabel', slot);\n}\nconst stepLabelClasses = generateUtilityClasses('MuiStepLabel', ['root', 'horizontal', 'vertical', 'label', 'active', 'completed', 'error', 'disabled', 'iconContainer', 'alternativeLabel', 'labelContainer']);\nexport default stepLabelClasses;", "map": {"version": 3, "names": ["unstable_generateUtilityClasses", "generateUtilityClasses", "generateUtilityClass", "getStepLabelUtilityClass", "slot", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/material/StepLabel/stepLabelClasses.js"], "sourcesContent": ["import { unstable_generateUtilityClasses as generateUtilityClasses } from '@mui/utils';\nimport generateUtilityClass from '../generateUtilityClass';\nexport function getStepLabelUtilityClass(slot) {\n  return generateUtilityClass('MuiStepLabel', slot);\n}\nconst stepLabelClasses = generateUtilityClasses('MuiStepLabel', ['root', 'horizontal', 'vertical', 'label', 'active', 'completed', 'error', 'disabled', 'iconContainer', 'alternativeLabel', 'labelContainer']);\nexport default stepLabelClasses;"], "mappings": "AAAA,SAASA,+BAA+B,IAAIC,sBAAsB,QAAQ,YAAY;AACtF,OAAOC,oBAAoB,MAAM,yBAAyB;AAC1D,OAAO,SAASC,wBAAwBA,CAACC,IAAI,EAAE;EAC7C,OAAOF,oBAAoB,CAAC,cAAc,EAAEE,IAAI,CAAC;AACnD;AACA,MAAMC,gBAAgB,GAAGJ,sBAAsB,CAAC,cAAc,EAAE,CAAC,MAAM,EAAE,YAAY,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,OAAO,EAAE,UAAU,EAAE,eAAe,EAAE,kBAAkB,EAAE,gBAAgB,CAAC,CAAC;AAC/M,eAAeI,gBAAgB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}