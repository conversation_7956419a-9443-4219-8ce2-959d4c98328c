{"ast": null, "code": "'use client';\n\nexport { Popper } from './Popper';\nexport { popperClasses, getPopperUtilityClass } from './popperClasses';", "map": {"version": 3, "names": ["<PERSON><PERSON>", "popperClasses", "getPopperUtilityClass"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/base/Popper/index.js"], "sourcesContent": ["'use client';\n\nexport { Popper } from './Popper';\nexport { popperClasses, getPopperUtilityClass } from './popperClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,MAAM,QAAQ,UAAU;AACjC,SAASC,aAAa,EAAEC,qBAAqB,QAAQ,iBAAiB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}