{"ast": null, "code": "import { formatMuiErrorMessage as _formatMuiErrorMessage } from \"@mui/utils\";\nexport default function withTheme() {\n  throw new Error(process.env.NODE_ENV !== \"production\" ? `MUI: withTheme is no longer exported from @mui/material/styles.\nYou have to import it from @mui/styles.\nSee https://mui.com/r/migration-v4/#mui-material-styles for more details.` : _formatMuiErrorMessage(16));\n}", "map": {"version": 3, "names": ["formatMuiErrorMessage", "_formatMuiErrorMessage", "withTheme", "Error", "process", "env", "NODE_ENV"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/material/styles/withTheme.js"], "sourcesContent": ["import { formatMuiErrorMessage as _formatMuiErrorMessage } from \"@mui/utils\";\nexport default function withTheme() {\n  throw new Error(process.env.NODE_ENV !== \"production\" ? `MUI: withTheme is no longer exported from @mui/material/styles.\nYou have to import it from @mui/styles.\nSee https://mui.com/r/migration-v4/#mui-material-styles for more details.` : _formatMuiErrorMessage(16));\n}"], "mappings": "AAAA,SAASA,qBAAqB,IAAIC,sBAAsB,QAAQ,YAAY;AAC5E,eAAe,SAASC,SAASA,CAAA,EAAG;EAClC,MAAM,IAAIC,KAAK,CAACC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAI;AAC3D;AACA,0EAA0E,GAAGL,sBAAsB,CAAC,EAAE,CAAC,CAAC;AACxG"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}