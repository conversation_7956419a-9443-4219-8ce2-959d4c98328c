{"ast": null, "code": "import { unstable_createChainedFunction as createChainedFunction } from '@mui/utils';\nexport default createChainedFunction;", "map": {"version": 3, "names": ["unstable_createChainedFunction", "createChainedFunction"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/material/utils/createChainedFunction.js"], "sourcesContent": ["import { unstable_createChainedFunction as createChainedFunction } from '@mui/utils';\nexport default createChainedFunction;"], "mappings": "AAAA,SAASA,8BAA8B,IAAIC,qBAAqB,QAAQ,YAAY;AACpF,eAAeA,qBAAqB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}