{"ast": null, "code": "'use client';\n\nimport { unstable_useId as useId } from '@mui/utils';\nexport default useId;", "map": {"version": 3, "names": ["unstable_useId", "useId"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/material/utils/useId.js"], "sourcesContent": ["'use client';\n\nimport { unstable_useId as useId } from '@mui/utils';\nexport default useId;"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,cAAc,IAAIC,KAAK,QAAQ,YAAY;AACpD,eAAeA,KAAK"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}