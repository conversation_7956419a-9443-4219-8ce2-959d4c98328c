{"ast": null, "code": "'use client';\n\nimport PropTypes from 'prop-types';\nimport { unstable_ClassNameGenerator as ClassNameGenerator } from '@mui/utils';\nimport createBox from '../createBox';\nimport boxClasses from './boxClasses';\nconst Box = createBox({\n  defaultClassName: boxClasses.root,\n  generateClassName: ClassNameGenerator.generate\n});\nprocess.env.NODE_ENV !== \"production\" ? Box.propTypes /* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit the d.ts file and run \"yarn proptypes\"     |\n  // ----------------------------------------------------------------------\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default Box;", "map": {"version": 3, "names": ["PropTypes", "unstable_ClassNameGenerator", "ClassNameGenerator", "createBox", "boxClasses", "Box", "defaultClassName", "root", "generateClassName", "generate", "process", "env", "NODE_ENV", "propTypes", "children", "node", "component", "elementType", "sx", "oneOfType", "arrayOf", "func", "object", "bool"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/system/esm/Box/Box.js"], "sourcesContent": ["'use client';\n\nimport PropTypes from 'prop-types';\nimport { unstable_ClassNameGenerator as ClassNameGenerator } from '@mui/utils';\nimport createBox from '../createBox';\nimport boxClasses from './boxClasses';\nconst Box = createBox({\n  defaultClassName: boxClasses.root,\n  generateClassName: ClassNameGenerator.generate\n});\nprocess.env.NODE_ENV !== \"production\" ? Box.propTypes /* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit the d.ts file and run \"yarn proptypes\"     |\n  // ----------------------------------------------------------------------\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default Box;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,SAAS,MAAM,YAAY;AAClC,SAASC,2BAA2B,IAAIC,kBAAkB,QAAQ,YAAY;AAC9E,OAAOC,SAAS,MAAM,cAAc;AACpC,OAAOC,UAAU,MAAM,cAAc;AACrC,MAAMC,GAAG,GAAGF,SAAS,CAAC;EACpBG,gBAAgB,EAAEF,UAAU,CAACG,IAAI;EACjCC,iBAAiB,EAAEN,kBAAkB,CAACO;AACxC,CAAC,CAAC;AACFC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGP,GAAG,CAACQ,SAAS,CAAC,yBAAyB;EAC7E;EACA;EACA;EACA;EACA;AACF;AACA;EACEC,QAAQ,EAAEd,SAAS,CAACe,IAAI;EACxB;AACF;AACA;AACA;EACEC,SAAS,EAAEhB,SAAS,CAACiB,WAAW;EAChC;AACF;AACA;EACEC,EAAE,EAAElB,SAAS,CAACmB,SAAS,CAAC,CAACnB,SAAS,CAACoB,OAAO,CAACpB,SAAS,CAACmB,SAAS,CAAC,CAACnB,SAAS,CAACqB,IAAI,EAAErB,SAAS,CAACsB,MAAM,EAAEtB,SAAS,CAACuB,IAAI,CAAC,CAAC,CAAC,EAAEvB,SAAS,CAACqB,IAAI,EAAErB,SAAS,CAACsB,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAejB,GAAG"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}