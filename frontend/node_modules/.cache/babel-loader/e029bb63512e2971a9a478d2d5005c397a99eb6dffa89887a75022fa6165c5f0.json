{"ast": null, "code": "import { unstable_ownerWindow as ownerWindow } from '@mui/utils';\nexport default ownerWindow;", "map": {"version": 3, "names": ["unstable_ownerW<PERSON>ow", "ownerWindow"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/material/utils/ownerWindow.js"], "sourcesContent": ["import { unstable_ownerWindow as ownerWindow } from '@mui/utils';\nexport default ownerWindow;"], "mappings": "AAAA,SAASA,oBAAoB,IAAIC,WAAW,QAAQ,YAAY;AAChE,eAAeA,WAAW"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}