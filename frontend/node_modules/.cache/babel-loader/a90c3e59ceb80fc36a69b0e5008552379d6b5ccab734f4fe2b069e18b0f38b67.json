{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"badgeContent\", \"children\", \"invisible\", \"max\", \"slotProps\", \"slots\", \"showZero\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { unstable_composeClasses as composeClasses } from '../composeClasses';\nimport { useBadge } from '../useBadge';\nimport { getBadgeUtilityClass } from './badgeClasses';\nimport { useSlotProps } from '../utils';\nimport { useClassNamesOverride } from '../utils/ClassNameConfigurator';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    invisible\n  } = ownerState;\n  const slots = {\n    root: ['root'],\n    badge: ['badge', invisible && 'invisible']\n  };\n  return composeClasses(slots, useClassNamesOverride(getBadgeUtilityClass));\n};\n/**\n *\n * Demos:\n *\n * - [Badge](https://mui.com/base-ui/react-badge/)\n *\n * API:\n *\n * - [Badge API](https://mui.com/base-ui/react-badge/components-api/#badge)\n */\nconst Badge = /*#__PURE__*/React.forwardRef(function Badge(props, forwardedRef) {\n  var _slots$root, _slots$badge;\n  const {\n      children,\n      max: maxProp = 99,\n      slotProps = {},\n      slots = {},\n      showZero = false\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    badgeContent,\n    max,\n    displayValue,\n    invisible\n  } = useBadge(_extends({}, props, {\n    max: maxProp\n  }));\n  const ownerState = _extends({}, props, {\n    badgeContent,\n    invisible,\n    max,\n    showZero\n  });\n  const classes = useUtilityClasses(ownerState);\n  const Root = (_slots$root = slots.root) != null ? _slots$root : 'span';\n  const rootProps = useSlotProps({\n    elementType: Root,\n    externalSlotProps: slotProps.root,\n    externalForwardedProps: other,\n    additionalProps: {\n      ref: forwardedRef\n    },\n    ownerState,\n    className: classes.root\n  });\n  const BadgeComponent = (_slots$badge = slots.badge) != null ? _slots$badge : 'span';\n  const badgeProps = useSlotProps({\n    elementType: BadgeComponent,\n    externalSlotProps: slotProps.badge,\n    ownerState,\n    className: classes.badge\n  });\n  return /*#__PURE__*/_jsxs(Root, _extends({}, rootProps, {\n    children: [children, /*#__PURE__*/_jsx(BadgeComponent, _extends({}, badgeProps, {\n      children: displayValue\n    }))]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? Badge.propTypes /* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * The content rendered within the badge.\n   */\n  badgeContent: PropTypes.node,\n  /**\n   * The badge will be added relative to this node.\n   */\n  children: PropTypes.node,\n  /**\n   * If `true`, the badge is invisible.\n   * @default false\n   */\n  invisible: PropTypes.bool,\n  /**\n   * Max count to show.\n   * @default 99\n   */\n  max: PropTypes.number,\n  /**\n   * Controls whether the badge is hidden when `badgeContent` is zero.\n   * @default false\n   */\n  showZero: PropTypes.bool,\n  /**\n   * The props used for each slot inside the Badge.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    badge: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside the Badge.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    badge: PropTypes.elementType,\n    root: PropTypes.elementType\n  })\n} : void 0;\nexport { Badge };", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "PropTypes", "unstable_composeClasses", "composeClasses", "useBadge", "getBadgeUtilityClass", "useSlotProps", "useClassNamesOverride", "jsx", "_jsx", "jsxs", "_jsxs", "useUtilityClasses", "ownerState", "invisible", "slots", "root", "badge", "Badge", "forwardRef", "props", "forwardedRef", "_slots$root", "_slots$badge", "children", "max", "maxProp", "slotProps", "showZero", "other", "badgeContent", "displayValue", "classes", "Root", "rootProps", "elementType", "externalSlotProps", "externalForwardedProps", "additionalProps", "ref", "className", "BadgeComponent", "badgeProps", "process", "env", "NODE_ENV", "propTypes", "node", "bool", "number", "shape", "oneOfType", "func", "object"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/base/Badge/Badge.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"badgeContent\", \"children\", \"invisible\", \"max\", \"slotProps\", \"slots\", \"showZero\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { unstable_composeClasses as composeClasses } from '../composeClasses';\nimport { useBadge } from '../useBadge';\nimport { getBadgeUtilityClass } from './badgeClasses';\nimport { useSlotProps } from '../utils';\nimport { useClassNamesOverride } from '../utils/ClassNameConfigurator';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    invisible\n  } = ownerState;\n  const slots = {\n    root: ['root'],\n    badge: ['badge', invisible && 'invisible']\n  };\n  return composeClasses(slots, useClassNamesOverride(getBadgeUtilityClass));\n};\n/**\n *\n * Demos:\n *\n * - [Badge](https://mui.com/base-ui/react-badge/)\n *\n * API:\n *\n * - [Badge API](https://mui.com/base-ui/react-badge/components-api/#badge)\n */\nconst Badge = /*#__PURE__*/React.forwardRef(function Badge(props, forwardedRef) {\n  var _slots$root, _slots$badge;\n  const {\n      children,\n      max: maxProp = 99,\n      slotProps = {},\n      slots = {},\n      showZero = false\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    badgeContent,\n    max,\n    displayValue,\n    invisible\n  } = useBadge(_extends({}, props, {\n    max: maxProp\n  }));\n  const ownerState = _extends({}, props, {\n    badgeContent,\n    invisible,\n    max,\n    showZero\n  });\n  const classes = useUtilityClasses(ownerState);\n  const Root = (_slots$root = slots.root) != null ? _slots$root : 'span';\n  const rootProps = useSlotProps({\n    elementType: Root,\n    externalSlotProps: slotProps.root,\n    externalForwardedProps: other,\n    additionalProps: {\n      ref: forwardedRef\n    },\n    ownerState,\n    className: classes.root\n  });\n  const BadgeComponent = (_slots$badge = slots.badge) != null ? _slots$badge : 'span';\n  const badgeProps = useSlotProps({\n    elementType: BadgeComponent,\n    externalSlotProps: slotProps.badge,\n    ownerState,\n    className: classes.badge\n  });\n  return /*#__PURE__*/_jsxs(Root, _extends({}, rootProps, {\n    children: [children, /*#__PURE__*/_jsx(BadgeComponent, _extends({}, badgeProps, {\n      children: displayValue\n    }))]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? Badge.propTypes /* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * The content rendered within the badge.\n   */\n  badgeContent: PropTypes.node,\n  /**\n   * The badge will be added relative to this node.\n   */\n  children: PropTypes.node,\n  /**\n   * If `true`, the badge is invisible.\n   * @default false\n   */\n  invisible: PropTypes.bool,\n  /**\n   * Max count to show.\n   * @default 99\n   */\n  max: PropTypes.number,\n  /**\n   * Controls whether the badge is hidden when `badgeContent` is zero.\n   * @default false\n   */\n  showZero: PropTypes.bool,\n  /**\n   * The props used for each slot inside the Badge.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    badge: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside the Badge.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    badge: PropTypes.elementType,\n    root: PropTypes.elementType\n  })\n} : void 0;\nexport { Badge };"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,cAAc,EAAE,UAAU,EAAE,WAAW,EAAE,KAAK,EAAE,WAAW,EAAE,OAAO,EAAE,UAAU,CAAC;AACpG,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,uBAAuB,IAAIC,cAAc,QAAQ,mBAAmB;AAC7E,SAASC,QAAQ,QAAQ,aAAa;AACtC,SAASC,oBAAoB,QAAQ,gBAAgB;AACrD,SAASC,YAAY,QAAQ,UAAU;AACvC,SAASC,qBAAqB,QAAQ,gCAAgC;AACtE,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC;EACF,CAAC,GAAGD,UAAU;EACd,MAAME,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,CAAC;IACdC,KAAK,EAAE,CAAC,OAAO,EAAEH,SAAS,IAAI,WAAW;EAC3C,CAAC;EACD,OAAOX,cAAc,CAACY,KAAK,EAAER,qBAAqB,CAACF,oBAAoB,CAAC,CAAC;AAC3E,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMa,KAAK,GAAG,aAAalB,KAAK,CAACmB,UAAU,CAAC,SAASD,KAAKA,CAACE,KAAK,EAAEC,YAAY,EAAE;EAC9E,IAAIC,WAAW,EAAEC,YAAY;EAC7B,MAAM;MACFC,QAAQ;MACRC,GAAG,EAAEC,OAAO,GAAG,EAAE;MACjBC,SAAS,GAAG,CAAC,CAAC;MACdZ,KAAK,GAAG,CAAC,CAAC;MACVa,QAAQ,GAAG;IACb,CAAC,GAAGR,KAAK;IACTS,KAAK,GAAG/B,6BAA6B,CAACsB,KAAK,EAAErB,SAAS,CAAC;EACzD,MAAM;IACJ+B,YAAY;IACZL,GAAG;IACHM,YAAY;IACZjB;EACF,CAAC,GAAGV,QAAQ,CAACP,QAAQ,CAAC,CAAC,CAAC,EAAEuB,KAAK,EAAE;IAC/BK,GAAG,EAAEC;EACP,CAAC,CAAC,CAAC;EACH,MAAMb,UAAU,GAAGhB,QAAQ,CAAC,CAAC,CAAC,EAAEuB,KAAK,EAAE;IACrCU,YAAY;IACZhB,SAAS;IACTW,GAAG;IACHG;EACF,CAAC,CAAC;EACF,MAAMI,OAAO,GAAGpB,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAMoB,IAAI,GAAG,CAACX,WAAW,GAAGP,KAAK,CAACC,IAAI,KAAK,IAAI,GAAGM,WAAW,GAAG,MAAM;EACtE,MAAMY,SAAS,GAAG5B,YAAY,CAAC;IAC7B6B,WAAW,EAAEF,IAAI;IACjBG,iBAAiB,EAAET,SAAS,CAACX,IAAI;IACjCqB,sBAAsB,EAAER,KAAK;IAC7BS,eAAe,EAAE;MACfC,GAAG,EAAElB;IACP,CAAC;IACDR,UAAU;IACV2B,SAAS,EAAER,OAAO,CAAChB;EACrB,CAAC,CAAC;EACF,MAAMyB,cAAc,GAAG,CAAClB,YAAY,GAAGR,KAAK,CAACE,KAAK,KAAK,IAAI,GAAGM,YAAY,GAAG,MAAM;EACnF,MAAMmB,UAAU,GAAGpC,YAAY,CAAC;IAC9B6B,WAAW,EAAEM,cAAc;IAC3BL,iBAAiB,EAAET,SAAS,CAACV,KAAK;IAClCJ,UAAU;IACV2B,SAAS,EAAER,OAAO,CAACf;EACrB,CAAC,CAAC;EACF,OAAO,aAAaN,KAAK,CAACsB,IAAI,EAAEpC,QAAQ,CAAC,CAAC,CAAC,EAAEqC,SAAS,EAAE;IACtDV,QAAQ,EAAE,CAACA,QAAQ,EAAE,aAAaf,IAAI,CAACgC,cAAc,EAAE5C,QAAQ,CAAC,CAAC,CAAC,EAAE6C,UAAU,EAAE;MAC9ElB,QAAQ,EAAEO;IACZ,CAAC,CAAC,CAAC;EACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFY,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG3B,KAAK,CAAC4B,SAAS,CAAC,yBAAyB;EAC/E;EACA;EACA;EACA;EACA;AACF;AACA;EACEhB,YAAY,EAAE7B,SAAS,CAAC8C,IAAI;EAC5B;AACF;AACA;EACEvB,QAAQ,EAAEvB,SAAS,CAAC8C,IAAI;EACxB;AACF;AACA;AACA;EACEjC,SAAS,EAAEb,SAAS,CAAC+C,IAAI;EACzB;AACF;AACA;AACA;EACEvB,GAAG,EAAExB,SAAS,CAACgD,MAAM;EACrB;AACF;AACA;AACA;EACErB,QAAQ,EAAE3B,SAAS,CAAC+C,IAAI;EACxB;AACF;AACA;AACA;EACErB,SAAS,EAAE1B,SAAS,CAACiD,KAAK,CAAC;IACzBjC,KAAK,EAAEhB,SAAS,CAACkD,SAAS,CAAC,CAAClD,SAAS,CAACmD,IAAI,EAAEnD,SAAS,CAACoD,MAAM,CAAC,CAAC;IAC9DrC,IAAI,EAAEf,SAAS,CAACkD,SAAS,CAAC,CAAClD,SAAS,CAACmD,IAAI,EAAEnD,SAAS,CAACoD,MAAM,CAAC;EAC9D,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;EACEtC,KAAK,EAAEd,SAAS,CAACiD,KAAK,CAAC;IACrBjC,KAAK,EAAEhB,SAAS,CAACkC,WAAW;IAC5BnB,IAAI,EAAEf,SAAS,CAACkC;EAClB,CAAC;AACH,CAAC,GAAG,KAAK,CAAC;AACV,SAASjB,KAAK"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}