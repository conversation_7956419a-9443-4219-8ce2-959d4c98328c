{"ast": null, "code": "export { unstable_generateUtilityClasses as generateUtilityClasses } from '@mui/utils';", "map": {"version": 3, "names": ["unstable_generateUtilityClasses", "generateUtilityClasses"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/base/generateUtilityClasses/index.js"], "sourcesContent": ["export { unstable_generateUtilityClasses as generateUtilityClasses } from '@mui/utils';"], "mappings": "AAAA,SAASA,+BAA+B,IAAIC,sBAAsB,QAAQ,YAAY"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}