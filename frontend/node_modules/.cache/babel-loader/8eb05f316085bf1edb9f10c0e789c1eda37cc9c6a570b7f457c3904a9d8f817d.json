{"ast": null, "code": "import { unstable_capitalize as capitalize } from '@mui/utils';\nexport default capitalize;", "map": {"version": 3, "names": ["unstable_capitalize", "capitalize"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/material/utils/capitalize.js"], "sourcesContent": ["import { unstable_capitalize as capitalize } from '@mui/utils';\nexport default capitalize;"], "mappings": "AAAA,SAASA,mBAAmB,IAAIC,UAAU,QAAQ,YAAY;AAC9D,eAAeA,UAAU"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}