{"ast": null, "code": "export { unstable_composeClasses } from '@mui/utils';", "map": {"version": 3, "names": ["unstable_composeClasses"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/base/composeClasses/index.js"], "sourcesContent": ["export { unstable_composeClasses } from '@mui/utils';"], "mappings": "AAAA,SAASA,uBAAuB,QAAQ,YAAY"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}