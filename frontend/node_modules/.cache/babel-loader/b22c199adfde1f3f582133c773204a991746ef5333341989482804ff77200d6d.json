{"ast": null, "code": "export * from './Dropdown';\nexport * from './Dropdown.types';", "map": {"version": 3, "names": [], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/base/Dropdown/index.js"], "sourcesContent": ["export * from './Dropdown';\nexport * from './Dropdown.types';"], "mappings": "AAAA,cAAc,YAAY;AAC1B,cAAc,kBAAkB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}