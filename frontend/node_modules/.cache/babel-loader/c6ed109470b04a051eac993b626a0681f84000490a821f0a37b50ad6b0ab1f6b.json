{"ast": null, "code": "import { alpha, getPath } from '@mui/system';\nexport const colorTransformations = {\n  primary: 'primary.main',\n  textPrimary: 'text.primary',\n  secondary: 'secondary.main',\n  textSecondary: 'text.secondary',\n  error: 'error.main'\n};\nconst transformDeprecatedColors = color => {\n  return colorTransformations[color] || color;\n};\nconst getTextDecoration = ({\n  theme,\n  ownerState\n}) => {\n  const transformedColor = transformDeprecatedColors(ownerState.color);\n  const color = getPath(theme, `palette.${transformedColor}`, false) || ownerState.color;\n  const channelColor = getPath(theme, `palette.${transformedColor}Channel`);\n  if ('vars' in theme && channelColor) {\n    return `rgba(${channelColor} / 0.4)`;\n  }\n  return alpha(color, 0.4);\n};\nexport default getTextDecoration;", "map": {"version": 3, "names": ["alpha", "<PERSON><PERSON><PERSON>", "colorTransformations", "primary", "textPrimary", "secondary", "textSecondary", "error", "transformDeprecatedColors", "color", "getTextDecoration", "theme", "ownerState", "transformedColor", "channelColor"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/material/Link/getTextDecoration.js"], "sourcesContent": ["import { alpha, getPath } from '@mui/system';\nexport const colorTransformations = {\n  primary: 'primary.main',\n  textPrimary: 'text.primary',\n  secondary: 'secondary.main',\n  textSecondary: 'text.secondary',\n  error: 'error.main'\n};\nconst transformDeprecatedColors = color => {\n  return colorTransformations[color] || color;\n};\nconst getTextDecoration = ({\n  theme,\n  ownerState\n}) => {\n  const transformedColor = transformDeprecatedColors(ownerState.color);\n  const color = getPath(theme, `palette.${transformedColor}`, false) || ownerState.color;\n  const channelColor = getPath(theme, `palette.${transformedColor}Channel`);\n  if ('vars' in theme && channelColor) {\n    return `rgba(${channelColor} / 0.4)`;\n  }\n  return alpha(color, 0.4);\n};\nexport default getTextDecoration;"], "mappings": "AAAA,SAASA,KAAK,EAAEC,OAAO,QAAQ,aAAa;AAC5C,OAAO,MAAMC,oBAAoB,GAAG;EAClCC,OAAO,EAAE,cAAc;EACvBC,WAAW,EAAE,cAAc;EAC3BC,SAAS,EAAE,gBAAgB;EAC3BC,aAAa,EAAE,gBAAgB;EAC/BC,KAAK,EAAE;AACT,CAAC;AACD,MAAMC,yBAAyB,GAAGC,KAAK,IAAI;EACzC,OAAOP,oBAAoB,CAACO,KAAK,CAAC,IAAIA,KAAK;AAC7C,CAAC;AACD,MAAMC,iBAAiB,GAAGA,CAAC;EACzBC,KAAK;EACLC;AACF,CAAC,KAAK;EACJ,MAAMC,gBAAgB,GAAGL,yBAAyB,CAACI,UAAU,CAACH,KAAK,CAAC;EACpE,MAAMA,KAAK,GAAGR,OAAO,CAACU,KAAK,EAAG,WAAUE,gBAAiB,EAAC,EAAE,KAAK,CAAC,IAAID,UAAU,CAACH,KAAK;EACtF,MAAMK,YAAY,GAAGb,OAAO,CAACU,KAAK,EAAG,WAAUE,gBAAiB,SAAQ,CAAC;EACzE,IAAI,MAAM,IAAIF,KAAK,IAAIG,YAAY,EAAE;IACnC,OAAQ,QAAOA,YAAa,SAAQ;EACtC;EACA,OAAOd,KAAK,CAACS,KAAK,EAAE,GAAG,CAAC;AAC1B,CAAC;AACD,eAAeC,iBAAiB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}