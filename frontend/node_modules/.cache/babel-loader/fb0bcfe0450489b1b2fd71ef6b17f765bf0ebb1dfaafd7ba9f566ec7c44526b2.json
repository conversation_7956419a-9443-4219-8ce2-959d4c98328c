{"ast": null, "code": "'use client';\n\nexport { Popup as Unstable_Popup } from './Popup';\nexport * from './Popup.types';\nexport * from './popupClasses';", "map": {"version": 3, "names": ["Popup", "Unstable_Popup"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/base/Unstable_Popup/index.js"], "sourcesContent": ["'use client';\n\nexport { Popup as Unstable_Popup } from './Popup';\nexport * from './Popup.types';\nexport * from './popupClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,KAAK,IAAIC,cAAc,QAAQ,SAAS;AACjD,cAAc,eAAe;AAC7B,cAAc,gBAAgB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}