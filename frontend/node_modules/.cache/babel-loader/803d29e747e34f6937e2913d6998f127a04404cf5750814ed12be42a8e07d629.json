{"ast": null, "code": "'use client';\n\nexport { default } from './ThemeProvider';", "map": {"version": 3, "names": ["default"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/system/esm/ThemeProvider/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './ThemeProvider';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,iBAAiB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}