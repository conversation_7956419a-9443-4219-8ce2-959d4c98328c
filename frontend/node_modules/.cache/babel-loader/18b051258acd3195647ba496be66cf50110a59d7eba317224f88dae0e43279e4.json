{"ast": null, "code": "import { generateUtilityClass } from '../generateUtilityClass';\nimport { generateUtilityClasses } from '../generateUtilityClasses';\nexport function getTablePaginationUtilityClass(slot) {\n  return generateUtilityClass('MuiTablePagination', slot);\n}\nexport const tablePaginationClasses = generateUtilityClasses('MuiTablePagination', ['root', 'toolbar', 'spacer', 'selectLabel', 'selectRoot', 'select', 'selectIcon', 'input', 'menuItem', 'displayedRows', 'actions']);", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getTablePaginationUtilityClass", "slot", "tablePaginationClasses"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/base/TablePagination/tablePaginationClasses.js"], "sourcesContent": ["import { generateUtilityClass } from '../generateUtilityClass';\nimport { generateUtilityClasses } from '../generateUtilityClasses';\nexport function getTablePaginationUtilityClass(slot) {\n  return generateUtilityClass('MuiTablePagination', slot);\n}\nexport const tablePaginationClasses = generateUtilityClasses('MuiTablePagination', ['root', 'toolbar', 'spacer', 'selectLabel', 'selectRoot', 'select', 'selectIcon', 'input', 'menuItem', 'displayedRows', 'actions']);"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,yBAAyB;AAC9D,SAASC,sBAAsB,QAAQ,2BAA2B;AAClE,OAAO,SAASC,8BAA8BA,CAACC,IAAI,EAAE;EACnD,OAAOH,oBAAoB,CAAC,oBAAoB,EAAEG,IAAI,CAAC;AACzD;AACA,OAAO,MAAMC,sBAAsB,GAAGH,sBAAsB,CAAC,oBAAoB,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,aAAa,EAAE,YAAY,EAAE,QAAQ,EAAE,YAAY,EAAE,OAAO,EAAE,UAAU,EAAE,eAAe,EAAE,SAAS,CAAC,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}