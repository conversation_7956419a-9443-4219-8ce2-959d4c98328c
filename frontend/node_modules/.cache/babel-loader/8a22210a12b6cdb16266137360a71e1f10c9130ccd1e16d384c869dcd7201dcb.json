{"ast": null, "code": "'use client';\n\nimport { unstable_useEnhancedEffect as useEnhancedEffect } from '@mui/utils';\nexport default useEnhancedEffect;", "map": {"version": 3, "names": ["unstable_useEnhancedEffect", "useEnhancedEffect"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/material/utils/useEnhancedEffect.js"], "sourcesContent": ["'use client';\n\nimport { unstable_useEnhancedEffect as useEnhancedEffect } from '@mui/utils';\nexport default useEnhancedEffect;"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,0BAA0B,IAAIC,iBAAiB,QAAQ,YAAY;AAC5E,eAAeA,iBAAiB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}