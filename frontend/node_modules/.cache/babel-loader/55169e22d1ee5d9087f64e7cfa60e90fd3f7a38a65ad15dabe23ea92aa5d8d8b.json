{"ast": null, "code": "'use client';\n\nimport { unstable_useForkRef as useForkRef } from '@mui/utils';\nexport default useForkRef;", "map": {"version": 3, "names": ["unstable_useForkRef", "useForkRef"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/material/utils/useForkRef.js"], "sourcesContent": ["'use client';\n\nimport { unstable_useForkRef as useForkRef } from '@mui/utils';\nexport default useForkRef;"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,mBAAmB,IAAIC,UAAU,QAAQ,YAAY;AAC9D,eAAeA,UAAU"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}