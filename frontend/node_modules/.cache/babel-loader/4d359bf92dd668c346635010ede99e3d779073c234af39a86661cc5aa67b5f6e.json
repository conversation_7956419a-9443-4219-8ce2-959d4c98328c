{"ast": null, "code": "export { default } from './HTMLElementType';", "map": {"version": 3, "names": ["default"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/utils/esm/HTMLElementType/index.js"], "sourcesContent": ["export { default } from './HTMLElementType';"], "mappings": "AAAA,SAASA,OAAO,QAAQ,mBAAmB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}