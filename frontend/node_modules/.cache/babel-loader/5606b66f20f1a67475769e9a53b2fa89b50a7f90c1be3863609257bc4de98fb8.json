{"ast": null, "code": "import { unstable_generateUtilityClasses as generateUtilityClasses } from '@mui/utils';\nimport generateUtilityClass from '../generateUtilityClass';\nexport function getSpeedDialUtilityClass(slot) {\n  return generateUtilityClass('MuiSpeedDial', slot);\n}\nconst speedDialClasses = generateUtilityClasses('MuiSpeedDial', ['root', 'fab', 'directionUp', 'directionDown', 'directionLeft', 'directionRight', 'actions', 'actionsClosed']);\nexport default speedDialClasses;", "map": {"version": 3, "names": ["unstable_generateUtilityClasses", "generateUtilityClasses", "generateUtilityClass", "getSpeedDialUtilityClass", "slot", "speedDialClasses"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/material/SpeedDial/speedDialClasses.js"], "sourcesContent": ["import { unstable_generateUtilityClasses as generateUtilityClasses } from '@mui/utils';\nimport generateUtilityClass from '../generateUtilityClass';\nexport function getSpeedDialUtilityClass(slot) {\n  return generateUtilityClass('MuiSpeedDial', slot);\n}\nconst speedDialClasses = generateUtilityClasses('MuiSpeedDial', ['root', 'fab', 'directionUp', 'directionDown', 'directionLeft', 'directionRight', 'actions', 'actionsClosed']);\nexport default speedDialClasses;"], "mappings": "AAAA,SAASA,+BAA+B,IAAIC,sBAAsB,QAAQ,YAAY;AACtF,OAAOC,oBAAoB,MAAM,yBAAyB;AAC1D,OAAO,SAASC,wBAAwBA,CAACC,IAAI,EAAE;EAC7C,OAAOF,oBAAoB,CAAC,cAAc,EAAEE,IAAI,CAAC;AACnD;AACA,MAAMC,gBAAgB,GAAGJ,sBAAsB,CAAC,cAAc,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,eAAe,EAAE,eAAe,EAAE,gBAAgB,EAAE,SAAS,EAAE,eAAe,CAAC,CAAC;AAC/K,eAAeI,gBAAgB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}