{"ast": null, "code": "import { DropdownActionTypes } from './useDropdown.types';\nexport function dropdownReducer(state, action) {\n  switch (action.type) {\n    case DropdownActionTypes.blur:\n      return {\n        open: false\n      };\n    case DropdownActionTypes.escapeKeyDown:\n      return {\n        open: false\n      };\n    case DropdownActionTypes.toggle:\n      return {\n        open: !state.open\n      };\n    case DropdownActionTypes.open:\n      return {\n        open: true\n      };\n    case DropdownActionTypes.close:\n      return {\n        open: false\n      };\n    default:\n      throw new Error(`Unhandled action`);\n  }\n}", "map": {"version": 3, "names": ["DropdownActionTypes", "dropdownReducer", "state", "action", "type", "blur", "open", "escapeKeyDown", "toggle", "close", "Error"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/base/useDropdown/dropdownReducer.js"], "sourcesContent": ["import { DropdownActionTypes } from './useDropdown.types';\nexport function dropdownReducer(state, action) {\n  switch (action.type) {\n    case DropdownActionTypes.blur:\n      return {\n        open: false\n      };\n    case DropdownActionTypes.escapeKeyDown:\n      return {\n        open: false\n      };\n    case DropdownActionTypes.toggle:\n      return {\n        open: !state.open\n      };\n    case DropdownActionTypes.open:\n      return {\n        open: true\n      };\n    case DropdownActionTypes.close:\n      return {\n        open: false\n      };\n    default:\n      throw new Error(`Unhandled action`);\n  }\n}"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,qBAAqB;AACzD,OAAO,SAASC,eAAeA,CAACC,KAAK,EAAEC,MAAM,EAAE;EAC7C,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKJ,mBAAmB,CAACK,IAAI;MAC3B,OAAO;QACLC,IAAI,EAAE;MACR,CAAC;IACH,KAAKN,mBAAmB,CAACO,aAAa;MACpC,OAAO;QACLD,IAAI,EAAE;MACR,CAAC;IACH,KAAKN,mBAAmB,CAACQ,MAAM;MAC7B,OAAO;QACLF,IAAI,EAAE,CAACJ,KAAK,CAACI;MACf,CAAC;IACH,KAAKN,mBAAmB,CAACM,IAAI;MAC3B,OAAO;QACLA,IAAI,EAAE;MACR,CAAC;IACH,KAAKN,mBAAmB,CAACS,KAAK;MAC5B,OAAO;QACLH,IAAI,EAAE;MACR,CAAC;IACH;MACE,MAAM,IAAII,KAAK,CAAE,kBAAiB,CAAC;EACvC;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}