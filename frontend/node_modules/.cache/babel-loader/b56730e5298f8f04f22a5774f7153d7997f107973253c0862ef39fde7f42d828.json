{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nconst TEXT_NAVIGATION_RESET_TIMEOUT = 500; // milliseconds\n\n/**\n * @ignore - internal hook.\n *\n * Provides a handler for text navigation.\n * It's used to navigate a list by typing the first letters of the options.\n *\n * @param callback A function to be called when the navigation should be performed.\n * @returns A function to be used in a keydown event handler.\n */\nexport function useTextNavigation(callback) {\n  const textCriteriaRef = React.useRef({\n    searchString: '',\n    lastTime: null\n  });\n  return React.useCallback(event => {\n    if (event.key.length === 1 && event.key !== ' ') {\n      const textCriteria = textCriteriaRef.current;\n      const lowerKey = event.key.toLowerCase();\n      const currentTime = performance.now();\n      if (textCriteria.searchString.length > 0 && textCriteria.lastTime && currentTime - textCriteria.lastTime > TEXT_NAVIGATION_RESET_TIMEOUT) {\n        textCriteria.searchString = lowerKey;\n      } else if (textCriteria.searchString.length !== 1 || lowerKey !== textCriteria.searchString) {\n        // If there is just one character in the buffer and the key is the same, do not append\n        textCriteria.searchString += lowerKey;\n      }\n      textCriteria.lastTime = currentTime;\n      callback(textCriteria.searchString, event);\n    }\n  }, [callback]);\n}", "map": {"version": 3, "names": ["React", "TEXT_NAVIGATION_RESET_TIMEOUT", "useTextNavigation", "callback", "textCriteriaRef", "useRef", "searchString", "lastTime", "useCallback", "event", "key", "length", "textCriteria", "current", "lowerKey", "toLowerCase", "currentTime", "performance", "now"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/base/utils/useTextNavigation.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nconst TEXT_NAVIGATION_RESET_TIMEOUT = 500; // milliseconds\n\n/**\n * @ignore - internal hook.\n *\n * Provides a handler for text navigation.\n * It's used to navigate a list by typing the first letters of the options.\n *\n * @param callback A function to be called when the navigation should be performed.\n * @returns A function to be used in a keydown event handler.\n */\nexport function useTextNavigation(callback) {\n  const textCriteriaRef = React.useRef({\n    searchString: '',\n    lastTime: null\n  });\n  return React.useCallback(event => {\n    if (event.key.length === 1 && event.key !== ' ') {\n      const textCriteria = textCriteriaRef.current;\n      const lowerKey = event.key.toLowerCase();\n      const currentTime = performance.now();\n      if (textCriteria.searchString.length > 0 && textCriteria.lastTime && currentTime - textCriteria.lastTime > TEXT_NAVIGATION_RESET_TIMEOUT) {\n        textCriteria.searchString = lowerKey;\n      } else if (textCriteria.searchString.length !== 1 || lowerKey !== textCriteria.searchString) {\n        // If there is just one character in the buffer and the key is the same, do not append\n        textCriteria.searchString += lowerKey;\n      }\n      textCriteria.lastTime = currentTime;\n      callback(textCriteria.searchString, event);\n    }\n  }, [callback]);\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,MAAMC,6BAA6B,GAAG,GAAG,CAAC,CAAC;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,iBAAiBA,CAACC,QAAQ,EAAE;EAC1C,MAAMC,eAAe,GAAGJ,KAAK,CAACK,MAAM,CAAC;IACnCC,YAAY,EAAE,EAAE;IAChBC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,OAAOP,KAAK,CAACQ,WAAW,CAACC,KAAK,IAAI;IAChC,IAAIA,KAAK,CAACC,GAAG,CAACC,MAAM,KAAK,CAAC,IAAIF,KAAK,CAACC,GAAG,KAAK,GAAG,EAAE;MAC/C,MAAME,YAAY,GAAGR,eAAe,CAACS,OAAO;MAC5C,MAAMC,QAAQ,GAAGL,KAAK,CAACC,GAAG,CAACK,WAAW,CAAC,CAAC;MACxC,MAAMC,WAAW,GAAGC,WAAW,CAACC,GAAG,CAAC,CAAC;MACrC,IAAIN,YAAY,CAACN,YAAY,CAACK,MAAM,GAAG,CAAC,IAAIC,YAAY,CAACL,QAAQ,IAAIS,WAAW,GAAGJ,YAAY,CAACL,QAAQ,GAAGN,6BAA6B,EAAE;QACxIW,YAAY,CAACN,YAAY,GAAGQ,QAAQ;MACtC,CAAC,MAAM,IAAIF,YAAY,CAACN,YAAY,CAACK,MAAM,KAAK,CAAC,IAAIG,QAAQ,KAAKF,YAAY,CAACN,YAAY,EAAE;QAC3F;QACAM,YAAY,CAACN,YAAY,IAAIQ,QAAQ;MACvC;MACAF,YAAY,CAACL,QAAQ,GAAGS,WAAW;MACnCb,QAAQ,CAACS,YAAY,CAACN,YAAY,EAAEG,KAAK,CAAC;IAC5C;EACF,CAAC,EAAE,CAACN,QAAQ,CAAC,CAAC;AAChB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}