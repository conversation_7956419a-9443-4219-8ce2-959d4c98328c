{"ast": null, "code": "'use client';\n\nexport { default } from './useId';", "map": {"version": 3, "names": ["default"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/utils/esm/useId/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './useId';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,SAAS"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}