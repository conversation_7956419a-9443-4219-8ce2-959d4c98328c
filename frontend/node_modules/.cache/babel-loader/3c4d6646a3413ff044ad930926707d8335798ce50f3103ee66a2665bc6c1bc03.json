{"ast": null, "code": "export { default } from './createTheme';", "map": {"version": 3, "names": ["default"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/system/esm/createTheme/index.js"], "sourcesContent": ["export { default } from './createTheme';"], "mappings": "AAAA,SAASA,OAAO,QAAQ,eAAe"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}