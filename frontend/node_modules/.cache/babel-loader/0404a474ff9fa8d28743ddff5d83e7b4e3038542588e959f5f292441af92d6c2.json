{"ast": null, "code": "import style from './style';\nimport compose from './compose';\nimport { handleBreakpoints, values as breakpointsValues } from './breakpoints';\nexport function sizingTransform(value) {\n  return value <= 1 && value !== 0 ? `${value * 100}%` : value;\n}\nexport const width = style({\n  prop: 'width',\n  transform: sizingTransform\n});\nexport const maxWidth = props => {\n  if (props.maxWidth !== undefined && props.maxWidth !== null) {\n    const styleFromPropValue = propValue => {\n      var _props$theme, _props$theme2;\n      const breakpoint = ((_props$theme = props.theme) == null || (_props$theme = _props$theme.breakpoints) == null || (_props$theme = _props$theme.values) == null ? void 0 : _props$theme[propValue]) || breakpointsValues[propValue];\n      if (!breakpoint) {\n        return {\n          maxWidth: sizingTransform(propValue)\n        };\n      }\n      if (((_props$theme2 = props.theme) == null || (_props$theme2 = _props$theme2.breakpoints) == null ? void 0 : _props$theme2.unit) !== 'px') {\n        return {\n          maxWidth: `${breakpoint}${props.theme.breakpoints.unit}`\n        };\n      }\n      return {\n        maxWidth: breakpoint\n      };\n    };\n    return handleBreakpoints(props, props.maxWidth, styleFromPropValue);\n  }\n  return null;\n};\nmaxWidth.filterProps = ['maxWidth'];\nexport const minWidth = style({\n  prop: 'minWidth',\n  transform: sizingTransform\n});\nexport const height = style({\n  prop: 'height',\n  transform: sizingTransform\n});\nexport const maxHeight = style({\n  prop: 'maxHeight',\n  transform: sizingTransform\n});\nexport const minHeight = style({\n  prop: 'minHeight',\n  transform: sizingTransform\n});\nexport const sizeWidth = style({\n  prop: 'size',\n  cssProperty: 'width',\n  transform: sizingTransform\n});\nexport const sizeHeight = style({\n  prop: 'size',\n  cssProperty: 'height',\n  transform: sizingTransform\n});\nexport const boxSizing = style({\n  prop: 'boxSizing'\n});\nconst sizing = compose(width, maxWidth, minWidth, height, maxHeight, minHeight, boxSizing);\nexport default sizing;", "map": {"version": 3, "names": ["style", "compose", "handleBreakpoints", "values", "breakpointsValues", "sizingTransform", "value", "width", "prop", "transform", "max<PERSON><PERSON><PERSON>", "props", "undefined", "styleFromPropValue", "propValue", "_props$theme", "_props$theme2", "breakpoint", "theme", "breakpoints", "unit", "filterProps", "min<PERSON><PERSON><PERSON>", "height", "maxHeight", "minHeight", "sizeWidth", "cssProperty", "sizeHeight", "boxSizing", "sizing"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/system/esm/sizing.js"], "sourcesContent": ["import style from './style';\nimport compose from './compose';\nimport { handleBreakpoints, values as breakpointsValues } from './breakpoints';\nexport function sizingTransform(value) {\n  return value <= 1 && value !== 0 ? `${value * 100}%` : value;\n}\nexport const width = style({\n  prop: 'width',\n  transform: sizingTransform\n});\nexport const maxWidth = props => {\n  if (props.maxWidth !== undefined && props.maxWidth !== null) {\n    const styleFromPropValue = propValue => {\n      var _props$theme, _props$theme2;\n      const breakpoint = ((_props$theme = props.theme) == null || (_props$theme = _props$theme.breakpoints) == null || (_props$theme = _props$theme.values) == null ? void 0 : _props$theme[propValue]) || breakpointsValues[propValue];\n      if (!breakpoint) {\n        return {\n          maxWidth: sizingTransform(propValue)\n        };\n      }\n      if (((_props$theme2 = props.theme) == null || (_props$theme2 = _props$theme2.breakpoints) == null ? void 0 : _props$theme2.unit) !== 'px') {\n        return {\n          maxWidth: `${breakpoint}${props.theme.breakpoints.unit}`\n        };\n      }\n      return {\n        maxWidth: breakpoint\n      };\n    };\n    return handleBreakpoints(props, props.maxWidth, styleFromPropValue);\n  }\n  return null;\n};\nmaxWidth.filterProps = ['maxWidth'];\nexport const minWidth = style({\n  prop: 'minWidth',\n  transform: sizingTransform\n});\nexport const height = style({\n  prop: 'height',\n  transform: sizingTransform\n});\nexport const maxHeight = style({\n  prop: 'maxHeight',\n  transform: sizingTransform\n});\nexport const minHeight = style({\n  prop: 'minHeight',\n  transform: sizingTransform\n});\nexport const sizeWidth = style({\n  prop: 'size',\n  cssProperty: 'width',\n  transform: sizingTransform\n});\nexport const sizeHeight = style({\n  prop: 'size',\n  cssProperty: 'height',\n  transform: sizingTransform\n});\nexport const boxSizing = style({\n  prop: 'boxSizing'\n});\nconst sizing = compose(width, maxWidth, minWidth, height, maxHeight, minHeight, boxSizing);\nexport default sizing;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,SAAS;AAC3B,OAAOC,OAAO,MAAM,WAAW;AAC/B,SAASC,iBAAiB,EAAEC,MAAM,IAAIC,iBAAiB,QAAQ,eAAe;AAC9E,OAAO,SAASC,eAAeA,CAACC,KAAK,EAAE;EACrC,OAAOA,KAAK,IAAI,CAAC,IAAIA,KAAK,KAAK,CAAC,GAAI,GAAEA,KAAK,GAAG,GAAI,GAAE,GAAGA,KAAK;AAC9D;AACA,OAAO,MAAMC,KAAK,GAAGP,KAAK,CAAC;EACzBQ,IAAI,EAAE,OAAO;EACbC,SAAS,EAAEJ;AACb,CAAC,CAAC;AACF,OAAO,MAAMK,QAAQ,GAAGC,KAAK,IAAI;EAC/B,IAAIA,KAAK,CAACD,QAAQ,KAAKE,SAAS,IAAID,KAAK,CAACD,QAAQ,KAAK,IAAI,EAAE;IAC3D,MAAMG,kBAAkB,GAAGC,SAAS,IAAI;MACtC,IAAIC,YAAY,EAAEC,aAAa;MAC/B,MAAMC,UAAU,GAAG,CAAC,CAACF,YAAY,GAAGJ,KAAK,CAACO,KAAK,KAAK,IAAI,IAAI,CAACH,YAAY,GAAGA,YAAY,CAACI,WAAW,KAAK,IAAI,IAAI,CAACJ,YAAY,GAAGA,YAAY,CAACZ,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGY,YAAY,CAACD,SAAS,CAAC,KAAKV,iBAAiB,CAACU,SAAS,CAAC;MACjO,IAAI,CAACG,UAAU,EAAE;QACf,OAAO;UACLP,QAAQ,EAAEL,eAAe,CAACS,SAAS;QACrC,CAAC;MACH;MACA,IAAI,CAAC,CAACE,aAAa,GAAGL,KAAK,CAACO,KAAK,KAAK,IAAI,IAAI,CAACF,aAAa,GAAGA,aAAa,CAACG,WAAW,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGH,aAAa,CAACI,IAAI,MAAM,IAAI,EAAE;QACzI,OAAO;UACLV,QAAQ,EAAG,GAAEO,UAAW,GAAEN,KAAK,CAACO,KAAK,CAACC,WAAW,CAACC,IAAK;QACzD,CAAC;MACH;MACA,OAAO;QACLV,QAAQ,EAAEO;MACZ,CAAC;IACH,CAAC;IACD,OAAOf,iBAAiB,CAACS,KAAK,EAAEA,KAAK,CAACD,QAAQ,EAAEG,kBAAkB,CAAC;EACrE;EACA,OAAO,IAAI;AACb,CAAC;AACDH,QAAQ,CAACW,WAAW,GAAG,CAAC,UAAU,CAAC;AACnC,OAAO,MAAMC,QAAQ,GAAGtB,KAAK,CAAC;EAC5BQ,IAAI,EAAE,UAAU;EAChBC,SAAS,EAAEJ;AACb,CAAC,CAAC;AACF,OAAO,MAAMkB,MAAM,GAAGvB,KAAK,CAAC;EAC1BQ,IAAI,EAAE,QAAQ;EACdC,SAAS,EAAEJ;AACb,CAAC,CAAC;AACF,OAAO,MAAMmB,SAAS,GAAGxB,KAAK,CAAC;EAC7BQ,IAAI,EAAE,WAAW;EACjBC,SAAS,EAAEJ;AACb,CAAC,CAAC;AACF,OAAO,MAAMoB,SAAS,GAAGzB,KAAK,CAAC;EAC7BQ,IAAI,EAAE,WAAW;EACjBC,SAAS,EAAEJ;AACb,CAAC,CAAC;AACF,OAAO,MAAMqB,SAAS,GAAG1B,KAAK,CAAC;EAC7BQ,IAAI,EAAE,MAAM;EACZmB,WAAW,EAAE,OAAO;EACpBlB,SAAS,EAAEJ;AACb,CAAC,CAAC;AACF,OAAO,MAAMuB,UAAU,GAAG5B,KAAK,CAAC;EAC9BQ,IAAI,EAAE,MAAM;EACZmB,WAAW,EAAE,QAAQ;EACrBlB,SAAS,EAAEJ;AACb,CAAC,CAAC;AACF,OAAO,MAAMwB,SAAS,GAAG7B,KAAK,CAAC;EAC7BQ,IAAI,EAAE;AACR,CAAC,CAAC;AACF,MAAMsB,MAAM,GAAG7B,OAAO,CAACM,KAAK,EAAEG,QAAQ,EAAEY,QAAQ,EAAEC,MAAM,EAAEC,SAAS,EAAEC,SAAS,EAAEI,SAAS,CAAC;AAC1F,eAAeC,MAAM"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}