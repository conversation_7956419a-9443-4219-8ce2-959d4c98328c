{"ast": null, "code": "import { unstable_requirePropFactory as requirePropFactory } from '@mui/utils';\nexport default requirePropFactory;", "map": {"version": 3, "names": ["unstable_requirePropFactory", "requirePropFactory"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/material/utils/requirePropFactory.js"], "sourcesContent": ["import { unstable_requirePropFactory as requirePropFactory } from '@mui/utils';\nexport default requirePropFactory;"], "mappings": "AAAA,SAASA,2BAA2B,IAAIC,kBAAkB,QAAQ,YAAY;AAC9E,eAAeA,kBAAkB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}