{"ast": null, "code": "'use client';\n\nexport { default } from './useEventCallback';", "map": {"version": 3, "names": ["default"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/utils/esm/useEventCallback/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './useEventCallback';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,oBAAoB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}