{"ast": null, "code": "export { default } from './ownerDocument';", "map": {"version": 3, "names": ["default"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/utils/esm/ownerDocument/index.js"], "sourcesContent": ["export { default } from './ownerDocument';"], "mappings": "AAAA,SAASA,OAAO,QAAQ,iBAAiB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}