{"ast": null, "code": "export { default } from './capitalize';", "map": {"version": 3, "names": ["default"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/utils/esm/capitalize/index.js"], "sourcesContent": ["export { default } from './capitalize';"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}