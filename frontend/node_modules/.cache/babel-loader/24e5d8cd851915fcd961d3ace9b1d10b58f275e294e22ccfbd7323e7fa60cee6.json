{"ast": null, "code": "export { unstable_ClassNameGenerator } from '@mui/utils';", "map": {"version": 3, "names": ["unstable_ClassNameGenerator"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/base/ClassNameGenerator/index.js"], "sourcesContent": ["export { unstable_ClassNameGenerator } from '@mui/utils';"], "mappings": "AAAA,SAASA,2BAA2B,QAAQ,YAAY"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}