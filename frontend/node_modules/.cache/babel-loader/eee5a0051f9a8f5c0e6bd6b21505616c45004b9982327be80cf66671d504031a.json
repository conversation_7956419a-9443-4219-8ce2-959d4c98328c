{"ast": null, "code": "'use client';\n\nexport * from './useAutocomplete';", "map": {"version": 3, "names": [], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/base/useAutocomplete/index.js"], "sourcesContent": ["'use client';\n\nexport * from './useAutocomplete';"], "mappings": "AAAA,YAAY;;AAEZ,cAAc,mBAAmB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}