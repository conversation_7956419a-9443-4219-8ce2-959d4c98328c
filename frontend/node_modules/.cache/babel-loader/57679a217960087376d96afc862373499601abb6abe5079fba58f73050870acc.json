{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"autoHideDuration\", \"children\", \"disableWindowBlurListener\", \"exited\", \"onBlur\", \"onClose\", \"onFocus\", \"onMouseEnter\", \"onMouseLeave\", \"open\", \"resumeHideDuration\", \"slotProps\", \"slots\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { ClickAwayListener } from '../ClickAwayListener';\nimport { unstable_composeClasses as composeClasses } from '../composeClasses';\nimport { getSnackbarUtilityClass } from './snackbarClasses';\nimport { useSnackbar } from '../useSnackbar';\nimport { useSlotProps } from '../utils';\nimport { useClassNamesOverride } from '../utils/ClassNameConfigurator';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = () => {\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, useClassNamesOverride(getSnackbarUtilityClass));\n};\n/**\n *\n * Demos:\n *\n * - [Snackbar](https://mui.com/base-ui/react-snackbar/)\n * - [Snackbar](https://mui.com/joy-ui/react-snackbar/)\n * - [Snackbar](https://mui.com/material-ui/react-snackbar/)\n *\n * API:\n *\n * - [Snackbar API](https://mui.com/base-ui/react-snackbar/components-api/#snackbar)\n */\nconst Snackbar = /*#__PURE__*/React.forwardRef(function Snackbar(props, forwardedRef) {\n  const {\n      autoHideDuration = null,\n      children,\n      disableWindowBlurListener = false,\n      exited = true,\n      onClose,\n      open,\n      resumeHideDuration,\n      slotProps = {},\n      slots = {}\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const classes = useUtilityClasses();\n  const {\n    getRootProps,\n    onClickAway\n  } = useSnackbar(_extends({}, props, {\n    autoHideDuration,\n    disableWindowBlurListener,\n    onClose,\n    open,\n    resumeHideDuration\n  }));\n  const ownerState = props;\n  const Root = slots.root || 'div';\n  const rootProps = useSlotProps({\n    elementType: Root,\n    getSlotProps: getRootProps,\n    externalForwardedProps: other,\n    externalSlotProps: slotProps.root,\n    additionalProps: {\n      ref: forwardedRef\n    },\n    ownerState,\n    className: classes.root\n  });\n  const clickAwayListenerProps = useSlotProps({\n    elementType: ClickAwayListener,\n    externalSlotProps: slotProps.clickAwayListener,\n    additionalProps: {\n      onClickAway\n    },\n    ownerState\n  });\n\n  // ClickAwayListener doesn't support ownerState\n  delete clickAwayListenerProps.ownerState;\n\n  // So that we only render active snackbars.\n  if (!open && exited) {\n    return null;\n  }\n  return /*#__PURE__*/_jsx(ClickAwayListener, _extends({}, clickAwayListenerProps, {\n    children: /*#__PURE__*/_jsx(Root, _extends({}, rootProps, {\n      children: children\n    }))\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? Snackbar.propTypes /* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * The number of milliseconds to wait before automatically calling the\n   * `onClose` function. `onClose` should then set the state of the `open`\n   * prop to hide the Snackbar. This behavior is disabled by default with\n   * the `null` value.\n   * @default null\n   */\n  autoHideDuration: PropTypes.number,\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * If `true`, the `autoHideDuration` timer will expire even if the window is not focused.\n   * @default false\n   */\n  disableWindowBlurListener: PropTypes.bool,\n  /**\n   * The prop used to handle exited transition and unmount the component.\n   * @default true\n   */\n  exited: PropTypes.bool,\n  /**\n   * Callback fired when the component requests to be closed.\n   * Typically `onClose` is used to set state in the parent component,\n   * which is used to control the `Snackbar` `open` prop.\n   * The `reason` parameter can optionally be used to control the response to `onClose`,\n   * for example ignoring `clickaway`.\n   *\n   * @param {React.SyntheticEvent<any> | Event} event The event source of the callback.\n   * @param {string} reason Can be: `\"timeout\"` (`autoHideDuration` expired), `\"clickaway\"`, or `\"escapeKeyDown\"`.\n   */\n  onClose: PropTypes.func,\n  /**\n   * If `true`, the component is shown.\n   */\n  open: PropTypes.bool,\n  /**\n   * The number of milliseconds to wait before dismissing after user interaction.\n   * If `autoHideDuration` prop isn't specified, it does nothing.\n   * If `autoHideDuration` prop is specified but `resumeHideDuration` isn't,\n   * we default to `autoHideDuration / 2` ms.\n   */\n  resumeHideDuration: PropTypes.number,\n  /**\n   * The props used for each slot inside the Snackbar.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    clickAwayListener: PropTypes.oneOfType([PropTypes.func, PropTypes.shape({\n      children: PropTypes.element.isRequired,\n      disableReactTree: PropTypes.bool,\n      mouseEvent: PropTypes.oneOf(['onClick', 'onMouseDown', 'onMouseUp', 'onPointerDown', 'onPointerUp', false]),\n      onClickAway: PropTypes.func,\n      touchEvent: PropTypes.oneOf(['onTouchEnd', 'onTouchStart', false])\n    })]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside the Snackbar.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    root: PropTypes.elementType\n  })\n} : void 0;\nexport { Snackbar };", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "PropTypes", "ClickAwayListener", "unstable_composeClasses", "composeClasses", "getSnackbarUtilityClass", "useSnackbar", "useSlotProps", "useClassNamesOverride", "jsx", "_jsx", "useUtilityClasses", "slots", "root", "Snackbar", "forwardRef", "props", "forwardedRef", "autoHideDuration", "children", "disableWindowBlurListener", "exited", "onClose", "open", "resumeHideDuration", "slotProps", "other", "classes", "getRootProps", "onClickAway", "ownerState", "Root", "rootProps", "elementType", "getSlotProps", "externalForwardedProps", "externalSlotProps", "additionalProps", "ref", "className", "clickAwayListenerProps", "clickAwayListener", "process", "env", "NODE_ENV", "propTypes", "number", "node", "bool", "func", "shape", "oneOfType", "element", "isRequired", "disableReactTree", "mouseEvent", "oneOf", "touchEvent", "object"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/base/Snackbar/Snackbar.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"autoHideDuration\", \"children\", \"disableWindowBlurListener\", \"exited\", \"onBlur\", \"onClose\", \"onFocus\", \"onMouseEnter\", \"onMouseLeave\", \"open\", \"resumeHideDuration\", \"slotProps\", \"slots\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { ClickAwayListener } from '../ClickAwayListener';\nimport { unstable_composeClasses as composeClasses } from '../composeClasses';\nimport { getSnackbarUtilityClass } from './snackbarClasses';\nimport { useSnackbar } from '../useSnackbar';\nimport { useSlotProps } from '../utils';\nimport { useClassNamesOverride } from '../utils/ClassNameConfigurator';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = () => {\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, useClassNamesOverride(getSnackbarUtilityClass));\n};\n/**\n *\n * Demos:\n *\n * - [Snackbar](https://mui.com/base-ui/react-snackbar/)\n * - [Snackbar](https://mui.com/joy-ui/react-snackbar/)\n * - [Snackbar](https://mui.com/material-ui/react-snackbar/)\n *\n * API:\n *\n * - [Snackbar API](https://mui.com/base-ui/react-snackbar/components-api/#snackbar)\n */\nconst Snackbar = /*#__PURE__*/React.forwardRef(function Snackbar(props, forwardedRef) {\n  const {\n      autoHideDuration = null,\n      children,\n      disableWindowBlurListener = false,\n      exited = true,\n      onClose,\n      open,\n      resumeHideDuration,\n      slotProps = {},\n      slots = {}\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const classes = useUtilityClasses();\n  const {\n    getRootProps,\n    onClickAway\n  } = useSnackbar(_extends({}, props, {\n    autoHideDuration,\n    disableWindowBlurListener,\n    onClose,\n    open,\n    resumeHideDuration\n  }));\n  const ownerState = props;\n  const Root = slots.root || 'div';\n  const rootProps = useSlotProps({\n    elementType: Root,\n    getSlotProps: getRootProps,\n    externalForwardedProps: other,\n    externalSlotProps: slotProps.root,\n    additionalProps: {\n      ref: forwardedRef\n    },\n    ownerState,\n    className: classes.root\n  });\n  const clickAwayListenerProps = useSlotProps({\n    elementType: ClickAwayListener,\n    externalSlotProps: slotProps.clickAwayListener,\n    additionalProps: {\n      onClickAway\n    },\n    ownerState\n  });\n\n  // ClickAwayListener doesn't support ownerState\n  delete clickAwayListenerProps.ownerState;\n\n  // So that we only render active snackbars.\n  if (!open && exited) {\n    return null;\n  }\n  return /*#__PURE__*/_jsx(ClickAwayListener, _extends({}, clickAwayListenerProps, {\n    children: /*#__PURE__*/_jsx(Root, _extends({}, rootProps, {\n      children: children\n    }))\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? Snackbar.propTypes /* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * The number of milliseconds to wait before automatically calling the\n   * `onClose` function. `onClose` should then set the state of the `open`\n   * prop to hide the Snackbar. This behavior is disabled by default with\n   * the `null` value.\n   * @default null\n   */\n  autoHideDuration: PropTypes.number,\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * If `true`, the `autoHideDuration` timer will expire even if the window is not focused.\n   * @default false\n   */\n  disableWindowBlurListener: PropTypes.bool,\n  /**\n   * The prop used to handle exited transition and unmount the component.\n   * @default true\n   */\n  exited: PropTypes.bool,\n  /**\n   * Callback fired when the component requests to be closed.\n   * Typically `onClose` is used to set state in the parent component,\n   * which is used to control the `Snackbar` `open` prop.\n   * The `reason` parameter can optionally be used to control the response to `onClose`,\n   * for example ignoring `clickaway`.\n   *\n   * @param {React.SyntheticEvent<any> | Event} event The event source of the callback.\n   * @param {string} reason Can be: `\"timeout\"` (`autoHideDuration` expired), `\"clickaway\"`, or `\"escapeKeyDown\"`.\n   */\n  onClose: PropTypes.func,\n  /**\n   * If `true`, the component is shown.\n   */\n  open: PropTypes.bool,\n  /**\n   * The number of milliseconds to wait before dismissing after user interaction.\n   * If `autoHideDuration` prop isn't specified, it does nothing.\n   * If `autoHideDuration` prop is specified but `resumeHideDuration` isn't,\n   * we default to `autoHideDuration / 2` ms.\n   */\n  resumeHideDuration: PropTypes.number,\n  /**\n   * The props used for each slot inside the Snackbar.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    clickAwayListener: PropTypes.oneOfType([PropTypes.func, PropTypes.shape({\n      children: PropTypes.element.isRequired,\n      disableReactTree: PropTypes.bool,\n      mouseEvent: PropTypes.oneOf(['onClick', 'onMouseDown', 'onMouseUp', 'onPointerDown', 'onPointerUp', false]),\n      onClickAway: PropTypes.func,\n      touchEvent: PropTypes.oneOf(['onTouchEnd', 'onTouchStart', false])\n    })]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside the Snackbar.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    root: PropTypes.elementType\n  })\n} : void 0;\nexport { Snackbar };"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,kBAAkB,EAAE,UAAU,EAAE,2BAA2B,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,cAAc,EAAE,cAAc,EAAE,MAAM,EAAE,oBAAoB,EAAE,WAAW,EAAE,OAAO,CAAC;AAC7M,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,iBAAiB,QAAQ,sBAAsB;AACxD,SAASC,uBAAuB,IAAIC,cAAc,QAAQ,mBAAmB;AAC7E,SAASC,uBAAuB,QAAQ,mBAAmB;AAC3D,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,YAAY,QAAQ,UAAU;AACvC,SAASC,qBAAqB,QAAQ,gCAAgC;AACtE,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAC9B,MAAMC,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM;EACf,CAAC;EACD,OAAOT,cAAc,CAACQ,KAAK,EAAEJ,qBAAqB,CAACH,uBAAuB,CAAC,CAAC;AAC9E,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMS,QAAQ,GAAG,aAAad,KAAK,CAACe,UAAU,CAAC,SAASD,QAAQA,CAACE,KAAK,EAAEC,YAAY,EAAE;EACpF,MAAM;MACFC,gBAAgB,GAAG,IAAI;MACvBC,QAAQ;MACRC,yBAAyB,GAAG,KAAK;MACjCC,MAAM,GAAG,IAAI;MACbC,OAAO;MACPC,IAAI;MACJC,kBAAkB;MAClBC,SAAS,GAAG,CAAC,CAAC;MACdb,KAAK,GAAG,CAAC;IACX,CAAC,GAAGI,KAAK;IACTU,KAAK,GAAG5B,6BAA6B,CAACkB,KAAK,EAAEjB,SAAS,CAAC;EACzD,MAAM4B,OAAO,GAAGhB,iBAAiB,CAAC,CAAC;EACnC,MAAM;IACJiB,YAAY;IACZC;EACF,CAAC,GAAGvB,WAAW,CAACT,QAAQ,CAAC,CAAC,CAAC,EAAEmB,KAAK,EAAE;IAClCE,gBAAgB;IAChBE,yBAAyB;IACzBE,OAAO;IACPC,IAAI;IACJC;EACF,CAAC,CAAC,CAAC;EACH,MAAMM,UAAU,GAAGd,KAAK;EACxB,MAAMe,IAAI,GAAGnB,KAAK,CAACC,IAAI,IAAI,KAAK;EAChC,MAAMmB,SAAS,GAAGzB,YAAY,CAAC;IAC7B0B,WAAW,EAAEF,IAAI;IACjBG,YAAY,EAAEN,YAAY;IAC1BO,sBAAsB,EAAET,KAAK;IAC7BU,iBAAiB,EAAEX,SAAS,CAACZ,IAAI;IACjCwB,eAAe,EAAE;MACfC,GAAG,EAAErB;IACP,CAAC;IACDa,UAAU;IACVS,SAAS,EAAEZ,OAAO,CAACd;EACrB,CAAC,CAAC;EACF,MAAM2B,sBAAsB,GAAGjC,YAAY,CAAC;IAC1C0B,WAAW,EAAE/B,iBAAiB;IAC9BkC,iBAAiB,EAAEX,SAAS,CAACgB,iBAAiB;IAC9CJ,eAAe,EAAE;MACfR;IACF,CAAC;IACDC;EACF,CAAC,CAAC;;EAEF;EACA,OAAOU,sBAAsB,CAACV,UAAU;;EAExC;EACA,IAAI,CAACP,IAAI,IAAIF,MAAM,EAAE;IACnB,OAAO,IAAI;EACb;EACA,OAAO,aAAaX,IAAI,CAACR,iBAAiB,EAAEL,QAAQ,CAAC,CAAC,CAAC,EAAE2C,sBAAsB,EAAE;IAC/ErB,QAAQ,EAAE,aAAaT,IAAI,CAACqB,IAAI,EAAElC,QAAQ,CAAC,CAAC,CAAC,EAAEmC,SAAS,EAAE;MACxDb,QAAQ,EAAEA;IACZ,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFuB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG9B,QAAQ,CAAC+B,SAAS,CAAC,yBAAyB;EAClF;EACA;EACA;EACA;EACA;AACF;AACA;AACA;AACA;AACA;AACA;EACE3B,gBAAgB,EAAEjB,SAAS,CAAC6C,MAAM;EAClC;AACF;AACA;EACE3B,QAAQ,EAAElB,SAAS,CAAC8C,IAAI;EACxB;AACF;AACA;AACA;EACE3B,yBAAyB,EAAEnB,SAAS,CAAC+C,IAAI;EACzC;AACF;AACA;AACA;EACE3B,MAAM,EAAEpB,SAAS,CAAC+C,IAAI;EACtB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE1B,OAAO,EAAErB,SAAS,CAACgD,IAAI;EACvB;AACF;AACA;EACE1B,IAAI,EAAEtB,SAAS,CAAC+C,IAAI;EACpB;AACF;AACA;AACA;AACA;AACA;EACExB,kBAAkB,EAAEvB,SAAS,CAAC6C,MAAM;EACpC;AACF;AACA;AACA;EACErB,SAAS,EAAExB,SAAS,CAACiD,KAAK,CAAC;IACzBT,iBAAiB,EAAExC,SAAS,CAACkD,SAAS,CAAC,CAAClD,SAAS,CAACgD,IAAI,EAAEhD,SAAS,CAACiD,KAAK,CAAC;MACtE/B,QAAQ,EAAElB,SAAS,CAACmD,OAAO,CAACC,UAAU;MACtCC,gBAAgB,EAAErD,SAAS,CAAC+C,IAAI;MAChCO,UAAU,EAAEtD,SAAS,CAACuD,KAAK,CAAC,CAAC,SAAS,EAAE,aAAa,EAAE,WAAW,EAAE,eAAe,EAAE,aAAa,EAAE,KAAK,CAAC,CAAC;MAC3G3B,WAAW,EAAE5B,SAAS,CAACgD,IAAI;MAC3BQ,UAAU,EAAExD,SAAS,CAACuD,KAAK,CAAC,CAAC,YAAY,EAAE,cAAc,EAAE,KAAK,CAAC;IACnE,CAAC,CAAC,CAAC,CAAC;IACJ3C,IAAI,EAAEZ,SAAS,CAACkD,SAAS,CAAC,CAAClD,SAAS,CAACgD,IAAI,EAAEhD,SAAS,CAACyD,MAAM,CAAC;EAC9D,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;EACE9C,KAAK,EAAEX,SAAS,CAACiD,KAAK,CAAC;IACrBrC,IAAI,EAAEZ,SAAS,CAACgC;EAClB,CAAC;AACH,CAAC,GAAG,KAAK,CAAC;AACV,SAASnB,QAAQ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}