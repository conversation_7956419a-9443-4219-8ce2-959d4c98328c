{"ast": null, "code": "'use client';\n\nimport { unstable_useIsFocusVisible as useIsFocusVisible } from '@mui/utils';\nexport default useIsFocusVisible;", "map": {"version": 3, "names": ["unstable_useIsFocusVisible", "useIsFocusVisible"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/material/utils/useIsFocusVisible.js"], "sourcesContent": ["'use client';\n\nimport { unstable_useIsFocusVisible as useIsFocusVisible } from '@mui/utils';\nexport default useIsFocusVisible;"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,0BAA0B,IAAIC,iBAAiB,QAAQ,YAAY;AAC5E,eAAeA,iBAAiB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}