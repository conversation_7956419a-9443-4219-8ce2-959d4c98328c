{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_ownerDocument as ownerDocument, unstable_useControlled as useControlled, unstable_useEnhancedEffect as useEnhancedEffect, unstable_useEventCallback as useEventCallback, unstable_useForkRef as useForkRef, unstable_useIsFocusVisible as useIsFocusVisible, visuallyHidden } from '@mui/utils';\nimport { areArraysEqual, extractEventHandlers } from '../utils';\nconst INTENTIONAL_DRAG_COUNT_THRESHOLD = 2;\nfunction asc(a, b) {\n  return a - b;\n}\nfunction clamp(value, min, max) {\n  if (value == null) {\n    return min;\n  }\n  return Math.min(Math.max(min, value), max);\n}\nfunction findClosest(values, currentValue) {\n  var _values$reduce;\n  const {\n    index: closestIndex\n  } = (_values$reduce = values.reduce((acc, value, index) => {\n    const distance = Math.abs(currentValue - value);\n    if (acc === null || distance < acc.distance || distance === acc.distance) {\n      return {\n        distance,\n        index\n      };\n    }\n    return acc;\n  }, null)) != null ? _values$reduce : {};\n  return closestIndex;\n}\nfunction trackFinger(event, touchId) {\n  // The event is TouchEvent\n  if (touchId.current !== undefined && event.changedTouches) {\n    const touchEvent = event;\n    for (let i = 0; i < touchEvent.changedTouches.length; i += 1) {\n      const touch = touchEvent.changedTouches[i];\n      if (touch.identifier === touchId.current) {\n        return {\n          x: touch.clientX,\n          y: touch.clientY\n        };\n      }\n    }\n    return false;\n  }\n\n  // The event is MouseEvent\n  return {\n    x: event.clientX,\n    y: event.clientY\n  };\n}\nexport function valueToPercent(value, min, max) {\n  return (value - min) * 100 / (max - min);\n}\nfunction percentToValue(percent, min, max) {\n  return (max - min) * percent + min;\n}\nfunction getDecimalPrecision(num) {\n  // This handles the case when num is very small (0.00000001), js will turn this into 1e-8.\n  // When num is bigger than 1 or less than -1 it won't get converted to this notation so it's fine.\n  if (Math.abs(num) < 1) {\n    const parts = num.toExponential().split('e-');\n    const matissaDecimalPart = parts[0].split('.')[1];\n    return (matissaDecimalPart ? matissaDecimalPart.length : 0) + parseInt(parts[1], 10);\n  }\n  const decimalPart = num.toString().split('.')[1];\n  return decimalPart ? decimalPart.length : 0;\n}\nfunction roundValueToStep(value, step, min) {\n  const nearest = Math.round((value - min) / step) * step + min;\n  return Number(nearest.toFixed(getDecimalPrecision(step)));\n}\nfunction setValueIndex({\n  values,\n  newValue,\n  index\n}) {\n  const output = values.slice();\n  output[index] = newValue;\n  return output.sort(asc);\n}\nfunction focusThumb({\n  sliderRef,\n  activeIndex,\n  setActive\n}) {\n  var _sliderRef$current, _doc$activeElement;\n  const doc = ownerDocument(sliderRef.current);\n  if (!((_sliderRef$current = sliderRef.current) != null && _sliderRef$current.contains(doc.activeElement)) || Number(doc == null || (_doc$activeElement = doc.activeElement) == null ? void 0 : _doc$activeElement.getAttribute('data-index')) !== activeIndex) {\n    var _sliderRef$current2;\n    (_sliderRef$current2 = sliderRef.current) == null || _sliderRef$current2.querySelector(`[type=\"range\"][data-index=\"${activeIndex}\"]`).focus();\n  }\n  if (setActive) {\n    setActive(activeIndex);\n  }\n}\nfunction areValuesEqual(newValue, oldValue) {\n  if (typeof newValue === 'number' && typeof oldValue === 'number') {\n    return newValue === oldValue;\n  }\n  if (typeof newValue === 'object' && typeof oldValue === 'object') {\n    return areArraysEqual(newValue, oldValue);\n  }\n  return false;\n}\nconst axisProps = {\n  horizontal: {\n    offset: percent => ({\n      left: `${percent}%`\n    }),\n    leap: percent => ({\n      width: `${percent}%`\n    })\n  },\n  'horizontal-reverse': {\n    offset: percent => ({\n      right: `${percent}%`\n    }),\n    leap: percent => ({\n      width: `${percent}%`\n    })\n  },\n  vertical: {\n    offset: percent => ({\n      bottom: `${percent}%`\n    }),\n    leap: percent => ({\n      height: `${percent}%`\n    })\n  }\n};\nexport const Identity = x => x;\n\n// TODO: remove support for Safari < 13.\n// https://caniuse.com/#search=touch-action\n//\n// Safari, on iOS, supports touch action since v13.\n// Over 80% of the iOS phones are compatible\n// in August 2020.\n// Utilizing the CSS.supports method to check if touch-action is supported.\n// Since CSS.supports is supported on all but Edge@12 and IE and touch-action\n// is supported on both Edge@12 and IE if CSS.supports is not available that means that\n// touch-action will be supported\nlet cachedSupportsTouchActionNone;\nfunction doesSupportTouchActionNone() {\n  if (cachedSupportsTouchActionNone === undefined) {\n    if (typeof CSS !== 'undefined' && typeof CSS.supports === 'function') {\n      cachedSupportsTouchActionNone = CSS.supports('touch-action', 'none');\n    } else {\n      cachedSupportsTouchActionNone = true;\n    }\n  }\n  return cachedSupportsTouchActionNone;\n}\n/**\n *\n * Demos:\n *\n * - [Slider](https://mui.com/base-ui/react-slider/#hook)\n *\n * API:\n *\n * - [useSlider API](https://mui.com/base-ui/react-slider/hooks-api/#use-slider)\n */\nexport function useSlider(parameters) {\n  const {\n    'aria-labelledby': ariaLabelledby,\n    defaultValue,\n    disabled = false,\n    disableSwap = false,\n    isRtl = false,\n    marks: marksProp = false,\n    max = 100,\n    min = 0,\n    name,\n    onChange,\n    onChangeCommitted,\n    orientation = 'horizontal',\n    rootRef: ref,\n    scale = Identity,\n    step = 1,\n    tabIndex,\n    value: valueProp\n  } = parameters;\n  const touchId = React.useRef();\n  // We can't use the :active browser pseudo-classes.\n  // - The active state isn't triggered when clicking on the rail.\n  // - The active state isn't transferred when inversing a range slider.\n  const [active, setActive] = React.useState(-1);\n  const [open, setOpen] = React.useState(-1);\n  const [dragging, setDragging] = React.useState(false);\n  const moveCount = React.useRef(0);\n  const [valueDerived, setValueState] = useControlled({\n    controlled: valueProp,\n    default: defaultValue != null ? defaultValue : min,\n    name: 'Slider'\n  });\n  const handleChange = onChange && ((event, value, thumbIndex) => {\n    // Redefine target to allow name and value to be read.\n    // This allows seamless integration with the most popular form libraries.\n    // https://github.com/mui/material-ui/issues/13485#issuecomment-676048492\n    // Clone the event to not override `target` of the original event.\n    const nativeEvent = event.nativeEvent || event;\n    // @ts-ignore The nativeEvent is function, not object\n    const clonedEvent = new nativeEvent.constructor(nativeEvent.type, nativeEvent);\n    Object.defineProperty(clonedEvent, 'target', {\n      writable: true,\n      value: {\n        value,\n        name\n      }\n    });\n    onChange(clonedEvent, value, thumbIndex);\n  });\n  const range = Array.isArray(valueDerived);\n  let values = range ? valueDerived.slice().sort(asc) : [valueDerived];\n  values = values.map(value => clamp(value, min, max));\n  const marks = marksProp === true && step !== null ? [...Array(Math.floor((max - min) / step) + 1)].map((_, index) => ({\n    value: min + step * index\n  })) : marksProp || [];\n  const marksValues = marks.map(mark => mark.value);\n  const {\n    isFocusVisibleRef,\n    onBlur: handleBlurVisible,\n    onFocus: handleFocusVisible,\n    ref: focusVisibleRef\n  } = useIsFocusVisible();\n  const [focusedThumbIndex, setFocusedThumbIndex] = React.useState(-1);\n  const sliderRef = React.useRef();\n  const handleFocusRef = useForkRef(focusVisibleRef, sliderRef);\n  const handleRef = useForkRef(ref, handleFocusRef);\n  const createHandleHiddenInputFocus = otherHandlers => event => {\n    var _otherHandlers$onFocu;\n    const index = Number(event.currentTarget.getAttribute('data-index'));\n    handleFocusVisible(event);\n    if (isFocusVisibleRef.current === true) {\n      setFocusedThumbIndex(index);\n    }\n    setOpen(index);\n    otherHandlers == null || (_otherHandlers$onFocu = otherHandlers.onFocus) == null || _otherHandlers$onFocu.call(otherHandlers, event);\n  };\n  const createHandleHiddenInputBlur = otherHandlers => event => {\n    var _otherHandlers$onBlur;\n    handleBlurVisible(event);\n    if (isFocusVisibleRef.current === false) {\n      setFocusedThumbIndex(-1);\n    }\n    setOpen(-1);\n    otherHandlers == null || (_otherHandlers$onBlur = otherHandlers.onBlur) == null || _otherHandlers$onBlur.call(otherHandlers, event);\n  };\n  useEnhancedEffect(() => {\n    if (disabled && sliderRef.current.contains(document.activeElement)) {\n      var _document$activeEleme;\n      // This is necessary because Firefox and Safari will keep focus\n      // on a disabled element:\n      // https://codesandbox.io/p/sandbox/mui-pr-22247-forked-h151h?file=/src/App.js\n      // @ts-ignore\n      (_document$activeEleme = document.activeElement) == null || _document$activeEleme.blur();\n    }\n  }, [disabled]);\n  if (disabled && active !== -1) {\n    setActive(-1);\n  }\n  if (disabled && focusedThumbIndex !== -1) {\n    setFocusedThumbIndex(-1);\n  }\n  const createHandleHiddenInputChange = otherHandlers => event => {\n    var _otherHandlers$onChan;\n    (_otherHandlers$onChan = otherHandlers.onChange) == null || _otherHandlers$onChan.call(otherHandlers, event);\n    const index = Number(event.currentTarget.getAttribute('data-index'));\n    const value = values[index];\n    const marksIndex = marksValues.indexOf(value);\n\n    // @ts-ignore\n    let newValue = event.target.valueAsNumber;\n    if (marks && step == null) {\n      const maxMarksValue = marksValues[marksValues.length - 1];\n      if (newValue > maxMarksValue) {\n        newValue = maxMarksValue;\n      } else if (newValue < marksValues[0]) {\n        newValue = marksValues[0];\n      } else {\n        newValue = newValue < value ? marksValues[marksIndex - 1] : marksValues[marksIndex + 1];\n      }\n    }\n    newValue = clamp(newValue, min, max);\n    if (range) {\n      // Bound the new value to the thumb's neighbours.\n      if (disableSwap) {\n        newValue = clamp(newValue, values[index - 1] || -Infinity, values[index + 1] || Infinity);\n      }\n      const previousValue = newValue;\n      newValue = setValueIndex({\n        values,\n        newValue,\n        index\n      });\n      let activeIndex = index;\n\n      // Potentially swap the index if needed.\n      if (!disableSwap) {\n        activeIndex = newValue.indexOf(previousValue);\n      }\n      focusThumb({\n        sliderRef,\n        activeIndex\n      });\n    }\n    setValueState(newValue);\n    setFocusedThumbIndex(index);\n    if (handleChange && !areValuesEqual(newValue, valueDerived)) {\n      handleChange(event, newValue, index);\n    }\n    if (onChangeCommitted) {\n      onChangeCommitted(event, newValue);\n    }\n  };\n  const previousIndex = React.useRef();\n  let axis = orientation;\n  if (isRtl && orientation === 'horizontal') {\n    axis += '-reverse';\n  }\n  const getFingerNewValue = ({\n    finger,\n    move = false\n  }) => {\n    const {\n      current: slider\n    } = sliderRef;\n    const {\n      width,\n      height,\n      bottom,\n      left\n    } = slider.getBoundingClientRect();\n    let percent;\n    if (axis.indexOf('vertical') === 0) {\n      percent = (bottom - finger.y) / height;\n    } else {\n      percent = (finger.x - left) / width;\n    }\n    if (axis.indexOf('-reverse') !== -1) {\n      percent = 1 - percent;\n    }\n    let newValue;\n    newValue = percentToValue(percent, min, max);\n    if (step) {\n      newValue = roundValueToStep(newValue, step, min);\n    } else {\n      const closestIndex = findClosest(marksValues, newValue);\n      newValue = marksValues[closestIndex];\n    }\n    newValue = clamp(newValue, min, max);\n    let activeIndex = 0;\n    if (range) {\n      if (!move) {\n        activeIndex = findClosest(values, newValue);\n      } else {\n        activeIndex = previousIndex.current;\n      }\n\n      // Bound the new value to the thumb's neighbours.\n      if (disableSwap) {\n        newValue = clamp(newValue, values[activeIndex - 1] || -Infinity, values[activeIndex + 1] || Infinity);\n      }\n      const previousValue = newValue;\n      newValue = setValueIndex({\n        values,\n        newValue,\n        index: activeIndex\n      });\n\n      // Potentially swap the index if needed.\n      if (!(disableSwap && move)) {\n        activeIndex = newValue.indexOf(previousValue);\n        previousIndex.current = activeIndex;\n      }\n    }\n    return {\n      newValue,\n      activeIndex\n    };\n  };\n  const handleTouchMove = useEventCallback(nativeEvent => {\n    const finger = trackFinger(nativeEvent, touchId);\n    if (!finger) {\n      return;\n    }\n    moveCount.current += 1;\n\n    // Cancel move in case some other element consumed a mouseup event and it was not fired.\n    // @ts-ignore buttons doesn't not exists on touch event\n    if (nativeEvent.type === 'mousemove' && nativeEvent.buttons === 0) {\n      // eslint-disable-next-line @typescript-eslint/no-use-before-define\n      handleTouchEnd(nativeEvent);\n      return;\n    }\n    const {\n      newValue,\n      activeIndex\n    } = getFingerNewValue({\n      finger,\n      move: true\n    });\n    focusThumb({\n      sliderRef,\n      activeIndex,\n      setActive\n    });\n    setValueState(newValue);\n    if (!dragging && moveCount.current > INTENTIONAL_DRAG_COUNT_THRESHOLD) {\n      setDragging(true);\n    }\n    if (handleChange && !areValuesEqual(newValue, valueDerived)) {\n      handleChange(nativeEvent, newValue, activeIndex);\n    }\n  });\n  const handleTouchEnd = useEventCallback(nativeEvent => {\n    const finger = trackFinger(nativeEvent, touchId);\n    setDragging(false);\n    if (!finger) {\n      return;\n    }\n    const {\n      newValue\n    } = getFingerNewValue({\n      finger,\n      move: true\n    });\n    setActive(-1);\n    if (nativeEvent.type === 'touchend') {\n      setOpen(-1);\n    }\n    if (onChangeCommitted) {\n      onChangeCommitted(nativeEvent, newValue);\n    }\n    touchId.current = undefined;\n\n    // eslint-disable-next-line @typescript-eslint/no-use-before-define\n    stopListening();\n  });\n  const handleTouchStart = useEventCallback(nativeEvent => {\n    if (disabled) {\n      return;\n    }\n    // If touch-action: none; is not supported we need to prevent the scroll manually.\n    if (!doesSupportTouchActionNone()) {\n      nativeEvent.preventDefault();\n    }\n    const touch = nativeEvent.changedTouches[0];\n    if (touch != null) {\n      // A number that uniquely identifies the current finger in the touch session.\n      touchId.current = touch.identifier;\n    }\n    const finger = trackFinger(nativeEvent, touchId);\n    if (finger !== false) {\n      const {\n        newValue,\n        activeIndex\n      } = getFingerNewValue({\n        finger\n      });\n      focusThumb({\n        sliderRef,\n        activeIndex,\n        setActive\n      });\n      setValueState(newValue);\n      if (handleChange && !areValuesEqual(newValue, valueDerived)) {\n        handleChange(nativeEvent, newValue, activeIndex);\n      }\n    }\n    moveCount.current = 0;\n    const doc = ownerDocument(sliderRef.current);\n    doc.addEventListener('touchmove', handleTouchMove);\n    doc.addEventListener('touchend', handleTouchEnd);\n  });\n  const stopListening = React.useCallback(() => {\n    const doc = ownerDocument(sliderRef.current);\n    doc.removeEventListener('mousemove', handleTouchMove);\n    doc.removeEventListener('mouseup', handleTouchEnd);\n    doc.removeEventListener('touchmove', handleTouchMove);\n    doc.removeEventListener('touchend', handleTouchEnd);\n  }, [handleTouchEnd, handleTouchMove]);\n  React.useEffect(() => {\n    const {\n      current: slider\n    } = sliderRef;\n    slider.addEventListener('touchstart', handleTouchStart, {\n      passive: doesSupportTouchActionNone()\n    });\n    return () => {\n      // @ts-ignore\n      slider.removeEventListener('touchstart', handleTouchStart, {\n        passive: doesSupportTouchActionNone()\n      });\n      stopListening();\n    };\n  }, [stopListening, handleTouchStart]);\n  React.useEffect(() => {\n    if (disabled) {\n      stopListening();\n    }\n  }, [disabled, stopListening]);\n  const createHandleMouseDown = otherHandlers => event => {\n    var _otherHandlers$onMous;\n    (_otherHandlers$onMous = otherHandlers.onMouseDown) == null || _otherHandlers$onMous.call(otherHandlers, event);\n    if (disabled) {\n      return;\n    }\n    if (event.defaultPrevented) {\n      return;\n    }\n\n    // Only handle left clicks\n    if (event.button !== 0) {\n      return;\n    }\n\n    // Avoid text selection\n    event.preventDefault();\n    const finger = trackFinger(event, touchId);\n    if (finger !== false) {\n      const {\n        newValue,\n        activeIndex\n      } = getFingerNewValue({\n        finger\n      });\n      focusThumb({\n        sliderRef,\n        activeIndex,\n        setActive\n      });\n      setValueState(newValue);\n      if (handleChange && !areValuesEqual(newValue, valueDerived)) {\n        handleChange(event, newValue, activeIndex);\n      }\n    }\n    moveCount.current = 0;\n    const doc = ownerDocument(sliderRef.current);\n    doc.addEventListener('mousemove', handleTouchMove);\n    doc.addEventListener('mouseup', handleTouchEnd);\n  };\n  const trackOffset = valueToPercent(range ? values[0] : min, min, max);\n  const trackLeap = valueToPercent(values[values.length - 1], min, max) - trackOffset;\n  const getRootProps = (externalProps = {}) => {\n    const externalHandlers = extractEventHandlers(externalProps);\n    const ownEventHandlers = {\n      onMouseDown: createHandleMouseDown(externalHandlers || {})\n    };\n    const mergedEventHandlers = _extends({}, externalHandlers, ownEventHandlers);\n    return _extends({}, externalProps, {\n      ref: handleRef\n    }, mergedEventHandlers);\n  };\n  const createHandleMouseOver = otherHandlers => event => {\n    var _otherHandlers$onMous2;\n    (_otherHandlers$onMous2 = otherHandlers.onMouseOver) == null || _otherHandlers$onMous2.call(otherHandlers, event);\n    const index = Number(event.currentTarget.getAttribute('data-index'));\n    setOpen(index);\n  };\n  const createHandleMouseLeave = otherHandlers => event => {\n    var _otherHandlers$onMous3;\n    (_otherHandlers$onMous3 = otherHandlers.onMouseLeave) == null || _otherHandlers$onMous3.call(otherHandlers, event);\n    setOpen(-1);\n  };\n  const getThumbProps = (externalProps = {}) => {\n    const externalHandlers = extractEventHandlers(externalProps);\n    const ownEventHandlers = {\n      onMouseOver: createHandleMouseOver(externalHandlers || {}),\n      onMouseLeave: createHandleMouseLeave(externalHandlers || {})\n    };\n    return _extends({}, externalProps, externalHandlers, ownEventHandlers);\n  };\n  const getThumbStyle = index => {\n    return {\n      // So the non active thumb doesn't show its label on hover.\n      pointerEvents: active !== -1 && active !== index ? 'none' : undefined\n    };\n  };\n  const getHiddenInputProps = (externalProps = {}) => {\n    var _parameters$step;\n    const externalHandlers = extractEventHandlers(externalProps);\n    const ownEventHandlers = {\n      onChange: createHandleHiddenInputChange(externalHandlers || {}),\n      onFocus: createHandleHiddenInputFocus(externalHandlers || {}),\n      onBlur: createHandleHiddenInputBlur(externalHandlers || {})\n    };\n    const mergedEventHandlers = _extends({}, externalHandlers, ownEventHandlers);\n    return _extends({\n      tabIndex,\n      'aria-labelledby': ariaLabelledby,\n      'aria-orientation': orientation,\n      'aria-valuemax': scale(max),\n      'aria-valuemin': scale(min),\n      name,\n      type: 'range',\n      min: parameters.min,\n      max: parameters.max,\n      step: parameters.step === null && parameters.marks ? 'any' : (_parameters$step = parameters.step) != null ? _parameters$step : undefined,\n      disabled\n    }, externalProps, mergedEventHandlers, {\n      style: _extends({}, visuallyHidden, {\n        direction: isRtl ? 'rtl' : 'ltr',\n        // So that VoiceOver's focus indicator matches the thumb's dimensions\n        width: '100%',\n        height: '100%'\n      })\n    });\n  };\n  return {\n    active,\n    axis: axis,\n    axisProps,\n    dragging,\n    focusedThumbIndex,\n    getHiddenInputProps,\n    getRootProps,\n    getThumbProps,\n    marks: marks,\n    open,\n    range,\n    rootRef: handleRef,\n    trackLeap,\n    trackOffset,\n    values,\n    getThumbStyle\n  };\n}", "map": {"version": 3, "names": ["_extends", "React", "unstable_ownerDocument", "ownerDocument", "unstable_useControlled", "useControlled", "unstable_useEnhancedEffect", "useEnhancedEffect", "unstable_useEventCallback", "useEventCallback", "unstable_useForkRef", "useForkRef", "unstable_useIsFocusVisible", "useIsFocusVisible", "visuallyHidden", "areArraysEqual", "extractEventHandlers", "INTENTIONAL_DRAG_COUNT_THRESHOLD", "asc", "a", "b", "clamp", "value", "min", "max", "Math", "findClosest", "values", "currentValue", "_values$reduce", "index", "closestIndex", "reduce", "acc", "distance", "abs", "trackFinger", "event", "touchId", "current", "undefined", "changedTouches", "touchEvent", "i", "length", "touch", "identifier", "x", "clientX", "y", "clientY", "valueToPercent", "percentToValue", "percent", "getDecimalPrecision", "num", "parts", "toExponential", "split", "mat<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parseInt", "decimalPart", "toString", "roundValueToStep", "step", "nearest", "round", "Number", "toFixed", "setValueIndex", "newValue", "output", "slice", "sort", "focusThumb", "sliderRef", "activeIndex", "setActive", "_sliderRef$current", "_doc$activeElement", "doc", "contains", "activeElement", "getAttribute", "_sliderRef$current2", "querySelector", "focus", "areValuesEqual", "oldValue", "axisProps", "horizontal", "offset", "left", "leap", "width", "right", "vertical", "bottom", "height", "Identity", "cachedSupportsTouchActionNone", "doesSupportTouchActionNone", "CSS", "supports", "useSlider", "parameters", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "defaultValue", "disabled", "disableSwap", "isRtl", "marks", "marksProp", "name", "onChange", "onChangeCommitted", "orientation", "rootRef", "ref", "scale", "tabIndex", "valueProp", "useRef", "active", "useState", "open", "<PERSON><PERSON><PERSON>", "dragging", "setDragging", "moveCount", "valueDerived", "setValueState", "controlled", "default", "handleChange", "thumbIndex", "nativeEvent", "clonedEvent", "constructor", "type", "Object", "defineProperty", "writable", "range", "Array", "isArray", "map", "floor", "_", "marksV<PERSON>ues", "mark", "isFocusVisibleRef", "onBlur", "handleBlurVisible", "onFocus", "handleFocusVisible", "focusVisibleRef", "focusedThumbIndex", "setFocusedThumbIndex", "handleFocusRef", "handleRef", "createHandleHiddenInputFocus", "otherHandlers", "_otherHandlers$onFocu", "currentTarget", "call", "createHandleHiddenInputBlur", "_otherHandlers$onBlur", "document", "_document$activeEleme", "blur", "createHandleHiddenInputChange", "_otherHandlers$onChan", "marksIndex", "indexOf", "target", "valueAsNumber", "maxMarksValue", "Infinity", "previousValue", "previousIndex", "axis", "getFingerNewValue", "finger", "move", "slider", "getBoundingClientRect", "handleTouchMove", "buttons", "handleTouchEnd", "stopListening", "handleTouchStart", "preventDefault", "addEventListener", "useCallback", "removeEventListener", "useEffect", "passive", "createHandleMouseDown", "_otherHandlers$onMous", "onMouseDown", "defaultPrevented", "button", "trackOffset", "trackLeap", "getRootProps", "externalProps", "externalHandlers", "ownEventHandlers", "mergedEventHandlers", "createHandleMouseOver", "_otherHandlers$onMous2", "onMouseOver", "createHandleMouseLeave", "_otherHandlers$onMous3", "onMouseLeave", "getThumbProps", "getThumbStyle", "pointerEvents", "getHiddenInputProps", "_parameters$step", "style", "direction"], "sources": ["/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/node_modules/@mui/base/useSlider/useSlider.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_ownerDocument as ownerDocument, unstable_useControlled as useControlled, unstable_useEnhancedEffect as useEnhancedEffect, unstable_useEventCallback as useEventCallback, unstable_useForkRef as useForkRef, unstable_useIsFocusVisible as useIsFocusVisible, visuallyHidden } from '@mui/utils';\nimport { areArraysEqual, extractEventHandlers } from '../utils';\nconst INTENTIONAL_DRAG_COUNT_THRESHOLD = 2;\nfunction asc(a, b) {\n  return a - b;\n}\nfunction clamp(value, min, max) {\n  if (value == null) {\n    return min;\n  }\n  return Math.min(Math.max(min, value), max);\n}\nfunction findClosest(values, currentValue) {\n  var _values$reduce;\n  const {\n    index: closestIndex\n  } = (_values$reduce = values.reduce((acc, value, index) => {\n    const distance = Math.abs(currentValue - value);\n    if (acc === null || distance < acc.distance || distance === acc.distance) {\n      return {\n        distance,\n        index\n      };\n    }\n    return acc;\n  }, null)) != null ? _values$reduce : {};\n  return closestIndex;\n}\nfunction trackFinger(event, touchId) {\n  // The event is TouchEvent\n  if (touchId.current !== undefined && event.changedTouches) {\n    const touchEvent = event;\n    for (let i = 0; i < touchEvent.changedTouches.length; i += 1) {\n      const touch = touchEvent.changedTouches[i];\n      if (touch.identifier === touchId.current) {\n        return {\n          x: touch.clientX,\n          y: touch.clientY\n        };\n      }\n    }\n    return false;\n  }\n\n  // The event is MouseEvent\n  return {\n    x: event.clientX,\n    y: event.clientY\n  };\n}\nexport function valueToPercent(value, min, max) {\n  return (value - min) * 100 / (max - min);\n}\nfunction percentToValue(percent, min, max) {\n  return (max - min) * percent + min;\n}\nfunction getDecimalPrecision(num) {\n  // This handles the case when num is very small (0.00000001), js will turn this into 1e-8.\n  // When num is bigger than 1 or less than -1 it won't get converted to this notation so it's fine.\n  if (Math.abs(num) < 1) {\n    const parts = num.toExponential().split('e-');\n    const matissaDecimalPart = parts[0].split('.')[1];\n    return (matissaDecimalPart ? matissaDecimalPart.length : 0) + parseInt(parts[1], 10);\n  }\n  const decimalPart = num.toString().split('.')[1];\n  return decimalPart ? decimalPart.length : 0;\n}\nfunction roundValueToStep(value, step, min) {\n  const nearest = Math.round((value - min) / step) * step + min;\n  return Number(nearest.toFixed(getDecimalPrecision(step)));\n}\nfunction setValueIndex({\n  values,\n  newValue,\n  index\n}) {\n  const output = values.slice();\n  output[index] = newValue;\n  return output.sort(asc);\n}\nfunction focusThumb({\n  sliderRef,\n  activeIndex,\n  setActive\n}) {\n  var _sliderRef$current, _doc$activeElement;\n  const doc = ownerDocument(sliderRef.current);\n  if (!((_sliderRef$current = sliderRef.current) != null && _sliderRef$current.contains(doc.activeElement)) || Number(doc == null || (_doc$activeElement = doc.activeElement) == null ? void 0 : _doc$activeElement.getAttribute('data-index')) !== activeIndex) {\n    var _sliderRef$current2;\n    (_sliderRef$current2 = sliderRef.current) == null || _sliderRef$current2.querySelector(`[type=\"range\"][data-index=\"${activeIndex}\"]`).focus();\n  }\n  if (setActive) {\n    setActive(activeIndex);\n  }\n}\nfunction areValuesEqual(newValue, oldValue) {\n  if (typeof newValue === 'number' && typeof oldValue === 'number') {\n    return newValue === oldValue;\n  }\n  if (typeof newValue === 'object' && typeof oldValue === 'object') {\n    return areArraysEqual(newValue, oldValue);\n  }\n  return false;\n}\nconst axisProps = {\n  horizontal: {\n    offset: percent => ({\n      left: `${percent}%`\n    }),\n    leap: percent => ({\n      width: `${percent}%`\n    })\n  },\n  'horizontal-reverse': {\n    offset: percent => ({\n      right: `${percent}%`\n    }),\n    leap: percent => ({\n      width: `${percent}%`\n    })\n  },\n  vertical: {\n    offset: percent => ({\n      bottom: `${percent}%`\n    }),\n    leap: percent => ({\n      height: `${percent}%`\n    })\n  }\n};\nexport const Identity = x => x;\n\n// TODO: remove support for Safari < 13.\n// https://caniuse.com/#search=touch-action\n//\n// Safari, on iOS, supports touch action since v13.\n// Over 80% of the iOS phones are compatible\n// in August 2020.\n// Utilizing the CSS.supports method to check if touch-action is supported.\n// Since CSS.supports is supported on all but Edge@12 and IE and touch-action\n// is supported on both Edge@12 and IE if CSS.supports is not available that means that\n// touch-action will be supported\nlet cachedSupportsTouchActionNone;\nfunction doesSupportTouchActionNone() {\n  if (cachedSupportsTouchActionNone === undefined) {\n    if (typeof CSS !== 'undefined' && typeof CSS.supports === 'function') {\n      cachedSupportsTouchActionNone = CSS.supports('touch-action', 'none');\n    } else {\n      cachedSupportsTouchActionNone = true;\n    }\n  }\n  return cachedSupportsTouchActionNone;\n}\n/**\n *\n * Demos:\n *\n * - [Slider](https://mui.com/base-ui/react-slider/#hook)\n *\n * API:\n *\n * - [useSlider API](https://mui.com/base-ui/react-slider/hooks-api/#use-slider)\n */\nexport function useSlider(parameters) {\n  const {\n    'aria-labelledby': ariaLabelledby,\n    defaultValue,\n    disabled = false,\n    disableSwap = false,\n    isRtl = false,\n    marks: marksProp = false,\n    max = 100,\n    min = 0,\n    name,\n    onChange,\n    onChangeCommitted,\n    orientation = 'horizontal',\n    rootRef: ref,\n    scale = Identity,\n    step = 1,\n    tabIndex,\n    value: valueProp\n  } = parameters;\n  const touchId = React.useRef();\n  // We can't use the :active browser pseudo-classes.\n  // - The active state isn't triggered when clicking on the rail.\n  // - The active state isn't transferred when inversing a range slider.\n  const [active, setActive] = React.useState(-1);\n  const [open, setOpen] = React.useState(-1);\n  const [dragging, setDragging] = React.useState(false);\n  const moveCount = React.useRef(0);\n  const [valueDerived, setValueState] = useControlled({\n    controlled: valueProp,\n    default: defaultValue != null ? defaultValue : min,\n    name: 'Slider'\n  });\n  const handleChange = onChange && ((event, value, thumbIndex) => {\n    // Redefine target to allow name and value to be read.\n    // This allows seamless integration with the most popular form libraries.\n    // https://github.com/mui/material-ui/issues/13485#issuecomment-676048492\n    // Clone the event to not override `target` of the original event.\n    const nativeEvent = event.nativeEvent || event;\n    // @ts-ignore The nativeEvent is function, not object\n    const clonedEvent = new nativeEvent.constructor(nativeEvent.type, nativeEvent);\n    Object.defineProperty(clonedEvent, 'target', {\n      writable: true,\n      value: {\n        value,\n        name\n      }\n    });\n    onChange(clonedEvent, value, thumbIndex);\n  });\n  const range = Array.isArray(valueDerived);\n  let values = range ? valueDerived.slice().sort(asc) : [valueDerived];\n  values = values.map(value => clamp(value, min, max));\n  const marks = marksProp === true && step !== null ? [...Array(Math.floor((max - min) / step) + 1)].map((_, index) => ({\n    value: min + step * index\n  })) : marksProp || [];\n  const marksValues = marks.map(mark => mark.value);\n  const {\n    isFocusVisibleRef,\n    onBlur: handleBlurVisible,\n    onFocus: handleFocusVisible,\n    ref: focusVisibleRef\n  } = useIsFocusVisible();\n  const [focusedThumbIndex, setFocusedThumbIndex] = React.useState(-1);\n  const sliderRef = React.useRef();\n  const handleFocusRef = useForkRef(focusVisibleRef, sliderRef);\n  const handleRef = useForkRef(ref, handleFocusRef);\n  const createHandleHiddenInputFocus = otherHandlers => event => {\n    var _otherHandlers$onFocu;\n    const index = Number(event.currentTarget.getAttribute('data-index'));\n    handleFocusVisible(event);\n    if (isFocusVisibleRef.current === true) {\n      setFocusedThumbIndex(index);\n    }\n    setOpen(index);\n    otherHandlers == null || (_otherHandlers$onFocu = otherHandlers.onFocus) == null || _otherHandlers$onFocu.call(otherHandlers, event);\n  };\n  const createHandleHiddenInputBlur = otherHandlers => event => {\n    var _otherHandlers$onBlur;\n    handleBlurVisible(event);\n    if (isFocusVisibleRef.current === false) {\n      setFocusedThumbIndex(-1);\n    }\n    setOpen(-1);\n    otherHandlers == null || (_otherHandlers$onBlur = otherHandlers.onBlur) == null || _otherHandlers$onBlur.call(otherHandlers, event);\n  };\n  useEnhancedEffect(() => {\n    if (disabled && sliderRef.current.contains(document.activeElement)) {\n      var _document$activeEleme;\n      // This is necessary because Firefox and Safari will keep focus\n      // on a disabled element:\n      // https://codesandbox.io/p/sandbox/mui-pr-22247-forked-h151h?file=/src/App.js\n      // @ts-ignore\n      (_document$activeEleme = document.activeElement) == null || _document$activeEleme.blur();\n    }\n  }, [disabled]);\n  if (disabled && active !== -1) {\n    setActive(-1);\n  }\n  if (disabled && focusedThumbIndex !== -1) {\n    setFocusedThumbIndex(-1);\n  }\n  const createHandleHiddenInputChange = otherHandlers => event => {\n    var _otherHandlers$onChan;\n    (_otherHandlers$onChan = otherHandlers.onChange) == null || _otherHandlers$onChan.call(otherHandlers, event);\n    const index = Number(event.currentTarget.getAttribute('data-index'));\n    const value = values[index];\n    const marksIndex = marksValues.indexOf(value);\n\n    // @ts-ignore\n    let newValue = event.target.valueAsNumber;\n    if (marks && step == null) {\n      const maxMarksValue = marksValues[marksValues.length - 1];\n      if (newValue > maxMarksValue) {\n        newValue = maxMarksValue;\n      } else if (newValue < marksValues[0]) {\n        newValue = marksValues[0];\n      } else {\n        newValue = newValue < value ? marksValues[marksIndex - 1] : marksValues[marksIndex + 1];\n      }\n    }\n    newValue = clamp(newValue, min, max);\n    if (range) {\n      // Bound the new value to the thumb's neighbours.\n      if (disableSwap) {\n        newValue = clamp(newValue, values[index - 1] || -Infinity, values[index + 1] || Infinity);\n      }\n      const previousValue = newValue;\n      newValue = setValueIndex({\n        values,\n        newValue,\n        index\n      });\n      let activeIndex = index;\n\n      // Potentially swap the index if needed.\n      if (!disableSwap) {\n        activeIndex = newValue.indexOf(previousValue);\n      }\n      focusThumb({\n        sliderRef,\n        activeIndex\n      });\n    }\n    setValueState(newValue);\n    setFocusedThumbIndex(index);\n    if (handleChange && !areValuesEqual(newValue, valueDerived)) {\n      handleChange(event, newValue, index);\n    }\n    if (onChangeCommitted) {\n      onChangeCommitted(event, newValue);\n    }\n  };\n  const previousIndex = React.useRef();\n  let axis = orientation;\n  if (isRtl && orientation === 'horizontal') {\n    axis += '-reverse';\n  }\n  const getFingerNewValue = ({\n    finger,\n    move = false\n  }) => {\n    const {\n      current: slider\n    } = sliderRef;\n    const {\n      width,\n      height,\n      bottom,\n      left\n    } = slider.getBoundingClientRect();\n    let percent;\n    if (axis.indexOf('vertical') === 0) {\n      percent = (bottom - finger.y) / height;\n    } else {\n      percent = (finger.x - left) / width;\n    }\n    if (axis.indexOf('-reverse') !== -1) {\n      percent = 1 - percent;\n    }\n    let newValue;\n    newValue = percentToValue(percent, min, max);\n    if (step) {\n      newValue = roundValueToStep(newValue, step, min);\n    } else {\n      const closestIndex = findClosest(marksValues, newValue);\n      newValue = marksValues[closestIndex];\n    }\n    newValue = clamp(newValue, min, max);\n    let activeIndex = 0;\n    if (range) {\n      if (!move) {\n        activeIndex = findClosest(values, newValue);\n      } else {\n        activeIndex = previousIndex.current;\n      }\n\n      // Bound the new value to the thumb's neighbours.\n      if (disableSwap) {\n        newValue = clamp(newValue, values[activeIndex - 1] || -Infinity, values[activeIndex + 1] || Infinity);\n      }\n      const previousValue = newValue;\n      newValue = setValueIndex({\n        values,\n        newValue,\n        index: activeIndex\n      });\n\n      // Potentially swap the index if needed.\n      if (!(disableSwap && move)) {\n        activeIndex = newValue.indexOf(previousValue);\n        previousIndex.current = activeIndex;\n      }\n    }\n    return {\n      newValue,\n      activeIndex\n    };\n  };\n  const handleTouchMove = useEventCallback(nativeEvent => {\n    const finger = trackFinger(nativeEvent, touchId);\n    if (!finger) {\n      return;\n    }\n    moveCount.current += 1;\n\n    // Cancel move in case some other element consumed a mouseup event and it was not fired.\n    // @ts-ignore buttons doesn't not exists on touch event\n    if (nativeEvent.type === 'mousemove' && nativeEvent.buttons === 0) {\n      // eslint-disable-next-line @typescript-eslint/no-use-before-define\n      handleTouchEnd(nativeEvent);\n      return;\n    }\n    const {\n      newValue,\n      activeIndex\n    } = getFingerNewValue({\n      finger,\n      move: true\n    });\n    focusThumb({\n      sliderRef,\n      activeIndex,\n      setActive\n    });\n    setValueState(newValue);\n    if (!dragging && moveCount.current > INTENTIONAL_DRAG_COUNT_THRESHOLD) {\n      setDragging(true);\n    }\n    if (handleChange && !areValuesEqual(newValue, valueDerived)) {\n      handleChange(nativeEvent, newValue, activeIndex);\n    }\n  });\n  const handleTouchEnd = useEventCallback(nativeEvent => {\n    const finger = trackFinger(nativeEvent, touchId);\n    setDragging(false);\n    if (!finger) {\n      return;\n    }\n    const {\n      newValue\n    } = getFingerNewValue({\n      finger,\n      move: true\n    });\n    setActive(-1);\n    if (nativeEvent.type === 'touchend') {\n      setOpen(-1);\n    }\n    if (onChangeCommitted) {\n      onChangeCommitted(nativeEvent, newValue);\n    }\n    touchId.current = undefined;\n\n    // eslint-disable-next-line @typescript-eslint/no-use-before-define\n    stopListening();\n  });\n  const handleTouchStart = useEventCallback(nativeEvent => {\n    if (disabled) {\n      return;\n    }\n    // If touch-action: none; is not supported we need to prevent the scroll manually.\n    if (!doesSupportTouchActionNone()) {\n      nativeEvent.preventDefault();\n    }\n    const touch = nativeEvent.changedTouches[0];\n    if (touch != null) {\n      // A number that uniquely identifies the current finger in the touch session.\n      touchId.current = touch.identifier;\n    }\n    const finger = trackFinger(nativeEvent, touchId);\n    if (finger !== false) {\n      const {\n        newValue,\n        activeIndex\n      } = getFingerNewValue({\n        finger\n      });\n      focusThumb({\n        sliderRef,\n        activeIndex,\n        setActive\n      });\n      setValueState(newValue);\n      if (handleChange && !areValuesEqual(newValue, valueDerived)) {\n        handleChange(nativeEvent, newValue, activeIndex);\n      }\n    }\n    moveCount.current = 0;\n    const doc = ownerDocument(sliderRef.current);\n    doc.addEventListener('touchmove', handleTouchMove);\n    doc.addEventListener('touchend', handleTouchEnd);\n  });\n  const stopListening = React.useCallback(() => {\n    const doc = ownerDocument(sliderRef.current);\n    doc.removeEventListener('mousemove', handleTouchMove);\n    doc.removeEventListener('mouseup', handleTouchEnd);\n    doc.removeEventListener('touchmove', handleTouchMove);\n    doc.removeEventListener('touchend', handleTouchEnd);\n  }, [handleTouchEnd, handleTouchMove]);\n  React.useEffect(() => {\n    const {\n      current: slider\n    } = sliderRef;\n    slider.addEventListener('touchstart', handleTouchStart, {\n      passive: doesSupportTouchActionNone()\n    });\n    return () => {\n      // @ts-ignore\n      slider.removeEventListener('touchstart', handleTouchStart, {\n        passive: doesSupportTouchActionNone()\n      });\n      stopListening();\n    };\n  }, [stopListening, handleTouchStart]);\n  React.useEffect(() => {\n    if (disabled) {\n      stopListening();\n    }\n  }, [disabled, stopListening]);\n  const createHandleMouseDown = otherHandlers => event => {\n    var _otherHandlers$onMous;\n    (_otherHandlers$onMous = otherHandlers.onMouseDown) == null || _otherHandlers$onMous.call(otherHandlers, event);\n    if (disabled) {\n      return;\n    }\n    if (event.defaultPrevented) {\n      return;\n    }\n\n    // Only handle left clicks\n    if (event.button !== 0) {\n      return;\n    }\n\n    // Avoid text selection\n    event.preventDefault();\n    const finger = trackFinger(event, touchId);\n    if (finger !== false) {\n      const {\n        newValue,\n        activeIndex\n      } = getFingerNewValue({\n        finger\n      });\n      focusThumb({\n        sliderRef,\n        activeIndex,\n        setActive\n      });\n      setValueState(newValue);\n      if (handleChange && !areValuesEqual(newValue, valueDerived)) {\n        handleChange(event, newValue, activeIndex);\n      }\n    }\n    moveCount.current = 0;\n    const doc = ownerDocument(sliderRef.current);\n    doc.addEventListener('mousemove', handleTouchMove);\n    doc.addEventListener('mouseup', handleTouchEnd);\n  };\n  const trackOffset = valueToPercent(range ? values[0] : min, min, max);\n  const trackLeap = valueToPercent(values[values.length - 1], min, max) - trackOffset;\n  const getRootProps = (externalProps = {}) => {\n    const externalHandlers = extractEventHandlers(externalProps);\n    const ownEventHandlers = {\n      onMouseDown: createHandleMouseDown(externalHandlers || {})\n    };\n    const mergedEventHandlers = _extends({}, externalHandlers, ownEventHandlers);\n    return _extends({}, externalProps, {\n      ref: handleRef\n    }, mergedEventHandlers);\n  };\n  const createHandleMouseOver = otherHandlers => event => {\n    var _otherHandlers$onMous2;\n    (_otherHandlers$onMous2 = otherHandlers.onMouseOver) == null || _otherHandlers$onMous2.call(otherHandlers, event);\n    const index = Number(event.currentTarget.getAttribute('data-index'));\n    setOpen(index);\n  };\n  const createHandleMouseLeave = otherHandlers => event => {\n    var _otherHandlers$onMous3;\n    (_otherHandlers$onMous3 = otherHandlers.onMouseLeave) == null || _otherHandlers$onMous3.call(otherHandlers, event);\n    setOpen(-1);\n  };\n  const getThumbProps = (externalProps = {}) => {\n    const externalHandlers = extractEventHandlers(externalProps);\n    const ownEventHandlers = {\n      onMouseOver: createHandleMouseOver(externalHandlers || {}),\n      onMouseLeave: createHandleMouseLeave(externalHandlers || {})\n    };\n    return _extends({}, externalProps, externalHandlers, ownEventHandlers);\n  };\n  const getThumbStyle = index => {\n    return {\n      // So the non active thumb doesn't show its label on hover.\n      pointerEvents: active !== -1 && active !== index ? 'none' : undefined\n    };\n  };\n  const getHiddenInputProps = (externalProps = {}) => {\n    var _parameters$step;\n    const externalHandlers = extractEventHandlers(externalProps);\n    const ownEventHandlers = {\n      onChange: createHandleHiddenInputChange(externalHandlers || {}),\n      onFocus: createHandleHiddenInputFocus(externalHandlers || {}),\n      onBlur: createHandleHiddenInputBlur(externalHandlers || {})\n    };\n    const mergedEventHandlers = _extends({}, externalHandlers, ownEventHandlers);\n    return _extends({\n      tabIndex,\n      'aria-labelledby': ariaLabelledby,\n      'aria-orientation': orientation,\n      'aria-valuemax': scale(max),\n      'aria-valuemin': scale(min),\n      name,\n      type: 'range',\n      min: parameters.min,\n      max: parameters.max,\n      step: parameters.step === null && parameters.marks ? 'any' : (_parameters$step = parameters.step) != null ? _parameters$step : undefined,\n      disabled\n    }, externalProps, mergedEventHandlers, {\n      style: _extends({}, visuallyHidden, {\n        direction: isRtl ? 'rtl' : 'ltr',\n        // So that VoiceOver's focus indicator matches the thumb's dimensions\n        width: '100%',\n        height: '100%'\n      })\n    });\n  };\n  return {\n    active,\n    axis: axis,\n    axisProps,\n    dragging,\n    focusedThumbIndex,\n    getHiddenInputProps,\n    getRootProps,\n    getThumbProps,\n    marks: marks,\n    open,\n    range,\n    rootRef: handleRef,\n    trackLeap,\n    trackOffset,\n    values,\n    getThumbStyle\n  };\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,sBAAsB,IAAIC,aAAa,EAAEC,sBAAsB,IAAIC,aAAa,EAAEC,0BAA0B,IAAIC,iBAAiB,EAAEC,yBAAyB,IAAIC,gBAAgB,EAAEC,mBAAmB,IAAIC,UAAU,EAAEC,0BAA0B,IAAIC,iBAAiB,EAAEC,cAAc,QAAQ,YAAY;AACjT,SAASC,cAAc,EAAEC,oBAAoB,QAAQ,UAAU;AAC/D,MAAMC,gCAAgC,GAAG,CAAC;AAC1C,SAASC,GAAGA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACjB,OAAOD,CAAC,GAAGC,CAAC;AACd;AACA,SAASC,KAAKA,CAACC,KAAK,EAAEC,GAAG,EAAEC,GAAG,EAAE;EAC9B,IAAIF,KAAK,IAAI,IAAI,EAAE;IACjB,OAAOC,GAAG;EACZ;EACA,OAAOE,IAAI,CAACF,GAAG,CAACE,IAAI,CAACD,GAAG,CAACD,GAAG,EAAED,KAAK,CAAC,EAAEE,GAAG,CAAC;AAC5C;AACA,SAASE,WAAWA,CAACC,MAAM,EAAEC,YAAY,EAAE;EACzC,IAAIC,cAAc;EAClB,MAAM;IACJC,KAAK,EAAEC;EACT,CAAC,GAAG,CAACF,cAAc,GAAGF,MAAM,CAACK,MAAM,CAAC,CAACC,GAAG,EAAEX,KAAK,EAAEQ,KAAK,KAAK;IACzD,MAAMI,QAAQ,GAAGT,IAAI,CAACU,GAAG,CAACP,YAAY,GAAGN,KAAK,CAAC;IAC/C,IAAIW,GAAG,KAAK,IAAI,IAAIC,QAAQ,GAAGD,GAAG,CAACC,QAAQ,IAAIA,QAAQ,KAAKD,GAAG,CAACC,QAAQ,EAAE;MACxE,OAAO;QACLA,QAAQ;QACRJ;MACF,CAAC;IACH;IACA,OAAOG,GAAG;EACZ,CAAC,EAAE,IAAI,CAAC,KAAK,IAAI,GAAGJ,cAAc,GAAG,CAAC,CAAC;EACvC,OAAOE,YAAY;AACrB;AACA,SAASK,WAAWA,CAACC,KAAK,EAAEC,OAAO,EAAE;EACnC;EACA,IAAIA,OAAO,CAACC,OAAO,KAAKC,SAAS,IAAIH,KAAK,CAACI,cAAc,EAAE;IACzD,MAAMC,UAAU,GAAGL,KAAK;IACxB,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,UAAU,CAACD,cAAc,CAACG,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;MAC5D,MAAME,KAAK,GAAGH,UAAU,CAACD,cAAc,CAACE,CAAC,CAAC;MAC1C,IAAIE,KAAK,CAACC,UAAU,KAAKR,OAAO,CAACC,OAAO,EAAE;QACxC,OAAO;UACLQ,CAAC,EAAEF,KAAK,CAACG,OAAO;UAChBC,CAAC,EAAEJ,KAAK,CAACK;QACX,CAAC;MACH;IACF;IACA,OAAO,KAAK;EACd;;EAEA;EACA,OAAO;IACLH,CAAC,EAAEV,KAAK,CAACW,OAAO;IAChBC,CAAC,EAAEZ,KAAK,CAACa;EACX,CAAC;AACH;AACA,OAAO,SAASC,cAAcA,CAAC7B,KAAK,EAAEC,GAAG,EAAEC,GAAG,EAAE;EAC9C,OAAO,CAACF,KAAK,GAAGC,GAAG,IAAI,GAAG,IAAIC,GAAG,GAAGD,GAAG,CAAC;AAC1C;AACA,SAAS6B,cAAcA,CAACC,OAAO,EAAE9B,GAAG,EAAEC,GAAG,EAAE;EACzC,OAAO,CAACA,GAAG,GAAGD,GAAG,IAAI8B,OAAO,GAAG9B,GAAG;AACpC;AACA,SAAS+B,mBAAmBA,CAACC,GAAG,EAAE;EAChC;EACA;EACA,IAAI9B,IAAI,CAACU,GAAG,CAACoB,GAAG,CAAC,GAAG,CAAC,EAAE;IACrB,MAAMC,KAAK,GAAGD,GAAG,CAACE,aAAa,CAAC,CAAC,CAACC,KAAK,CAAC,IAAI,CAAC;IAC7C,MAAMC,kBAAkB,GAAGH,KAAK,CAAC,CAAC,CAAC,CAACE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACjD,OAAO,CAACC,kBAAkB,GAAGA,kBAAkB,CAACf,MAAM,GAAG,CAAC,IAAIgB,QAAQ,CAACJ,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;EACtF;EACA,MAAMK,WAAW,GAAGN,GAAG,CAACO,QAAQ,CAAC,CAAC,CAACJ,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAChD,OAAOG,WAAW,GAAGA,WAAW,CAACjB,MAAM,GAAG,CAAC;AAC7C;AACA,SAASmB,gBAAgBA,CAACzC,KAAK,EAAE0C,IAAI,EAAEzC,GAAG,EAAE;EAC1C,MAAM0C,OAAO,GAAGxC,IAAI,CAACyC,KAAK,CAAC,CAAC5C,KAAK,GAAGC,GAAG,IAAIyC,IAAI,CAAC,GAAGA,IAAI,GAAGzC,GAAG;EAC7D,OAAO4C,MAAM,CAACF,OAAO,CAACG,OAAO,CAACd,mBAAmB,CAACU,IAAI,CAAC,CAAC,CAAC;AAC3D;AACA,SAASK,aAAaA,CAAC;EACrB1C,MAAM;EACN2C,QAAQ;EACRxC;AACF,CAAC,EAAE;EACD,MAAMyC,MAAM,GAAG5C,MAAM,CAAC6C,KAAK,CAAC,CAAC;EAC7BD,MAAM,CAACzC,KAAK,CAAC,GAAGwC,QAAQ;EACxB,OAAOC,MAAM,CAACE,IAAI,CAACvD,GAAG,CAAC;AACzB;AACA,SAASwD,UAAUA,CAAC;EAClBC,SAAS;EACTC,WAAW;EACXC;AACF,CAAC,EAAE;EACD,IAAIC,kBAAkB,EAAEC,kBAAkB;EAC1C,MAAMC,GAAG,GAAG7E,aAAa,CAACwE,SAAS,CAACpC,OAAO,CAAC;EAC5C,IAAI,EAAE,CAACuC,kBAAkB,GAAGH,SAAS,CAACpC,OAAO,KAAK,IAAI,IAAIuC,kBAAkB,CAACG,QAAQ,CAACD,GAAG,CAACE,aAAa,CAAC,CAAC,IAAIf,MAAM,CAACa,GAAG,IAAI,IAAI,IAAI,CAACD,kBAAkB,GAAGC,GAAG,CAACE,aAAa,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGH,kBAAkB,CAACI,YAAY,CAAC,YAAY,CAAC,CAAC,KAAKP,WAAW,EAAE;IAC7P,IAAIQ,mBAAmB;IACvB,CAACA,mBAAmB,GAAGT,SAAS,CAACpC,OAAO,KAAK,IAAI,IAAI6C,mBAAmB,CAACC,aAAa,CAAE,8BAA6BT,WAAY,IAAG,CAAC,CAACU,KAAK,CAAC,CAAC;EAC/I;EACA,IAAIT,SAAS,EAAE;IACbA,SAAS,CAACD,WAAW,CAAC;EACxB;AACF;AACA,SAASW,cAAcA,CAACjB,QAAQ,EAAEkB,QAAQ,EAAE;EAC1C,IAAI,OAAOlB,QAAQ,KAAK,QAAQ,IAAI,OAAOkB,QAAQ,KAAK,QAAQ,EAAE;IAChE,OAAOlB,QAAQ,KAAKkB,QAAQ;EAC9B;EACA,IAAI,OAAOlB,QAAQ,KAAK,QAAQ,IAAI,OAAOkB,QAAQ,KAAK,QAAQ,EAAE;IAChE,OAAOzE,cAAc,CAACuD,QAAQ,EAAEkB,QAAQ,CAAC;EAC3C;EACA,OAAO,KAAK;AACd;AACA,MAAMC,SAAS,GAAG;EAChBC,UAAU,EAAE;IACVC,MAAM,EAAEtC,OAAO,KAAK;MAClBuC,IAAI,EAAG,GAAEvC,OAAQ;IACnB,CAAC,CAAC;IACFwC,IAAI,EAAExC,OAAO,KAAK;MAChByC,KAAK,EAAG,GAAEzC,OAAQ;IACpB,CAAC;EACH,CAAC;EACD,oBAAoB,EAAE;IACpBsC,MAAM,EAAEtC,OAAO,KAAK;MAClB0C,KAAK,EAAG,GAAE1C,OAAQ;IACpB,CAAC,CAAC;IACFwC,IAAI,EAAExC,OAAO,KAAK;MAChByC,KAAK,EAAG,GAAEzC,OAAQ;IACpB,CAAC;EACH,CAAC;EACD2C,QAAQ,EAAE;IACRL,MAAM,EAAEtC,OAAO,KAAK;MAClB4C,MAAM,EAAG,GAAE5C,OAAQ;IACrB,CAAC,CAAC;IACFwC,IAAI,EAAExC,OAAO,KAAK;MAChB6C,MAAM,EAAG,GAAE7C,OAAQ;IACrB,CAAC;EACH;AACF,CAAC;AACD,OAAO,MAAM8C,QAAQ,GAAGpD,CAAC,IAAIA,CAAC;;AAE9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIqD,6BAA6B;AACjC,SAASC,0BAA0BA,CAAA,EAAG;EACpC,IAAID,6BAA6B,KAAK5D,SAAS,EAAE;IAC/C,IAAI,OAAO8D,GAAG,KAAK,WAAW,IAAI,OAAOA,GAAG,CAACC,QAAQ,KAAK,UAAU,EAAE;MACpEH,6BAA6B,GAAGE,GAAG,CAACC,QAAQ,CAAC,cAAc,EAAE,MAAM,CAAC;IACtE,CAAC,MAAM;MACLH,6BAA6B,GAAG,IAAI;IACtC;EACF;EACA,OAAOA,6BAA6B;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASI,SAASA,CAACC,UAAU,EAAE;EACpC,MAAM;IACJ,iBAAiB,EAAEC,cAAc;IACjCC,YAAY;IACZC,QAAQ,GAAG,KAAK;IAChBC,WAAW,GAAG,KAAK;IACnBC,KAAK,GAAG,KAAK;IACbC,KAAK,EAAEC,SAAS,GAAG,KAAK;IACxBxF,GAAG,GAAG,GAAG;IACTD,GAAG,GAAG,CAAC;IACP0F,IAAI;IACJC,QAAQ;IACRC,iBAAiB;IACjBC,WAAW,GAAG,YAAY;IAC1BC,OAAO,EAAEC,GAAG;IACZC,KAAK,GAAGpB,QAAQ;IAChBnC,IAAI,GAAG,CAAC;IACRwD,QAAQ;IACRlG,KAAK,EAAEmG;EACT,CAAC,GAAGhB,UAAU;EACd,MAAMnE,OAAO,GAAGrC,KAAK,CAACyH,MAAM,CAAC,CAAC;EAC9B;EACA;EACA;EACA,MAAM,CAACC,MAAM,EAAE9C,SAAS,CAAC,GAAG5E,KAAK,CAAC2H,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC9C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAG7H,KAAK,CAAC2H,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1C,MAAM,CAACG,QAAQ,EAAEC,WAAW,CAAC,GAAG/H,KAAK,CAAC2H,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAMK,SAAS,GAAGhI,KAAK,CAACyH,MAAM,CAAC,CAAC,CAAC;EACjC,MAAM,CAACQ,YAAY,EAAEC,aAAa,CAAC,GAAG9H,aAAa,CAAC;IAClD+H,UAAU,EAAEX,SAAS;IACrBY,OAAO,EAAE1B,YAAY,IAAI,IAAI,GAAGA,YAAY,GAAGpF,GAAG;IAClD0F,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAMqB,YAAY,GAAGpB,QAAQ,KAAK,CAAC7E,KAAK,EAAEf,KAAK,EAAEiH,UAAU,KAAK;IAC9D;IACA;IACA;IACA;IACA,MAAMC,WAAW,GAAGnG,KAAK,CAACmG,WAAW,IAAInG,KAAK;IAC9C;IACA,MAAMoG,WAAW,GAAG,IAAID,WAAW,CAACE,WAAW,CAACF,WAAW,CAACG,IAAI,EAAEH,WAAW,CAAC;IAC9EI,MAAM,CAACC,cAAc,CAACJ,WAAW,EAAE,QAAQ,EAAE;MAC3CK,QAAQ,EAAE,IAAI;MACdxH,KAAK,EAAE;QACLA,KAAK;QACL2F;MACF;IACF,CAAC,CAAC;IACFC,QAAQ,CAACuB,WAAW,EAAEnH,KAAK,EAAEiH,UAAU,CAAC;EAC1C,CAAC,CAAC;EACF,MAAMQ,KAAK,GAAGC,KAAK,CAACC,OAAO,CAACf,YAAY,CAAC;EACzC,IAAIvG,MAAM,GAAGoH,KAAK,GAAGb,YAAY,CAAC1D,KAAK,CAAC,CAAC,CAACC,IAAI,CAACvD,GAAG,CAAC,GAAG,CAACgH,YAAY,CAAC;EACpEvG,MAAM,GAAGA,MAAM,CAACuH,GAAG,CAAC5H,KAAK,IAAID,KAAK,CAACC,KAAK,EAAEC,GAAG,EAAEC,GAAG,CAAC,CAAC;EACpD,MAAMuF,KAAK,GAAGC,SAAS,KAAK,IAAI,IAAIhD,IAAI,KAAK,IAAI,GAAG,CAAC,GAAGgF,KAAK,CAACvH,IAAI,CAAC0H,KAAK,CAAC,CAAC3H,GAAG,GAAGD,GAAG,IAAIyC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAACkF,GAAG,CAAC,CAACE,CAAC,EAAEtH,KAAK,MAAM;IACpHR,KAAK,EAAEC,GAAG,GAAGyC,IAAI,GAAGlC;EACtB,CAAC,CAAC,CAAC,GAAGkF,SAAS,IAAI,EAAE;EACrB,MAAMqC,WAAW,GAAGtC,KAAK,CAACmC,GAAG,CAACI,IAAI,IAAIA,IAAI,CAAChI,KAAK,CAAC;EACjD,MAAM;IACJiI,iBAAiB;IACjBC,MAAM,EAAEC,iBAAiB;IACzBC,OAAO,EAAEC,kBAAkB;IAC3BrC,GAAG,EAAEsC;EACP,CAAC,GAAG/I,iBAAiB,CAAC,CAAC;EACvB,MAAM,CAACgJ,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG7J,KAAK,CAAC2H,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpE,MAAMjD,SAAS,GAAG1E,KAAK,CAACyH,MAAM,CAAC,CAAC;EAChC,MAAMqC,cAAc,GAAGpJ,UAAU,CAACiJ,eAAe,EAAEjF,SAAS,CAAC;EAC7D,MAAMqF,SAAS,GAAGrJ,UAAU,CAAC2G,GAAG,EAAEyC,cAAc,CAAC;EACjD,MAAME,4BAA4B,GAAGC,aAAa,IAAI7H,KAAK,IAAI;IAC7D,IAAI8H,qBAAqB;IACzB,MAAMrI,KAAK,GAAGqC,MAAM,CAAC9B,KAAK,CAAC+H,aAAa,CAACjF,YAAY,CAAC,YAAY,CAAC,CAAC;IACpEwE,kBAAkB,CAACtH,KAAK,CAAC;IACzB,IAAIkH,iBAAiB,CAAChH,OAAO,KAAK,IAAI,EAAE;MACtCuH,oBAAoB,CAAChI,KAAK,CAAC;IAC7B;IACAgG,OAAO,CAAChG,KAAK,CAAC;IACdoI,aAAa,IAAI,IAAI,IAAI,CAACC,qBAAqB,GAAGD,aAAa,CAACR,OAAO,KAAK,IAAI,IAAIS,qBAAqB,CAACE,IAAI,CAACH,aAAa,EAAE7H,KAAK,CAAC;EACtI,CAAC;EACD,MAAMiI,2BAA2B,GAAGJ,aAAa,IAAI7H,KAAK,IAAI;IAC5D,IAAIkI,qBAAqB;IACzBd,iBAAiB,CAACpH,KAAK,CAAC;IACxB,IAAIkH,iBAAiB,CAAChH,OAAO,KAAK,KAAK,EAAE;MACvCuH,oBAAoB,CAAC,CAAC,CAAC,CAAC;IAC1B;IACAhC,OAAO,CAAC,CAAC,CAAC,CAAC;IACXoC,aAAa,IAAI,IAAI,IAAI,CAACK,qBAAqB,GAAGL,aAAa,CAACV,MAAM,KAAK,IAAI,IAAIe,qBAAqB,CAACF,IAAI,CAACH,aAAa,EAAE7H,KAAK,CAAC;EACrI,CAAC;EACD9B,iBAAiB,CAAC,MAAM;IACtB,IAAIqG,QAAQ,IAAIjC,SAAS,CAACpC,OAAO,CAAC0C,QAAQ,CAACuF,QAAQ,CAACtF,aAAa,CAAC,EAAE;MAClE,IAAIuF,qBAAqB;MACzB;MACA;MACA;MACA;MACA,CAACA,qBAAqB,GAAGD,QAAQ,CAACtF,aAAa,KAAK,IAAI,IAAIuF,qBAAqB,CAACC,IAAI,CAAC,CAAC;IAC1F;EACF,CAAC,EAAE,CAAC9D,QAAQ,CAAC,CAAC;EACd,IAAIA,QAAQ,IAAIe,MAAM,KAAK,CAAC,CAAC,EAAE;IAC7B9C,SAAS,CAAC,CAAC,CAAC,CAAC;EACf;EACA,IAAI+B,QAAQ,IAAIiD,iBAAiB,KAAK,CAAC,CAAC,EAAE;IACxCC,oBAAoB,CAAC,CAAC,CAAC,CAAC;EAC1B;EACA,MAAMa,6BAA6B,GAAGT,aAAa,IAAI7H,KAAK,IAAI;IAC9D,IAAIuI,qBAAqB;IACzB,CAACA,qBAAqB,GAAGV,aAAa,CAAChD,QAAQ,KAAK,IAAI,IAAI0D,qBAAqB,CAACP,IAAI,CAACH,aAAa,EAAE7H,KAAK,CAAC;IAC5G,MAAMP,KAAK,GAAGqC,MAAM,CAAC9B,KAAK,CAAC+H,aAAa,CAACjF,YAAY,CAAC,YAAY,CAAC,CAAC;IACpE,MAAM7D,KAAK,GAAGK,MAAM,CAACG,KAAK,CAAC;IAC3B,MAAM+I,UAAU,GAAGxB,WAAW,CAACyB,OAAO,CAACxJ,KAAK,CAAC;;IAE7C;IACA,IAAIgD,QAAQ,GAAGjC,KAAK,CAAC0I,MAAM,CAACC,aAAa;IACzC,IAAIjE,KAAK,IAAI/C,IAAI,IAAI,IAAI,EAAE;MACzB,MAAMiH,aAAa,GAAG5B,WAAW,CAACA,WAAW,CAACzG,MAAM,GAAG,CAAC,CAAC;MACzD,IAAI0B,QAAQ,GAAG2G,aAAa,EAAE;QAC5B3G,QAAQ,GAAG2G,aAAa;MAC1B,CAAC,MAAM,IAAI3G,QAAQ,GAAG+E,WAAW,CAAC,CAAC,CAAC,EAAE;QACpC/E,QAAQ,GAAG+E,WAAW,CAAC,CAAC,CAAC;MAC3B,CAAC,MAAM;QACL/E,QAAQ,GAAGA,QAAQ,GAAGhD,KAAK,GAAG+H,WAAW,CAACwB,UAAU,GAAG,CAAC,CAAC,GAAGxB,WAAW,CAACwB,UAAU,GAAG,CAAC,CAAC;MACzF;IACF;IACAvG,QAAQ,GAAGjD,KAAK,CAACiD,QAAQ,EAAE/C,GAAG,EAAEC,GAAG,CAAC;IACpC,IAAIuH,KAAK,EAAE;MACT;MACA,IAAIlC,WAAW,EAAE;QACfvC,QAAQ,GAAGjD,KAAK,CAACiD,QAAQ,EAAE3C,MAAM,CAACG,KAAK,GAAG,CAAC,CAAC,IAAI,CAACoJ,QAAQ,EAAEvJ,MAAM,CAACG,KAAK,GAAG,CAAC,CAAC,IAAIoJ,QAAQ,CAAC;MAC3F;MACA,MAAMC,aAAa,GAAG7G,QAAQ;MAC9BA,QAAQ,GAAGD,aAAa,CAAC;QACvB1C,MAAM;QACN2C,QAAQ;QACRxC;MACF,CAAC,CAAC;MACF,IAAI8C,WAAW,GAAG9C,KAAK;;MAEvB;MACA,IAAI,CAAC+E,WAAW,EAAE;QAChBjC,WAAW,GAAGN,QAAQ,CAACwG,OAAO,CAACK,aAAa,CAAC;MAC/C;MACAzG,UAAU,CAAC;QACTC,SAAS;QACTC;MACF,CAAC,CAAC;IACJ;IACAuD,aAAa,CAAC7D,QAAQ,CAAC;IACvBwF,oBAAoB,CAAChI,KAAK,CAAC;IAC3B,IAAIwG,YAAY,IAAI,CAAC/C,cAAc,CAACjB,QAAQ,EAAE4D,YAAY,CAAC,EAAE;MAC3DI,YAAY,CAACjG,KAAK,EAAEiC,QAAQ,EAAExC,KAAK,CAAC;IACtC;IACA,IAAIqF,iBAAiB,EAAE;MACrBA,iBAAiB,CAAC9E,KAAK,EAAEiC,QAAQ,CAAC;IACpC;EACF,CAAC;EACD,MAAM8G,aAAa,GAAGnL,KAAK,CAACyH,MAAM,CAAC,CAAC;EACpC,IAAI2D,IAAI,GAAGjE,WAAW;EACtB,IAAIN,KAAK,IAAIM,WAAW,KAAK,YAAY,EAAE;IACzCiE,IAAI,IAAI,UAAU;EACpB;EACA,MAAMC,iBAAiB,GAAGA,CAAC;IACzBC,MAAM;IACNC,IAAI,GAAG;EACT,CAAC,KAAK;IACJ,MAAM;MACJjJ,OAAO,EAAEkJ;IACX,CAAC,GAAG9G,SAAS;IACb,MAAM;MACJmB,KAAK;MACLI,MAAM;MACND,MAAM;MACNL;IACF,CAAC,GAAG6F,MAAM,CAACC,qBAAqB,CAAC,CAAC;IAClC,IAAIrI,OAAO;IACX,IAAIgI,IAAI,CAACP,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE;MAClCzH,OAAO,GAAG,CAAC4C,MAAM,GAAGsF,MAAM,CAACtI,CAAC,IAAIiD,MAAM;IACxC,CAAC,MAAM;MACL7C,OAAO,GAAG,CAACkI,MAAM,CAACxI,CAAC,GAAG6C,IAAI,IAAIE,KAAK;IACrC;IACA,IAAIuF,IAAI,CAACP,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE;MACnCzH,OAAO,GAAG,CAAC,GAAGA,OAAO;IACvB;IACA,IAAIiB,QAAQ;IACZA,QAAQ,GAAGlB,cAAc,CAACC,OAAO,EAAE9B,GAAG,EAAEC,GAAG,CAAC;IAC5C,IAAIwC,IAAI,EAAE;MACRM,QAAQ,GAAGP,gBAAgB,CAACO,QAAQ,EAAEN,IAAI,EAAEzC,GAAG,CAAC;IAClD,CAAC,MAAM;MACL,MAAMQ,YAAY,GAAGL,WAAW,CAAC2H,WAAW,EAAE/E,QAAQ,CAAC;MACvDA,QAAQ,GAAG+E,WAAW,CAACtH,YAAY,CAAC;IACtC;IACAuC,QAAQ,GAAGjD,KAAK,CAACiD,QAAQ,EAAE/C,GAAG,EAAEC,GAAG,CAAC;IACpC,IAAIoD,WAAW,GAAG,CAAC;IACnB,IAAImE,KAAK,EAAE;MACT,IAAI,CAACyC,IAAI,EAAE;QACT5G,WAAW,GAAGlD,WAAW,CAACC,MAAM,EAAE2C,QAAQ,CAAC;MAC7C,CAAC,MAAM;QACLM,WAAW,GAAGwG,aAAa,CAAC7I,OAAO;MACrC;;MAEA;MACA,IAAIsE,WAAW,EAAE;QACfvC,QAAQ,GAAGjD,KAAK,CAACiD,QAAQ,EAAE3C,MAAM,CAACiD,WAAW,GAAG,CAAC,CAAC,IAAI,CAACsG,QAAQ,EAAEvJ,MAAM,CAACiD,WAAW,GAAG,CAAC,CAAC,IAAIsG,QAAQ,CAAC;MACvG;MACA,MAAMC,aAAa,GAAG7G,QAAQ;MAC9BA,QAAQ,GAAGD,aAAa,CAAC;QACvB1C,MAAM;QACN2C,QAAQ;QACRxC,KAAK,EAAE8C;MACT,CAAC,CAAC;;MAEF;MACA,IAAI,EAAEiC,WAAW,IAAI2E,IAAI,CAAC,EAAE;QAC1B5G,WAAW,GAAGN,QAAQ,CAACwG,OAAO,CAACK,aAAa,CAAC;QAC7CC,aAAa,CAAC7I,OAAO,GAAGqC,WAAW;MACrC;IACF;IACA,OAAO;MACLN,QAAQ;MACRM;IACF,CAAC;EACH,CAAC;EACD,MAAM+G,eAAe,GAAGlL,gBAAgB,CAAC+H,WAAW,IAAI;IACtD,MAAM+C,MAAM,GAAGnJ,WAAW,CAACoG,WAAW,EAAElG,OAAO,CAAC;IAChD,IAAI,CAACiJ,MAAM,EAAE;MACX;IACF;IACAtD,SAAS,CAAC1F,OAAO,IAAI,CAAC;;IAEtB;IACA;IACA,IAAIiG,WAAW,CAACG,IAAI,KAAK,WAAW,IAAIH,WAAW,CAACoD,OAAO,KAAK,CAAC,EAAE;MACjE;MACAC,cAAc,CAACrD,WAAW,CAAC;MAC3B;IACF;IACA,MAAM;MACJlE,QAAQ;MACRM;IACF,CAAC,GAAG0G,iBAAiB,CAAC;MACpBC,MAAM;MACNC,IAAI,EAAE;IACR,CAAC,CAAC;IACF9G,UAAU,CAAC;MACTC,SAAS;MACTC,WAAW;MACXC;IACF,CAAC,CAAC;IACFsD,aAAa,CAAC7D,QAAQ,CAAC;IACvB,IAAI,CAACyD,QAAQ,IAAIE,SAAS,CAAC1F,OAAO,GAAGtB,gCAAgC,EAAE;MACrE+G,WAAW,CAAC,IAAI,CAAC;IACnB;IACA,IAAIM,YAAY,IAAI,CAAC/C,cAAc,CAACjB,QAAQ,EAAE4D,YAAY,CAAC,EAAE;MAC3DI,YAAY,CAACE,WAAW,EAAElE,QAAQ,EAAEM,WAAW,CAAC;IAClD;EACF,CAAC,CAAC;EACF,MAAMiH,cAAc,GAAGpL,gBAAgB,CAAC+H,WAAW,IAAI;IACrD,MAAM+C,MAAM,GAAGnJ,WAAW,CAACoG,WAAW,EAAElG,OAAO,CAAC;IAChD0F,WAAW,CAAC,KAAK,CAAC;IAClB,IAAI,CAACuD,MAAM,EAAE;MACX;IACF;IACA,MAAM;MACJjH;IACF,CAAC,GAAGgH,iBAAiB,CAAC;MACpBC,MAAM;MACNC,IAAI,EAAE;IACR,CAAC,CAAC;IACF3G,SAAS,CAAC,CAAC,CAAC,CAAC;IACb,IAAI2D,WAAW,CAACG,IAAI,KAAK,UAAU,EAAE;MACnCb,OAAO,CAAC,CAAC,CAAC,CAAC;IACb;IACA,IAAIX,iBAAiB,EAAE;MACrBA,iBAAiB,CAACqB,WAAW,EAAElE,QAAQ,CAAC;IAC1C;IACAhC,OAAO,CAACC,OAAO,GAAGC,SAAS;;IAE3B;IACAsJ,aAAa,CAAC,CAAC;EACjB,CAAC,CAAC;EACF,MAAMC,gBAAgB,GAAGtL,gBAAgB,CAAC+H,WAAW,IAAI;IACvD,IAAI5B,QAAQ,EAAE;MACZ;IACF;IACA;IACA,IAAI,CAACP,0BAA0B,CAAC,CAAC,EAAE;MACjCmC,WAAW,CAACwD,cAAc,CAAC,CAAC;IAC9B;IACA,MAAMnJ,KAAK,GAAG2F,WAAW,CAAC/F,cAAc,CAAC,CAAC,CAAC;IAC3C,IAAII,KAAK,IAAI,IAAI,EAAE;MACjB;MACAP,OAAO,CAACC,OAAO,GAAGM,KAAK,CAACC,UAAU;IACpC;IACA,MAAMyI,MAAM,GAAGnJ,WAAW,CAACoG,WAAW,EAAElG,OAAO,CAAC;IAChD,IAAIiJ,MAAM,KAAK,KAAK,EAAE;MACpB,MAAM;QACJjH,QAAQ;QACRM;MACF,CAAC,GAAG0G,iBAAiB,CAAC;QACpBC;MACF,CAAC,CAAC;MACF7G,UAAU,CAAC;QACTC,SAAS;QACTC,WAAW;QACXC;MACF,CAAC,CAAC;MACFsD,aAAa,CAAC7D,QAAQ,CAAC;MACvB,IAAIgE,YAAY,IAAI,CAAC/C,cAAc,CAACjB,QAAQ,EAAE4D,YAAY,CAAC,EAAE;QAC3DI,YAAY,CAACE,WAAW,EAAElE,QAAQ,EAAEM,WAAW,CAAC;MAClD;IACF;IACAqD,SAAS,CAAC1F,OAAO,GAAG,CAAC;IACrB,MAAMyC,GAAG,GAAG7E,aAAa,CAACwE,SAAS,CAACpC,OAAO,CAAC;IAC5CyC,GAAG,CAACiH,gBAAgB,CAAC,WAAW,EAAEN,eAAe,CAAC;IAClD3G,GAAG,CAACiH,gBAAgB,CAAC,UAAU,EAAEJ,cAAc,CAAC;EAClD,CAAC,CAAC;EACF,MAAMC,aAAa,GAAG7L,KAAK,CAACiM,WAAW,CAAC,MAAM;IAC5C,MAAMlH,GAAG,GAAG7E,aAAa,CAACwE,SAAS,CAACpC,OAAO,CAAC;IAC5CyC,GAAG,CAACmH,mBAAmB,CAAC,WAAW,EAAER,eAAe,CAAC;IACrD3G,GAAG,CAACmH,mBAAmB,CAAC,SAAS,EAAEN,cAAc,CAAC;IAClD7G,GAAG,CAACmH,mBAAmB,CAAC,WAAW,EAAER,eAAe,CAAC;IACrD3G,GAAG,CAACmH,mBAAmB,CAAC,UAAU,EAAEN,cAAc,CAAC;EACrD,CAAC,EAAE,CAACA,cAAc,EAAEF,eAAe,CAAC,CAAC;EACrC1L,KAAK,CAACmM,SAAS,CAAC,MAAM;IACpB,MAAM;MACJ7J,OAAO,EAAEkJ;IACX,CAAC,GAAG9G,SAAS;IACb8G,MAAM,CAACQ,gBAAgB,CAAC,YAAY,EAAEF,gBAAgB,EAAE;MACtDM,OAAO,EAAEhG,0BAA0B,CAAC;IACtC,CAAC,CAAC;IACF,OAAO,MAAM;MACX;MACAoF,MAAM,CAACU,mBAAmB,CAAC,YAAY,EAAEJ,gBAAgB,EAAE;QACzDM,OAAO,EAAEhG,0BAA0B,CAAC;MACtC,CAAC,CAAC;MACFyF,aAAa,CAAC,CAAC;IACjB,CAAC;EACH,CAAC,EAAE,CAACA,aAAa,EAAEC,gBAAgB,CAAC,CAAC;EACrC9L,KAAK,CAACmM,SAAS,CAAC,MAAM;IACpB,IAAIxF,QAAQ,EAAE;MACZkF,aAAa,CAAC,CAAC;IACjB;EACF,CAAC,EAAE,CAAClF,QAAQ,EAAEkF,aAAa,CAAC,CAAC;EAC7B,MAAMQ,qBAAqB,GAAGpC,aAAa,IAAI7H,KAAK,IAAI;IACtD,IAAIkK,qBAAqB;IACzB,CAACA,qBAAqB,GAAGrC,aAAa,CAACsC,WAAW,KAAK,IAAI,IAAID,qBAAqB,CAAClC,IAAI,CAACH,aAAa,EAAE7H,KAAK,CAAC;IAC/G,IAAIuE,QAAQ,EAAE;MACZ;IACF;IACA,IAAIvE,KAAK,CAACoK,gBAAgB,EAAE;MAC1B;IACF;;IAEA;IACA,IAAIpK,KAAK,CAACqK,MAAM,KAAK,CAAC,EAAE;MACtB;IACF;;IAEA;IACArK,KAAK,CAAC2J,cAAc,CAAC,CAAC;IACtB,MAAMT,MAAM,GAAGnJ,WAAW,CAACC,KAAK,EAAEC,OAAO,CAAC;IAC1C,IAAIiJ,MAAM,KAAK,KAAK,EAAE;MACpB,MAAM;QACJjH,QAAQ;QACRM;MACF,CAAC,GAAG0G,iBAAiB,CAAC;QACpBC;MACF,CAAC,CAAC;MACF7G,UAAU,CAAC;QACTC,SAAS;QACTC,WAAW;QACXC;MACF,CAAC,CAAC;MACFsD,aAAa,CAAC7D,QAAQ,CAAC;MACvB,IAAIgE,YAAY,IAAI,CAAC/C,cAAc,CAACjB,QAAQ,EAAE4D,YAAY,CAAC,EAAE;QAC3DI,YAAY,CAACjG,KAAK,EAAEiC,QAAQ,EAAEM,WAAW,CAAC;MAC5C;IACF;IACAqD,SAAS,CAAC1F,OAAO,GAAG,CAAC;IACrB,MAAMyC,GAAG,GAAG7E,aAAa,CAACwE,SAAS,CAACpC,OAAO,CAAC;IAC5CyC,GAAG,CAACiH,gBAAgB,CAAC,WAAW,EAAEN,eAAe,CAAC;IAClD3G,GAAG,CAACiH,gBAAgB,CAAC,SAAS,EAAEJ,cAAc,CAAC;EACjD,CAAC;EACD,MAAMc,WAAW,GAAGxJ,cAAc,CAAC4F,KAAK,GAAGpH,MAAM,CAAC,CAAC,CAAC,GAAGJ,GAAG,EAAEA,GAAG,EAAEC,GAAG,CAAC;EACrE,MAAMoL,SAAS,GAAGzJ,cAAc,CAACxB,MAAM,CAACA,MAAM,CAACiB,MAAM,GAAG,CAAC,CAAC,EAAErB,GAAG,EAAEC,GAAG,CAAC,GAAGmL,WAAW;EACnF,MAAME,YAAY,GAAGA,CAACC,aAAa,GAAG,CAAC,CAAC,KAAK;IAC3C,MAAMC,gBAAgB,GAAG/L,oBAAoB,CAAC8L,aAAa,CAAC;IAC5D,MAAME,gBAAgB,GAAG;MACvBR,WAAW,EAAEF,qBAAqB,CAACS,gBAAgB,IAAI,CAAC,CAAC;IAC3D,CAAC;IACD,MAAME,mBAAmB,GAAGjN,QAAQ,CAAC,CAAC,CAAC,EAAE+M,gBAAgB,EAAEC,gBAAgB,CAAC;IAC5E,OAAOhN,QAAQ,CAAC,CAAC,CAAC,EAAE8M,aAAa,EAAE;MACjCxF,GAAG,EAAE0C;IACP,CAAC,EAAEiD,mBAAmB,CAAC;EACzB,CAAC;EACD,MAAMC,qBAAqB,GAAGhD,aAAa,IAAI7H,KAAK,IAAI;IACtD,IAAI8K,sBAAsB;IAC1B,CAACA,sBAAsB,GAAGjD,aAAa,CAACkD,WAAW,KAAK,IAAI,IAAID,sBAAsB,CAAC9C,IAAI,CAACH,aAAa,EAAE7H,KAAK,CAAC;IACjH,MAAMP,KAAK,GAAGqC,MAAM,CAAC9B,KAAK,CAAC+H,aAAa,CAACjF,YAAY,CAAC,YAAY,CAAC,CAAC;IACpE2C,OAAO,CAAChG,KAAK,CAAC;EAChB,CAAC;EACD,MAAMuL,sBAAsB,GAAGnD,aAAa,IAAI7H,KAAK,IAAI;IACvD,IAAIiL,sBAAsB;IAC1B,CAACA,sBAAsB,GAAGpD,aAAa,CAACqD,YAAY,KAAK,IAAI,IAAID,sBAAsB,CAACjD,IAAI,CAACH,aAAa,EAAE7H,KAAK,CAAC;IAClHyF,OAAO,CAAC,CAAC,CAAC,CAAC;EACb,CAAC;EACD,MAAM0F,aAAa,GAAGA,CAACV,aAAa,GAAG,CAAC,CAAC,KAAK;IAC5C,MAAMC,gBAAgB,GAAG/L,oBAAoB,CAAC8L,aAAa,CAAC;IAC5D,MAAME,gBAAgB,GAAG;MACvBI,WAAW,EAAEF,qBAAqB,CAACH,gBAAgB,IAAI,CAAC,CAAC,CAAC;MAC1DQ,YAAY,EAAEF,sBAAsB,CAACN,gBAAgB,IAAI,CAAC,CAAC;IAC7D,CAAC;IACD,OAAO/M,QAAQ,CAAC,CAAC,CAAC,EAAE8M,aAAa,EAAEC,gBAAgB,EAAEC,gBAAgB,CAAC;EACxE,CAAC;EACD,MAAMS,aAAa,GAAG3L,KAAK,IAAI;IAC7B,OAAO;MACL;MACA4L,aAAa,EAAE/F,MAAM,KAAK,CAAC,CAAC,IAAIA,MAAM,KAAK7F,KAAK,GAAG,MAAM,GAAGU;IAC9D,CAAC;EACH,CAAC;EACD,MAAMmL,mBAAmB,GAAGA,CAACb,aAAa,GAAG,CAAC,CAAC,KAAK;IAClD,IAAIc,gBAAgB;IACpB,MAAMb,gBAAgB,GAAG/L,oBAAoB,CAAC8L,aAAa,CAAC;IAC5D,MAAME,gBAAgB,GAAG;MACvB9F,QAAQ,EAAEyD,6BAA6B,CAACoC,gBAAgB,IAAI,CAAC,CAAC,CAAC;MAC/DrD,OAAO,EAAEO,4BAA4B,CAAC8C,gBAAgB,IAAI,CAAC,CAAC,CAAC;MAC7DvD,MAAM,EAAEc,2BAA2B,CAACyC,gBAAgB,IAAI,CAAC,CAAC;IAC5D,CAAC;IACD,MAAME,mBAAmB,GAAGjN,QAAQ,CAAC,CAAC,CAAC,EAAE+M,gBAAgB,EAAEC,gBAAgB,CAAC;IAC5E,OAAOhN,QAAQ,CAAC;MACdwH,QAAQ;MACR,iBAAiB,EAAEd,cAAc;MACjC,kBAAkB,EAAEU,WAAW;MAC/B,eAAe,EAAEG,KAAK,CAAC/F,GAAG,CAAC;MAC3B,eAAe,EAAE+F,KAAK,CAAChG,GAAG,CAAC;MAC3B0F,IAAI;MACJ0B,IAAI,EAAE,OAAO;MACbpH,GAAG,EAAEkF,UAAU,CAAClF,GAAG;MACnBC,GAAG,EAAEiF,UAAU,CAACjF,GAAG;MACnBwC,IAAI,EAAEyC,UAAU,CAACzC,IAAI,KAAK,IAAI,IAAIyC,UAAU,CAACM,KAAK,GAAG,KAAK,GAAG,CAAC6G,gBAAgB,GAAGnH,UAAU,CAACzC,IAAI,KAAK,IAAI,GAAG4J,gBAAgB,GAAGpL,SAAS;MACxIoE;IACF,CAAC,EAAEkG,aAAa,EAAEG,mBAAmB,EAAE;MACrCY,KAAK,EAAE7N,QAAQ,CAAC,CAAC,CAAC,EAAEc,cAAc,EAAE;QAClCgN,SAAS,EAAEhH,KAAK,GAAG,KAAK,GAAG,KAAK;QAChC;QACAhB,KAAK,EAAE,MAAM;QACbI,MAAM,EAAE;MACV,CAAC;IACH,CAAC,CAAC;EACJ,CAAC;EACD,OAAO;IACLyB,MAAM;IACN0D,IAAI,EAAEA,IAAI;IACV5F,SAAS;IACTsC,QAAQ;IACR8B,iBAAiB;IACjB8D,mBAAmB;IACnBd,YAAY;IACZW,aAAa;IACbzG,KAAK,EAAEA,KAAK;IACZc,IAAI;IACJkB,KAAK;IACL1B,OAAO,EAAE2C,SAAS;IAClB4C,SAAS;IACTD,WAAW;IACXhL,MAAM;IACN8L;EACF,CAAC;AACH"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}