[{"/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/src/index.js": "1", "/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/src/App.js": "2", "/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/src/integration-form.js": "3", "/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/src/data-form.js": "4", "/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/src/integrations/hubspot.js": "5", "/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/src/integrations/notion.js": "6", "/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/src/integrations/airtable.js": "7"}, {"size": 254, "mtime": 1758291796920, "results": "8", "hashOfConfig": "9"}, {"size": 158, "mtime": 1758291796935, "results": "10", "hashOfConfig": "9"}, {"size": 2031, "mtime": 1758292902727, "results": "11", "hashOfConfig": "9"}, {"size": 2390, "mtime": 1758353592812, "results": "12", "hashOfConfig": "9"}, {"size": 3121, "mtime": 1758353452089, "results": "13", "hashOfConfig": "9"}, {"size": 3148, "mtime": 1758291796954, "results": "14", "hashOfConfig": "9"}, {"size": 3129, "mtime": 1758291796952, "results": "15", "hashOfConfig": "9"}, {"filePath": "16", "messages": "17", "suppressedMessages": "18", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "bq1t8u", {"filePath": "19", "messages": "20", "suppressedMessages": "21", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/src/index.js", [], [], "/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/src/App.js", [], [], "/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/src/integration-form.js", [], [], "/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/src/data-form.js", [], [], "/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/src/integrations/hubspot.js", ["37"], [], "/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/src/integrations/notion.js", ["38"], [], "/home/<USER>/PROJECTS/automations_technical_assessment/integrations_technical_assessment/frontend/src/integrations/airtable.js", ["39"], [], {"ruleId": "40", "severity": 1, "message": "41", "line": 62, "column": 8, "nodeType": "42", "endLine": 62, "endColumn": 10, "suggestions": "43"}, {"ruleId": "40", "severity": 1, "message": "41", "line": 63, "column": 8, "nodeType": "42", "endLine": 63, "endColumn": 10, "suggestions": "44"}, {"ruleId": "40", "severity": 1, "message": "41", "line": 62, "column": 8, "nodeType": "42", "endLine": 62, "endColumn": 10, "suggestions": "45"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'integrationParams?.credentials'. Either include it or remove the dependency array. If 'setIsConnected' needs the current value of 'integrationParams.credentials', you can also switch to useReducer instead of useState and read 'integrationParams.credentials' in the reducer.", "ArrayExpression", ["46"], ["47"], ["48"], {"desc": "49", "fix": "50"}, {"desc": "49", "fix": "51"}, {"desc": "49", "fix": "52"}, "Update the dependencies array to be: [integrationParams?.credentials]", {"range": "53", "text": "54"}, {"range": "55", "text": "54"}, {"range": "56", "text": "54"}, [2228, 2230], "[integrationParams?.credentials]", [2257, 2259], [2234, 2236]]