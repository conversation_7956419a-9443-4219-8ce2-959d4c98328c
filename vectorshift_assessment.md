# VectorShift Integrations Technical Assessment

## Overview

Thank you for taking the time to interview with us at VectorShift! This technical assessment focuses on building integrations, specifically implementing a HubSpot OAuth integration and data retrieval functionality.

## Prerequisites

- **Frontend**: JavaScript/React
- **Backend**: Python/FastAPI
- **Database**: Redis
- **Files Location**: `/frontend/src` and `/backend` folders

## Setup Instructions

### Frontend Setup
```bash
cd /frontend
npm i
npm run start
```

### Backend Setup
```bash
cd /backend
uvicorn main:app --reload
```

### Redis Setup
```bash
redis-server
```

## Assessment Structure

The assessment consists of **two main parts** that build upon each other. Please read both parts before starting to plan your approach effectively.

---

## Part 1: HubSpot OAuth Integration

### Backend Implementation

**Location**: `/backend/integrations/hubspot.py`

**Reference Files**: 
- `airtable.py` (completed)
- `notion.py` (completed)

**Functions to Implement**:

1. **`authorize_hubspot()`**
   - Initialize OAuth authorization flow
   - Redirect users to HubSpot authorization page
   - Follow the pattern established in existing integrations

2. **`oauth2callback_hubspot()`**
   - Handle OAuth callback from HubSpot
   - Exchange authorization code for access tokens
   - Store credentials securely

3. **`get_hubspot_credentials()`**
   - Retrieve stored HubSpot credentials
   - Handle token refresh if necessary
   - Return valid authentication credentials

### Frontend Implementation

**Location**: `/frontend/src/integrations/hubspot.js`

**Reference Files**:
- `airtable.js`
- `notion.js`

**Tasks**:
1. Complete the empty `hubspot.js` file
2. Add HubSpot integration to relevant UI components
3. Ensure the integration is accessible through the user interface
4. Follow existing integration patterns

### Testing Setup

**Important**: Create your own HubSpot app credentials for testing:
1. Go to [HubSpot Developer Portal](https://developers.hubspot.com/)
2. Create a new app
3. Configure OAuth settings
4. Get Client ID and Client Secret
5. Optional: Create Notion and AirTable credentials for complete testing

---

## Part 2: Loading HubSpot Items

### Backend Implementation

**Location**: `/backend/integrations/hubspot.py`

**Function to Implement**: `get_items_hubspot()`

**Requirements**:
1. Use credentials from Part 1's OAuth flow
2. Query HubSpot's API endpoints
3. Return a list of `IntegrationItem` objects
4. Decide which HubSpot fields/endpoints are most important

**IntegrationItem Structure**:
- Contains various parameters that may/may not be filled
- Reference existing Notion and AirTable implementations
- Adapt structure to HubSpot's data model

### Key Considerations

**HubSpot API Endpoints** (examples to consider):
- Contacts
- Companies  
- Deals
- Tickets
- Products
- Custom Objects

**Data Mapping**:
- Map HubSpot properties to IntegrationItem parameters
- Handle different data types appropriately
- Consider pagination for large datasets

### Output Requirements

**Suggested Approach**: Print the final list of Integration Items to the console

**Alternative Approaches**:
- Display in UI
- Save to file
- Return as API response

---

## Technical Guidelines

### Code Quality
- Follow existing code patterns and structure
- Maintain consistency with Notion and AirTable integrations
- Include proper error handling
- Add meaningful comments where necessary

### Security Considerations
- Store credentials securely
- Handle token refresh properly
- Validate OAuth responses
- Protect against common security vulnerabilities

### API Integration Best Practices
- Implement proper rate limiting
- Handle API errors gracefully
- Use appropriate HTTP methods
- Follow HubSpot's API documentation

---

## Resources

### Documentation Links
- [HubSpot API Documentation](https://developers.hubspot.com/docs/api/overview)
- [HubSpot OAuth Guide](https://developers.hubspot.com/docs/api/oauth-quickstart-guide)
- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [React Documentation](https://reactjs.org/docs/)

### File Structure Reference
```
/backend/
  └── integrations/
      ├── airtable.py    (✅ completed)
      ├── notion.py      (✅ completed)
      └── hubspot.py     (🔧 to implement)

/frontend/src/
  └── integrations/
      ├── airtable.js    (✅ completed)
      ├── notion.js      (✅ completed)
      └── hubspot.js     (🔧 to implement)
```

---

## Support

If you have any questions during the assessment, please reach out to:
**<EMAIL>**

---

## Assessment Checklist

### Part 1 Completion
- [ ] `authorize_hubspot()` function implemented
- [ ] `oauth2callback_hubspot()` function implemented  
- [ ] `get_hubspot_credentials()` function implemented
- [ ] `hubspot.js` frontend file completed
- [ ] HubSpot integration added to UI
- [ ] HubSpot app credentials created and configured
- [ ] OAuth flow tested end-to-end

### Part 2 Completion
- [ ] `get_items_hubspot()` function implemented
- [ ] HubSpot API endpoints integrated
- [ ] IntegrationItem objects properly structured
- [ ] Data retrieval tested with valid credentials
- [ ] Results displayed/logged as requested
- [ ] Error handling implemented

### Final Review
- [ ] Code follows existing patterns
- [ ] All functions are properly documented
- [ ] Security best practices followed
- [ ] Integration works end-to-end
- [ ] Ready for submission

---

**Good luck with your assessment! We look forward to reviewing your implementation.**